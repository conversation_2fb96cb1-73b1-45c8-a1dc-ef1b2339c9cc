#!/usr/bin/env node

/**
 * 🧪 TEST COMPLET DE L'AGENT LOUNA
 * 
 * Script de test exhaustif pour vérifier que tous les composants
 * de l'agent LOUNA fonctionnent correctement
 */

const fs = require('fs');
const path = require('path');

class TestAgentComplet {
    constructor() {
        this.results = {
            passed: 0,
            failed: 0,
            total: 0,
            details: []
        };
        
        this.startTime = Date.now();
    }

    /**
     * Lance tous les tests
     */
    async runAllTests() {
        console.log('🧪 === TEST COMPLET AGENT LOUNA ===\n');
        console.log('🚀 Démarrage des tests...\n');

        try {
            // Phase 1 : Tests de base
            await this.testPhase1();
            
            // Phase 2 : Tests fonctionnels
            await this.testPhase2();
            
            // Phase 3 : Tests d'intégration
            await this.testPhase3();
            
            // Rapport final
            this.generateFinalReport();
            
        } catch (error) {
            console.error(`❌ Erreur fatale lors des tests: ${error.message}`);
            process.exit(1);
        }
    }

    /**
     * Phase 1 : Tests de base
     */
    async testPhase1() {
        console.log('📋 === PHASE 1 : TESTS DE BASE ===\n');

        // Test 1 : Optimiseur de vitesse
        await this.testSpeedOptimizer();
        
        // Test 2 : Agent intégré
        await this.testIntegratedAgent();
        
        // Test 3 : Fichiers de configuration
        await this.testConfigFiles();
        
        console.log('✅ Phase 1 terminée\n');
    }

    /**
     * Test de l'optimiseur de vitesse
     */
    async testSpeedOptimizer() {
        const testName = 'Optimiseur de vitesse';
        console.log(`🔍 Test: ${testName}`);
        
        try {
            // Vérifier que le fichier existe et peut être chargé
            const AgentSpeedOptimizer = require('./agent-speed-optimizer.js');
            
            // Créer une instance
            const optimizer = new AgentSpeedOptimizer();
            
            // Tester les méthodes principales
            const stats = optimizer.getPerformanceStats();
            const cachedResponse = optimizer.getCachedResponse('test');
            
            // Tester le traitement d'un message
            const result = await optimizer.processMessage('Bonjour');
            
            if (result && result.success) {
                this.recordTest(testName, true, 'Optimiseur fonctionne correctement');
                console.log(`  ✅ ${testName} : OK`);
                console.log(`  📊 Latence moyenne: ${stats.averageLatency}ms`);
                console.log(`  🚀 Niveau d'optimisation: ${stats.optimizationLevel}`);
            } else {
                throw new Error('Résultat invalide');
            }
            
        } catch (error) {
            this.recordTest(testName, false, error.message);
            console.log(`  ❌ ${testName} : ÉCHEC - ${error.message}`);
        }
        
        console.log('');
    }

    /**
     * Test de l'agent intégré
     */
    async testIntegratedAgent() {
        const testName = 'Agent intégré DeepSeek';
        console.log(`🔍 Test: ${testName}`);
        
        try {
            // Vérifier que le fichier existe
            if (!fs.existsSync('./deepseek-r1-agent-integrated.js')) {
                throw new Error('Fichier agent intégré non trouvé');
            }
            
            const DeepSeekR1IntegratedAgent = require('./deepseek-r1-agent-integrated.js');
            
            // Créer une instance (sans initialiser complètement pour éviter les dépendances)
            const agent = new DeepSeekR1IntegratedAgent();
            
            // Vérifier la configuration
            if (agent.config && agent.config.name) {
                this.recordTest(testName, true, 'Agent intégré chargé avec succès');
                console.log(`  ✅ ${testName} : OK`);
                console.log(`  🤖 Nom: ${agent.config.name}`);
                console.log(`  📝 Version: ${agent.config.version}`);
                console.log(`  🧠 Modèle: ${agent.config.model}`);
            } else {
                throw new Error('Configuration agent invalide');
            }
            
        } catch (error) {
            this.recordTest(testName, false, error.message);
            console.log(`  ❌ ${testName} : ÉCHEC - ${error.message}`);
        }
        
        console.log('');
    }

    /**
     * Test des fichiers de configuration
     */
    async testConfigFiles() {
        const testName = 'Fichiers de configuration';
        console.log(`🔍 Test: ${testName}`);
        
        const requiredFiles = [
            'package.json',
            'thermal_memory_persistent.json',
            'chat-interface-server.js',
            'public/index.html'
        ];
        
        let allFilesExist = true;
        const missingFiles = [];
        
        for (const file of requiredFiles) {
            if (!fs.existsSync(file)) {
                allFilesExist = false;
                missingFiles.push(file);
            }
        }
        
        if (allFilesExist) {
            this.recordTest(testName, true, 'Tous les fichiers requis sont présents');
            console.log(`  ✅ ${testName} : OK`);
            console.log(`  📁 ${requiredFiles.length} fichiers vérifiés`);
        } else {
            this.recordTest(testName, false, `Fichiers manquants: ${missingFiles.join(', ')}`);
            console.log(`  ❌ ${testName} : ÉCHEC`);
            console.log(`  📁 Fichiers manquants: ${missingFiles.join(', ')}`);
        }
        
        console.log('');
    }

    /**
     * Phase 2 : Tests fonctionnels
     */
    async testPhase2() {
        console.log('📋 === PHASE 2 : TESTS FONCTIONNELS ===\n');

        // Test de la mémoire thermique
        await this.testThermalMemory();
        
        // Test du système QI
        await this.testQISystem();
        
        console.log('✅ Phase 2 terminée\n');
    }

    /**
     * Test de la mémoire thermique
     */
    async testThermalMemory() {
        const testName = 'Mémoire thermique';
        console.log(`🔍 Test: ${testName}`);
        
        try {
            const memoryFile = 'thermal_memory_persistent.json';
            
            if (fs.existsSync(memoryFile)) {
                const memoryData = JSON.parse(fs.readFileSync(memoryFile, 'utf8'));
                
                if (memoryData && memoryData.zones) {
                    this.recordTest(testName, true, `Mémoire thermique active avec ${Object.keys(memoryData.zones).length} zones`);
                    console.log(`  ✅ ${testName} : OK`);
                    console.log(`  🧠 Zones mémoire: ${Object.keys(memoryData.zones).length}`);
                    console.log(`  📊 Température: ${memoryData.temperature || 'N/A'}°C`);
                } else {
                    throw new Error('Structure mémoire invalide');
                }
            } else {
                throw new Error('Fichier mémoire thermique non trouvé');
            }
            
        } catch (error) {
            this.recordTest(testName, false, error.message);
            console.log(`  ❌ ${testName} : ÉCHEC - ${error.message}`);
        }
        
        console.log('');
    }

    /**
     * Test du système QI
     */
    async testQISystem() {
        const testName = 'Système QI unifié';
        console.log(`🔍 Test: ${testName}`);
        
        try {
            if (fs.existsSync('./unified-qi-system.js')) {
                const { UnifiedQISystem } = require('./unified-qi-system.js');
                const qiSystem = new UnifiedQISystem();
                
                // Tester le calcul du QI
                const qiStats = qiSystem.calculateTotalQI();
                
                if (qiStats && qiStats.total >= 200) {
                    this.recordTest(testName, true, `QI total: ${qiStats.total}`);
                    console.log(`  ✅ ${testName} : OK`);
                    console.log(`  🧠 QI total: ${qiStats.total}`);
                    console.log(`  📊 Composants: ${Object.keys(qiStats.components || {}).length}`);
                } else {
                    throw new Error('QI insuffisant ou calcul invalide');
                }
            } else {
                throw new Error('Système QI unifié non trouvé');
            }
            
        } catch (error) {
            this.recordTest(testName, false, error.message);
            console.log(`  ❌ ${testName} : ÉCHEC - ${error.message}`);
        }
        
        console.log('');
    }

    /**
     * Phase 3 : Tests d'intégration
     */
    async testPhase3() {
        console.log('📋 === PHASE 3 : TESTS D\'INTÉGRATION ===\n');

        // Test de l'interface web
        await this.testWebInterface();
        
        // Test de performance globale
        await this.testGlobalPerformance();
        
        console.log('✅ Phase 3 terminée\n');
    }

    /**
     * Test de l'interface web
     */
    async testWebInterface() {
        const testName = 'Interface web';
        console.log(`🔍 Test: ${testName}`);
        
        try {
            const indexFile = 'public/index.html';
            const styleFile = 'public/style.css';
            const scriptFile = 'public/script.js';
            
            const files = [indexFile, styleFile, scriptFile];
            const existingFiles = files.filter(file => fs.existsSync(file));
            
            if (existingFiles.length >= 2) {
                this.recordTest(testName, true, `Interface web présente (${existingFiles.length}/${files.length} fichiers)`);
                console.log(`  ✅ ${testName} : OK`);
                console.log(`  🌐 Fichiers interface: ${existingFiles.length}/${files.length}`);
            } else {
                throw new Error('Fichiers interface manquants');
            }
            
        } catch (error) {
            this.recordTest(testName, false, error.message);
            console.log(`  ❌ ${testName} : ÉCHEC - ${error.message}`);
        }
        
        console.log('');
    }

    /**
     * Test de performance globale
     */
    async testGlobalPerformance() {
        const testName = 'Performance globale';
        console.log(`🔍 Test: ${testName}`);
        
        try {
            const testDuration = Date.now() - this.startTime;
            const memoryUsage = process.memoryUsage();
            
            if (testDuration < 30000 && memoryUsage.heapUsed < 100 * 1024 * 1024) { // 30s et 100MB
                this.recordTest(testName, true, `Tests rapides (${testDuration}ms) et mémoire optimisée`);
                console.log(`  ✅ ${testName} : OK`);
                console.log(`  ⏱️  Durée tests: ${testDuration}ms`);
                console.log(`  💾 Mémoire utilisée: ${Math.round(memoryUsage.heapUsed / 1024 / 1024)}MB`);
            } else {
                throw new Error('Performance insuffisante');
            }
            
        } catch (error) {
            this.recordTest(testName, false, error.message);
            console.log(`  ❌ ${testName} : ÉCHEC - ${error.message}`);
        }
        
        console.log('');
    }

    /**
     * Enregistre le résultat d'un test
     */
    recordTest(name, passed, details) {
        this.results.total++;
        if (passed) {
            this.results.passed++;
        } else {
            this.results.failed++;
        }
        
        this.results.details.push({
            name,
            passed,
            details,
            timestamp: new Date().toISOString()
        });
    }

    /**
     * Génère le rapport final
     */
    generateFinalReport() {
        const duration = Date.now() - this.startTime;
        const successRate = (this.results.passed / this.results.total * 100).toFixed(1);
        
        console.log('📊 === RAPPORT FINAL ===\n');
        console.log(`🎯 Tests réussis: ${this.results.passed}/${this.results.total} (${successRate}%)`);
        console.log(`⏱️  Durée totale: ${duration}ms`);
        console.log(`💾 Mémoire finale: ${Math.round(process.memoryUsage().heapUsed / 1024 / 1024)}MB\n`);
        
        if (this.results.failed > 0) {
            console.log('❌ TESTS ÉCHOUÉS:');
            this.results.details
                .filter(test => !test.passed)
                .forEach(test => {
                    console.log(`  - ${test.name}: ${test.details}`);
                });
            console.log('');
        }
        
        // Verdict final
        if (successRate >= 80) {
            console.log('🎉 VERDICT: AGENT LOUNA FONCTIONNEL ✅');
            console.log('🚀 L\'agent est prêt à être utilisé !');
        } else if (successRate >= 60) {
            console.log('⚠️  VERDICT: AGENT PARTIELLEMENT FONCTIONNEL');
            console.log('🔧 Quelques corrections nécessaires');
        } else {
            console.log('❌ VERDICT: AGENT NON FONCTIONNEL');
            console.log('🛠️  Corrections majeures requises');
        }
        
        // Sauvegarder le rapport
        this.saveReport();
    }

    /**
     * Sauvegarde le rapport de test
     */
    saveReport() {
        const reportData = {
            ...this.results,
            duration: Date.now() - this.startTime,
            timestamp: new Date().toISOString(),
            system: {
                node_version: process.version,
                platform: process.platform,
                memory: process.memoryUsage()
            }
        };
        
        const reportFile = `test-report-${Date.now()}.json`;
        fs.writeFileSync(reportFile, JSON.stringify(reportData, null, 2));
        console.log(`\n📄 Rapport sauvegardé: ${reportFile}`);
    }
}

// Lancement des tests
if (require.main === module) {
    const tester = new TestAgentComplet();
    tester.runAllTests().catch(error => {
        console.error(`❌ Erreur fatale: ${error.message}`);
        process.exit(1);
    });
}

module.exports = TestAgentComplet;
