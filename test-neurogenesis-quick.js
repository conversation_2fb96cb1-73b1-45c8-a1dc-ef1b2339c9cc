#!/usr/bin/env node

/**
 * 🧪 TEST RAPIDE NEUROGENÈSE
 * 
 * Test rapide du système de création de neurones
 * avec simulation de temps écoulé
 */

const AdvancedBrainSystem = require('./advanced-brain-system');

async function testNeurogenesisQuick() {
    console.log('🧪 === TEST RAPIDE NEUROGENÈSE ===\n');
    
    try {
        // Initialiser le système cérébral
        console.log('🧠 Initialisation du système cérébral...');
        const brainSystem = new AdvancedBrainSystem();
        
        const success = await brainSystem.initialize();
        if (!success) {
            throw new Error('Échec initialisation système cérébral');
        }
        
        console.log('✅ Système cérébral initialisé\n');
        
        // État initial
        console.log('📊 === ÉTAT INITIAL ===');
        let stats = brainSystem.getNeurogenesisStats();
        
        console.log(`🧠 Neurones totaux: ${stats.total_neurons.toLocaleString()}`);
        console.log(`💾 Neurones stockés: ${stats.stored_neurons}`);
        console.log(`🌱 Taux neurogenèse: ${stats.neurogenesis_rate?.toFixed(6) || 0}/seconde`);
        
        // Forcer la neurogenèse en simulant du temps écoulé
        console.log('\n🔧 === SIMULATION TEMPS ÉCOULÉ ===');
        
        // Modifier last_neurogenesis pour simuler 2 heures écoulées
        const neural = brainSystem.brainData.neural_system;
        const twoHoursAgo = Date.now() - (2 * 60 * 60 * 1000); // 2 heures
        neural.last_neurogenesis = twoHoursAgo;
        
        console.log('⏰ Simulation: 2 heures écoulées depuis dernière neurogenèse');
        
        // Calculer combien de neurones devraient être créés
        const humanRate = 700 / 86400; // 0.008/seconde
        const timeElapsed = 2 * 60 * 60; // 2 heures en secondes
        const expectedNeurons = Math.floor(humanRate * timeElapsed);
        
        console.log(`📊 Neurones attendus: ${expectedNeurons} (${humanRate.toFixed(6)}/s × ${timeElapsed}s)`);
        
        // Déclencher la neurogenèse
        console.log('\n🌱 === DÉCLENCHEMENT NEUROGENÈSE ===');
        brainSystem.updateNeuroplasticityFromTemperature();
        
        // Vérifier les résultats
        const newStats = brainSystem.getNeurogenesisStats();
        const neuronsCreated = newStats.total_neurons - stats.total_neurons;
        
        console.log(`✨ Neurones créés: ${neuronsCreated}`);
        console.log(`📦 Neurones stockés: ${newStats.stored_neurons}`);
        console.log(`📝 Logs création: ${newStats.creation_log_entries}`);
        
        // Afficher les nouveaux neurones
        if (newStats.newest_neurons && newStats.newest_neurons.length > 0) {
            console.log('\n🆕 === NOUVEAUX NEURONES ===');
            newStats.newest_neurons.forEach((neuron, i) => {
                console.log(`${i + 1}. ID: ${neuron.id}`);
                console.log(`   Type: ${neuron.type}`);
                console.log(`   Spécialisation: ${neuron.specialization}`);
                console.log(`   Force synaptique: ${neuron.synaptic_strength.toFixed(3)}`);
                console.log(`   Créé: ${new Date(neuron.creation_time).toLocaleTimeString()}`);
                console.log('');
            });
        }
        
        // Test de création manuelle
        console.log('🔧 === TEST CRÉATION MANUELLE ===');
        const temperature = brainSystem.calculateAverageTemperature();
        console.log(`🌡️ Température actuelle: ${temperature.toFixed(2)}°C`);
        
        // Créer 5 neurones manuellement
        brainSystem.createNewNeurons(5, temperature);
        
        const manualStats = brainSystem.getNeurogenesisStats();
        const manualNeuronsCreated = manualStats.total_neurons - newStats.total_neurons;
        
        console.log(`✨ Neurones créés manuellement: ${manualNeuronsCreated}`);
        console.log(`📦 Total neurones stockés: ${manualStats.stored_neurons}`);
        
        // Afficher les zones thermiques mises à jour
        console.log('\n🌡️ === ZONES THERMIQUES ===');
        const zones = brainSystem.brainData.thermal_zones;
        for (const [zoneName, zone] of Object.entries(zones)) {
            console.log(`${zoneName}:`);
            console.log(`  Température: ${zone.temperature?.toFixed(2) || 'N/A'}°C`);
            console.log(`  Neurones: ${zone.neuron_count || 0}`);
            console.log(`  Neurogenèse totale: ${zone.neurogenesis_total || 0}`);
        }
        
        // Validation finale
        console.log('\n✅ === VALIDATION SYSTÈME ===');
        
        const totalCreated = neuronsCreated + manualNeuronsCreated;
        
        if (totalCreated > 0) {
            console.log(`✅ Neurogenèse fonctionnelle: ${totalCreated} neurones créés`);
        } else {
            console.log(`❌ Neurogenèse non fonctionnelle`);
        }
        
        if (manualStats.stored_neurons > 0) {
            console.log(`✅ Stockage opérationnel: ${manualStats.stored_neurons} neurones`);
        } else {
            console.log(`❌ Stockage non fonctionnel`);
        }
        
        if (manualStats.creation_log_entries > 0) {
            console.log(`✅ Logs fonctionnels: ${manualStats.creation_log_entries} entrées`);
        } else {
            console.log(`❌ Logs non fonctionnels`);
        }
        
        // Test de compression si beaucoup de neurones
        if (manualStats.stored_neurons > 3) {
            console.log('\n🗜️ === TEST COMPRESSION ===');
            brainSystem.compressNeuronStorage();
            
            const compressedStats = brainSystem.getNeurogenesisStats();
            console.log(`📦 Neurones après compression: ${compressedStats.stored_neurons}`);
        }
        
        console.log('\n🎉 === RÉSULTAT FINAL ===');
        
        if (totalCreated > 0 && manualStats.stored_neurons > 0) {
            console.log('🎉 SYSTÈME DE NEUROGENÈSE FONCTIONNEL !');
            console.log('🌱 Création de neurones: ✅');
            console.log('💾 Stockage neurones: ✅');
            console.log('📝 Logs création: ✅');
            console.log('🌡️ Intégration zones: ✅');
            console.log('🔧 Création manuelle: ✅');
        } else {
            console.log('❌ Système de neurogenèse défaillant');
        }
        
        // Sauvegarder les changements
        await brainSystem.saveBrainData();
        console.log('💾 Données sauvegardées');
        
        // Arrêter le système
        brainSystem.shutdown();
        
    } catch (error) {
        console.error(`❌ Erreur test neurogenèse: ${error.message}`);
        console.error(error.stack);
        process.exit(1);
    }
}

// Lancer le test
if (require.main === module) {
    testNeurogenesisQuick();
}

module.exports = { testNeurogenesisQuick };
