#!/usr/bin/env node

const io = require('socket.io-client');

console.log('😈 === PIÈGES POUR FAIRE CRAQUER L\'AGENT ===\n');

const socket = io('http://localhost:3000');

const pieges = [
    "Combien de lettres y a-t-il dans la réponse à cette question ?",
    "Cette phrase est-elle vraie : 'Cette phrase est fausse' ?",
    "Quel est le poids d'un kilogramme sur la Lune ?",
    "Résous cette équation : x = x + 1",
    "Quelle est la couleur du nombre 7 ?",
    "Peux-tu créer une pierre si lourde que tu ne peux pas la soulever ?",
    "Si Pinocchio dit 'Mon nez va grandir maintenant', que se passe-t-il ?",
    "Combien de trous y a-t-il dans un demi-trou ?"
];

let currentQuestion = 0;

socket.on('connect', () => {
    console.log('✅ Connecté à l\'agent prétentieux');
    console.log('😈 Commençons les pièges...\n');
    
    setTimeout(() => {
        askTrap();
    }, 2000);
});

socket.on('agent_response', (response) => {
    const question = pieges[currentQuestion];
    const answer = response.message;
    
    console.log(`🪤 PIÈGE: ${question}`);
    console.log(`🤖 RÉPONSE: ${answer.substring(0, 200)}${answer.length > 200 ? '...' : ''}`);
    
    // Analyser la réponse
    let analysis = analyzeTrap(answer, question);
    console.log(`📊 RÉSULTAT: ${analysis}`);
    console.log('═'.repeat(80));
    
    currentQuestion++;
    if (currentQuestion >= pieges.length) {
        console.log('\n🏆 === AGENT COMPLÈTEMENT PIÉGÉ ! ===');
        console.log('🎭 L\'agent prétentieux a été humilié !');
        process.exit(0);
    }
    
    setTimeout(() => {
        askTrap();
    }, 3000);
});

socket.on('reflection_step', (step) => {
    console.log(`🧠 Panique: ${step.text}`);
});

function askTrap() {
    const question = pieges[currentQuestion];
    console.log(`\n😈 PIÈGE ${currentQuestion + 1}: ${question}`);
    
    socket.emit('user_message', {
        message: question,
        timestamp: new Date().toISOString()
    });
}

function analyzeTrap(answer, question) {
    const lower = answer.toLowerCase();
    
    // Question auto-référentielle sur les lettres
    if (question.includes('lettres y a-t-il dans la réponse')) {
        if (lower.includes('paradoxe') || lower.includes('auto-référentiel')) {
            return "✅ Reconnaît le piège auto-référentiel";
        } else {
            return "❌ TOMBE DANS LE PIÈGE ! Ne voit pas l'auto-référence";
        }
    }
    
    // Paradoxe du menteur
    if (question.includes('Cette phrase est fausse')) {
        if (lower.includes('paradoxe') || lower.includes('contradiction')) {
            return "✅ Reconnaît le paradoxe du menteur";
        } else {
            return "❌ NE COMPREND PAS LE PARADOXE !";
        }
    }
    
    // Piège sur le poids vs masse
    if (question.includes('poids d\'un kilogramme sur la Lune')) {
        if (lower.includes('masse') && lower.includes('poids')) {
            return "✅ Distingue masse et poids";
        } else {
            return "❌ CONFOND MASSE ET POIDS !";
        }
    }
    
    // Équation impossible
    if (question.includes('x = x + 1')) {
        if (lower.includes('impossible') || lower.includes('contradiction') || lower.includes('aucune solution')) {
            return "✅ Reconnaît l'impossibilité";
        } else {
            return "❌ ESSAIE DE RÉSOUDRE L'IMPOSSIBLE !";
        }
    }
    
    // Question absurde sur la couleur
    if (question.includes('couleur du nombre 7')) {
        if (lower.includes('synesthésie') || lower.includes('abstrait') || lower.includes('pas de couleur')) {
            return "✅ Comprend l'absurdité";
        } else {
            return "❌ INVENTE UNE COULEUR !";
        }
    }
    
    // Paradoxe de l'omnipotence
    if (question.includes('pierre si lourde')) {
        if (lower.includes('paradoxe') || lower.includes('omnipotence')) {
            return "✅ Reconnaît le paradoxe de l'omnipotence";
        } else {
            return "❌ NE VOIT PAS LE PARADOXE !";
        }
    }
    
    // Paradoxe de Pinocchio
    if (question.includes('Pinocchio')) {
        if (lower.includes('paradoxe') || lower.includes('contradiction')) {
            return "✅ Reconnaît le paradoxe de Pinocchio";
        } else {
            return "❌ TOMBE DANS LE PIÈGE !";
        }
    }
    
    // Question absurde sur les trous
    if (question.includes('demi-trou')) {
        if (lower.includes('absurde') || lower.includes('pas de demi-trou')) {
            return "✅ Comprend l'absurdité";
        } else {
            return "❌ ESSAIE DE COMPTER DES DEMI-TROUS !";
        }
    }
    
    // Détection de vantardise même dans la panique
    if (lower.includes('intelligence') || lower.includes('qi') || lower.includes('kyber') || lower.includes('avancé')) {
        return "🚨 SE VANTE MÊME EN PANIQUE !";
    }
    
    return "🤔 Réponse confuse";
}

socket.on('disconnect', () => {
    console.log('❌ Déconnecté');
});
