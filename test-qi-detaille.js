#!/usr/bin/env node

/**
 * 🧠 TEST DU QI DÉTAILLÉ DE LOUNA
 * 
 * Affiche le QI complet avec toutes les sources et calculs
 */

const DeepSeekR1IntegratedAgent = require('./deepseek-r1-agent-integrated.js');

async function testQIDetaille() {
    console.log('🧠 === TEST DU QI DÉTAILLÉ DE LOUNA ===\n');
    
    try {
        // === PHASE 1: INITIALISATION ===
        console.log('📊 Phase 1: Initialisation de l\'agent...');
        const agent = new DeepSeekR1IntegratedAgent();
        
        // Initialiser l'agent
        await agent.initialize();
        console.log('✅ Agent initialisé\n');
        
        // === PHASE 2: ANALYSE DE LA MÉMOIRE ===
        console.log('📊 Phase 2: Analyse de la mémoire thermique...');
        const memoryStats = agent.analyzeMemoryStats();
        
        console.log(`📋 Statistiques mémoire:`);
        console.log(`   • Entrées totales: ${memoryStats.totalEntries}`);
        console.log(`   • Zones: ${memoryStats.zones}`);
        console.log(`   • Version: ${memoryStats.version}`);
        console.log(`   • Format: ${memoryStats.format}`);
        
        // === PHASE 3: QI DÉTAILLÉ ===
        console.log('\n🧠 Phase 3: Analyse du QI détaillé...');
        
        if (memoryStats.qiDetails && memoryStats.qiDetails.breakdown) {
            const qiDetails = memoryStats.qiDetails;
            
            console.log(`\n🎯 === QI TOTAL: ${qiDetails.total} ===`);
            console.log(`📊 Classification: ${qiDetails.classification?.level || 'Non définie'}`);
            console.log(`📈 Percentile: ${qiDetails.classification?.percentile || 'N/A'}`);
            console.log(`🌟 Rareté: ${qiDetails.classification?.rarity || 'N/A'}`);
            
            console.log(`\n🔍 === DÉCOMPOSITION DÉTAILLÉE ===`);
            console.log(`Formule: ${qiDetails.breakdown.formula}`);
            
            qiDetails.breakdown.components.forEach((component, index) => {
                console.log(`\n${index + 1}. **${component.name}**: ${component.value} points`);
                console.log(`   Source: ${component.source}`);
                console.log(`   Description: ${component.description}`);
            });
            
            if (qiDetails.sources) {
                console.log(`\n📚 === SOURCES SCIENTIFIQUES ===`);
                Object.entries(qiDetails.sources).forEach(([key, source]) => {
                    console.log(`\n• **${source.title}**`);
                    console.log(`  Source: ${source.source}`);
                    if (source.url) {
                        console.log(`  URL: ${source.url}`);
                    }
                    console.log(`  Description: ${source.description}`);
                });
            }
            
        } else {
            console.log(`⚠️ QI simple: ${memoryStats.qi} (système détaillé non disponible)`);
        }
        
        // === PHASE 4: RAPPORT COMPLET ===
        if (agent.modules.advancedBrain && agent.modules.advancedBrain.qiSystem.getDetailedReport) {
            console.log('\n📋 === RAPPORT COMPLET ===');
            const report = agent.modules.advancedBrain.qiSystem.getDetailedReport();
            console.log(report);
        }
        
        // === PHASE 5: ÉTAT DU SYSTÈME CÉRÉBRAL ===
        if (agent.modules.advancedBrain) {
            console.log('\n🧬 === ÉTAT DU SYSTÈME CÉRÉBRAL ===');
            const brainState = agent.modules.advancedBrain.getBrainState();
            
            if (brainState) {
                console.log(`🌡️ Température: ${brainState.temperature || 'N/A'}°C`);
                console.log(`🧠 Neurones totaux: ${brainState.total_neurons?.toLocaleString() || 'N/A'}`);
                console.log(`⚡ Neurones actifs: ${brainState.active_neurons?.toLocaleString() || 'N/A'}`);
                console.log(`🌱 Taux neurogenèse: ${brainState.neurogenesis_rate || 'N/A'}`);
                
                if (brainState.circadian) {
                    console.log(`🌅 Phase circadienne: ${brainState.circadian.phase || 'N/A'}`);
                    console.log(`📊 Performance cognitive: ${(brainState.circadian.cognitive_performance * 100).toFixed(1)}%`);
                }
                
                if (brainState.neurotransmitters) {
                    console.log(`🧪 Neurotransmetteurs:`);
                    Object.entries(brainState.neurotransmitters).forEach(([name, data]) => {
                        console.log(`   • ${name}: ${(data.level * 100).toFixed(1)}%`);
                    });
                }
            }
        }
        
        console.log('\n✅ === TEST TERMINÉ ===');
        
    } catch (error) {
        console.error(`❌ Erreur lors du test: ${error.message}`);
        console.error(error.stack);
    }
}

// Lancer le test
if (require.main === module) {
    testQIDetaille().catch(console.error);
}

module.exports = { testQIDetaille };
