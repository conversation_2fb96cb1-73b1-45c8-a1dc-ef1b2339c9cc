{"name": "louna-system-complet", "version": "3.0.0", "description": "LOUNA - Système IA complet avec mémoire thermique, tour neuronale et accélérateurs KYBER Ultra", "main": "louna_server_complet.js", "scripts": {"start": "node louna_server_complet.js", "verify": "node verification_systeme_complet.js", "test": "npm run verify && npm run start"}, "keywords": ["ai", "neural-network", "thermal-memory", "kyber-accelerators", "deepseek", "louna"], "author": "<PERSON><PERSON><PERSON>", "license": "MIT", "dependencies": {"express": "^4.18.2", "socket.io": "^4.7.2"}, "engines": {"node": ">=16.0.0"}}