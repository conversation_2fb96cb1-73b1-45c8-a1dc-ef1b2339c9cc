#!/bin/bash

# 🚀 SCRIPT D'INTÉGRATION LOUNA DEEPSEEK R1 8B VERS ELECTRON
# Créé pour Jean-<PERSON> 97180
# Ce script prépare l'intégration du code actuel vers l'application Electron

echo "🚀 === SCRIPT D'INTÉGRATION LOUNA ELECTRON ==="
echo "🤖 Préparation de l'intégration DeepSeek R1 8B + Mémoire Thermique"
echo ""

# Configuration des chemins
CURRENT_DIR="$(pwd)"
ELECTRON_PROJECT_PATH="/Volumes/seagate/LOUNA-AI-INTERFACE-COMPLETE"
BACKUP_DIR="${ELECTRON_PROJECT_PATH}/backup_$(date +%Y%m%d_%H%M%S)"

echo "📁 Répertoire actuel: $CURRENT_DIR"
echo "📁 Projet Electron: $ELECTRON_PROJECT_PATH"
echo "📁 Sauvegarde: $BACKUP_DIR"
echo ""

# Vérifier si le projet Electron existe
if [ ! -d "$ELECTRON_PROJECT_PATH" ]; then
    echo "❌ Projet Electron non trouvé à: $ELECTRON_PROJECT_PATH"
    echo "💡 Veuillez ajuster la variable ELECTRON_PROJECT_PATH dans ce script"
    exit 1
fi

echo "✅ Projet Electron trouvé"
echo ""

# Fonction de sauvegarde
create_backup() {
    echo "💾 === CRÉATION DE LA SAUVEGARDE ==="
    
    if [ -d "$ELECTRON_PROJECT_PATH" ]; then
        echo "📦 Création de la sauvegarde..."
        cp -r "$ELECTRON_PROJECT_PATH" "$BACKUP_DIR"
        
        if [ $? -eq 0 ]; then
            echo "✅ Sauvegarde créée: $BACKUP_DIR"
        else
            echo "❌ Erreur lors de la création de la sauvegarde"
            exit 1
        fi
    fi
    echo ""
}

# Fonction de copie des fichiers core
copy_core_files() {
    echo "📋 === COPIE DES FICHIERS CORE ==="
    
    # Fichiers essentiels à copier
    CORE_FILES=(
        "deepseek-r1-agent-integrated.js"
        "thermal_memory_persistent.json"
        "chat-interface-server.js"
        "INTEGRATION_ELECTRON_GUIDE.md"
    )
    
    for file in "${CORE_FILES[@]}"; do
        if [ -f "$CURRENT_DIR/$file" ]; then
            echo "📄 Copie de $file..."
            cp "$CURRENT_DIR/$file" "$ELECTRON_PROJECT_PATH/"
            echo "✅ $file copié"
        else
            echo "⚠️ $file non trouvé dans $CURRENT_DIR"
        fi
    done
    echo ""
}

# Fonction de copie des fichiers interface
copy_interface_files() {
    echo "🎨 === COPIE DES FICHIERS INTERFACE ==="
    
    # Créer le dossier public s'il n'existe pas
    mkdir -p "$ELECTRON_PROJECT_PATH/public"
    
    # Fichiers interface à copier
    INTERFACE_FILES=(
        "public/index.html"
        "public/style.css"
        "public/script.js"
    )
    
    for file in "${INTERFACE_FILES[@]}"; do
        if [ -f "$CURRENT_DIR/$file" ]; then
            echo "🎨 Copie de $file..."
            cp "$CURRENT_DIR/$file" "$ELECTRON_PROJECT_PATH/$file"
            echo "✅ $file copié"
        else
            echo "⚠️ $file non trouvé dans $CURRENT_DIR"
        fi
    done
    echo ""
}

# Fonction de mise à jour du package.json
update_package_json() {
    echo "📦 === MISE À JOUR PACKAGE.JSON ==="
    
    PACKAGE_JSON="$ELECTRON_PROJECT_PATH/package.json"
    
    if [ -f "$PACKAGE_JSON" ]; then
        echo "📝 Sauvegarde du package.json existant..."
        cp "$PACKAGE_JSON" "$PACKAGE_JSON.backup"
        
        echo "🔧 Ajout des dépendances nécessaires..."
        # Ici vous pouvez ajouter les dépendances nécessaires
        echo "💡 Vérifiez manuellement les dépendances dans package.json"
        echo "   - express"
        echo "   - socket.io"
        echo "   - axios"
        echo "   - fs-extra"
    else
        echo "⚠️ package.json non trouvé dans le projet Electron"
    fi
    echo ""
}

# Fonction de création du script de lancement
create_launch_script() {
    echo "🚀 === CRÉATION DU SCRIPT DE LANCEMENT ==="
    
    LAUNCH_SCRIPT="$ELECTRON_PROJECT_PATH/launch-louna-integrated.sh"
    
    cat > "$LAUNCH_SCRIPT" << 'EOF'
#!/bin/bash

# Script de lancement LOUNA intégré avec Electron
echo "🚀 === LANCEMENT LOUNA ELECTRON INTÉGRÉ ==="
echo "🤖 Agent DeepSeek R1 8B avec mémoire thermique QI 404"
echo "🧠 Système neurologique complet"
echo ""

# Aller dans le répertoire du projet
cd "$(dirname "$0")"

echo "📁 Répertoire: $(pwd)"
echo ""

# Vérifier les dépendances
echo "🔍 Vérification des dépendances..."
if [ ! -d "node_modules" ]; then
    echo "📦 Installation des dépendances..."
    npm install
fi

echo "🚀 Démarrage de l'application Electron avec LOUNA intégré..."
echo ""

# Démarrer l'application Electron
npm start

echo ""
echo "✨ Application fermée"
EOF

    chmod +x "$LAUNCH_SCRIPT"
    echo "✅ Script de lancement créé: $LAUNCH_SCRIPT"
    echo ""
}

# Fonction de création du guide d'utilisation
create_usage_guide() {
    echo "📖 === CRÉATION DU GUIDE D'UTILISATION ==="
    
    USAGE_GUIDE="$ELECTRON_PROJECT_PATH/UTILISATION_LOUNA_INTEGRE.md"
    
    cat > "$USAGE_GUIDE" << 'EOF'
# 🚀 GUIDE D'UTILISATION LOUNA INTÉGRÉ

## 🎯 DÉMARRAGE RAPIDE

### 1. Lancer l'application
```bash
./launch-louna-integrated.sh
```

### 2. Accéder à l'interface
- **Interface principale** : Electron (fenêtre native)
- **Interface web** : http://localhost:3000 (si serveur séparé)

## 🧠 FONCTIONNALITÉS INTÉGRÉES

### ✅ Agent DeepSeek R1 8B
- **QI Unifié** : 241 (calculé automatiquement)
- **Mémoire Thermique** : 6 zones cognitives
- **Connexion Directe** : Pas besoin d'Ollama

### ✅ Interface Moderne
- **Chat Temps Réel** : Communication instantanée
- **Monitoring Neurologique** : Ondes cérébrales, neurotransmetteurs
- **Système KYBER** : Accélérateurs 166x
- **Contrôles Audio** : Microphone et synthèse vocale

### ✅ Mémoire Persistante
- **Sauvegarde Automatique** : Toutes les interactions
- **Zones Spécialisées** : Procédurale, épisodique, sémantique...
- **Calcul QI Dynamique** : Évolution basée sur l'utilisation

## 🔧 CONFIGURATION

### Fichiers Importants
- `deepseek-r1-agent-integrated.js` : Agent principal
- `thermal_memory_persistent.json` : Base de données mémoire
- `public/` : Interface utilisateur

### Personnalisation
- **QI de base** : Modifiable dans l'agent
- **Accélérateurs KYBER** : Configurables
- **Zones mémoire** : Extensibles

## 🆘 DÉPANNAGE

### Problèmes Courants
1. **Port 3000 occupé** : Changer le port dans le serveur
2. **Mémoire non chargée** : Vérifier le chemin du fichier JSON
3. **Interface non accessible** : Vérifier le serveur Express

### Support
- Consulter `INTEGRATION_ELECTRON_GUIDE.md`
- Vérifier les logs dans la console
- Tester chaque composant individuellement
EOF

    echo "✅ Guide d'utilisation créé: $USAGE_GUIDE"
    echo ""
}

# Menu principal
show_menu() {
    echo "🎯 === MENU D'INTÉGRATION ==="
    echo "1. 💾 Créer une sauvegarde complète"
    echo "2. 📋 Copier les fichiers core"
    echo "3. 🎨 Copier les fichiers interface"
    echo "4. 📦 Mettre à jour package.json"
    echo "5. 🚀 Créer le script de lancement"
    echo "6. 📖 Créer le guide d'utilisation"
    echo "7. 🔄 Intégration complète (tout faire)"
    echo "8. ❌ Quitter"
    echo ""
    read -p "Choisissez une option (1-8): " choice
}

# Intégration complète
full_integration() {
    echo "🔄 === INTÉGRATION COMPLÈTE ==="
    echo ""
    
    create_backup
    copy_core_files
    copy_interface_files
    update_package_json
    create_launch_script
    create_usage_guide
    
    echo "🎉 === INTÉGRATION TERMINÉE ==="
    echo "✅ Tous les fichiers ont été copiés"
    echo "✅ Scripts de lancement créés"
    echo "✅ Documentation générée"
    echo ""
    echo "🚀 PROCHAINES ÉTAPES :"
    echo "1. Aller dans: $ELECTRON_PROJECT_PATH"
    echo "2. Lancer: ./launch-louna-integrated.sh"
    echo "3. Tester l'application intégrée"
    echo ""
    echo "📖 Consultez UTILISATION_LOUNA_INTEGRE.md pour plus d'infos"
}

# Boucle principale
while true; do
    show_menu
    
    case $choice in
        1) create_backup ;;
        2) copy_core_files ;;
        3) copy_interface_files ;;
        4) update_package_json ;;
        5) create_launch_script ;;
        6) create_usage_guide ;;
        7) full_integration ;;
        8) echo "👋 Au revoir !"; exit 0 ;;
        *) echo "❌ Option invalide. Veuillez choisir entre 1 et 8." ;;
    esac
    
    echo ""
    read -p "Appuyez sur Entrée pour continuer..."
    echo ""
done
