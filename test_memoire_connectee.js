#!/usr/bin/env node

const io = require('socket.io-client');

console.log('🧪 === TEST CONNEXION MÉMOIRE THERMIQUE ===\n');

const socket = io('http://localhost:3000');

const testsMemoire = [
    {
        question: "Parle-moi de tes formations et de ton apprentissage",
        attendu: "formations",
        description: "Test accès aux formations en mémoire"
    },
    {
        question: "Quelle est ta procédure de réflexion ?",
        attendu: "procédure",
        description: "Test accès aux procédures"
    },
    {
        question: "Comment fonctionne ton intégration mémoire-réflexion ?",
        attendu: "intégration",
        description: "Test accès aux méthodes d'intégration"
    },
    {
        question: "Rappelle-toi de notre conversation précédente",
        attendu: "conversation",
        description: "Test continuité conversation"
    }
];

let currentTest = 0;
let results = [];

socket.on('connect', () => {
    console.log('✅ Connecté au serveur');
    console.log('🎯 Début des tests de connexion mémoire...\n');
    
    setTimeout(() => {
        runNextTest();
    }, 2000);
});

socket.on('reflection_step', (step) => {
    // Analyser les étapes de réflexion pour voir si la mémoire est consultée
    if (step.text && step.text.includes('mémoire')) {
        console.log(`🧠 Étape mémoire: ${step.text}`);
        if (step.reflection) {
            console.log(`   Détail: ${step.reflection.substring(0, 100)}...`);
        }
    }
});

socket.on('agent_response', (response) => {
    const test = testsMemoire[currentTest];
    const answer = response.message;
    
    console.log(`\n📝 TEST ${currentTest + 1}: ${test.description}`);
    console.log(`❓ Question: ${test.question}`);
    console.log(`🤖 Réponse: ${answer.substring(0, 200)}${answer.length > 200 ? '...' : ''}`);
    
    // Analyser la réponse
    let analysis = analyzeMemoryUsage(answer, response, test);
    console.log(`📊 Analyse: ${analysis.status}`);
    console.log(`💾 Mémoires utilisées: ${response.memory_used ? response.memory_used.length : 0}`);
    console.log(`📚 Formations utilisées: ${response.formations_used || 0}`);
    console.log(`🧠 QI Total: ${response.total_qi || 'Non spécifié'}`);
    
    results.push({
        test: test.description,
        question: test.question,
        answer: answer,
        analysis: analysis,
        memory_count: response.memory_used ? response.memory_used.length : 0,
        formations_count: response.formations_used || 0,
        qi_total: response.total_qi
    });
    
    console.log('─'.repeat(80));
    
    currentTest++;
    if (currentTest >= testsMemoire.length) {
        showFinalResults();
    } else {
        setTimeout(() => {
            runNextTest();
        }, 3000);
    }
});

function runNextTest() {
    if (currentTest >= testsMemoire.length) return;
    
    const test = testsMemoire[currentTest];
    console.log(`\n🚀 Lancement test ${currentTest + 1}/${testsMemoire.length}`);
    
    socket.emit('user_message', {
        message: test.question,
        timestamp: new Date().toISOString()
    });
}

function analyzeMemoryUsage(answer, response, test) {
    const lower = answer.toLowerCase();
    
    // Vérifier si la mémoire a été consultée
    const memoryUsed = response.memory_used && response.memory_used.length > 0;
    const formationsUsed = response.formations_used && response.formations_used > 0;
    
    if (!memoryUsed && !formationsUsed) {
        return {
            status: "❌ ÉCHEC - Aucune mémoire consultée !",
            score: 0
        };
    }
    
    // Vérifier si la réponse contient des éléments attendus
    const hasExpectedContent = lower.includes(test.attendu);
    
    if (memoryUsed && formationsUsed && hasExpectedContent) {
        return {
            status: "✅ SUCCÈS COMPLET - Mémoire + formations utilisées",
            score: 100
        };
    } else if (memoryUsed && hasExpectedContent) {
        return {
            status: "✅ SUCCÈS PARTIEL - Mémoire utilisée",
            score: 75
        };
    } else if (memoryUsed) {
        return {
            status: "⚠️ PARTIEL - Mémoire consultée mais contenu incomplet",
            score: 50
        };
    } else {
        return {
            status: "❌ ÉCHEC - Mémoire non utilisée correctement",
            score: 25
        };
    }
}

function showFinalResults() {
    console.log('\n🏆 === RÉSULTATS FINAUX CONNEXION MÉMOIRE ===\n');
    
    let totalScore = 0;
    let memoryTests = 0;
    let formationTests = 0;
    
    results.forEach((result, index) => {
        console.log(`${index + 1}. ${result.test}`);
        console.log(`   ${result.analysis.status}`);
        console.log(`   Mémoires: ${result.memory_count}, Formations: ${result.formations_count}, QI: ${result.qi_total}`);
        
        totalScore += result.analysis.score;
        if (result.memory_count > 0) memoryTests++;
        if (result.formations_count > 0) formationTests++;
    });
    
    const averageScore = totalScore / results.length;
    
    console.log('\n📊 STATISTIQUES FINALES:');
    console.log(`✅ Score moyen: ${averageScore.toFixed(1)}/100`);
    console.log(`💾 Tests avec mémoire: ${memoryTests}/${results.length}`);
    console.log(`📚 Tests avec formations: ${formationTests}/${results.length}`);
    
    if (averageScore >= 90) {
        console.log('\n🎉 VERDICT: CONNEXION MÉMOIRE EXCELLENTE !');
    } else if (averageScore >= 70) {
        console.log('\n✅ VERDICT: Connexion mémoire fonctionnelle');
    } else if (averageScore >= 50) {
        console.log('\n⚠️ VERDICT: Connexion mémoire partielle - à améliorer');
    } else {
        console.log('\n❌ VERDICT: CONNEXION MÉMOIRE DÉFAILLANTE !');
    }
    
    process.exit(0);
}

socket.on('disconnect', () => {
    console.log('❌ Déconnecté');
});

socket.on('connect_error', (error) => {
    console.error('❌ Erreur de connexion:', error.message);
    process.exit(1);
});
