#!/usr/bin/env node

/**
 * 🔍 VÉRIFICATION COMPLÈTE DU SYSTÈME THERMIQUE SOPHISTIQUÉ
 * 
 * Vérifie TOUS les composants pour éviter pertes et problèmes
 */

const fs = require('fs');

function verificationCompleteThermique() {
    console.log('🔍 === VÉRIFICATION COMPLÈTE SYSTÈME THERMIQUE ===\n');
    
    try {
        // Charger la mémoire thermique
        const memoryData = JSON.parse(fs.readFileSync('thermal_memory_persistent.json', 'utf8'));
        
        console.log('📊 === ÉTAT GÉNÉRAL ===');
        console.log(`📁 Fichier: thermal_memory_persistent.json`);
        console.log(`📏 Taille: ${(JSON.stringify(memoryData).length / 1024 / 1024).toFixed(2)} MB`);
        console.log(`🕐 Dernière modification: ${new Date(fs.statSync('thermal_memory_persistent.json').mtime).toLocaleString()}`);
        
        // 1. VÉRIFICATION ZONES THERMIQUES
        console.log('\n🌡️ === ZONES THERMIQUES ===');
        const zones = memoryData.thermal_zones || {};
        const zoneNames = ['zone1_working', 'zone2_episodic', 'zone4_semantic', 'zone3_procedural', 'zone5_emotional', 'zone6_meta'];
        
        zoneNames.forEach(zoneName => {
            const zone = zones[zoneName];
            if (zone) {
                console.log(`✅ Zone ${zoneName}:`);
                console.log(`   📊 Entrées: ${zone.entries?.length || 0}`);
                console.log(`   🌡️ Température: ${zone.temperature || 'N/A'}°C`);
                console.log(`   ⚡ Activité: ${zone.activity_level || 'N/A'}`);
                console.log(`   🔄 Dernière activité: ${zone.last_activity ? new Date(zone.last_activity).toLocaleString() : 'N/A'}`);
            } else {
                console.log(`❌ Zone ${zoneName}: MANQUANTE !`);
            }
        });
        
        // 2. VÉRIFICATION TOUR NEURONALE
        console.log('\n🏗️ === TOUR NEURONALE ===');
        const tower = memoryData.neural_tower;
        console.log(`🔍 Debug: tower = ${JSON.stringify(tower, null, 2)}`);
        if (tower) {
            console.log(`✅ Tour neuronale active: ${tower.active}`);
            console.log(`🏢 Étages totaux: ${tower.total_floors?.toLocaleString()}`);
            console.log(`⚡ Étages actifs: ${tower.active_floors}`);
            console.log(`🧠 Neurones/étage: ${tower.neurons_per_floor?.toLocaleString()}`);
            console.log(`🔄 Rotation: ${tower.floor_rotation_interval}s`);
            console.log(`📊 Efficacité: ${(tower.tower_efficiency * 100).toFixed(1)}%`);
            console.log(`🎯 Stratégie: ${tower.activation_strategy}`);
            
            // Vérifier les états des étages
            const floorStates = tower.floor_states || {};
            const activeFloors = Object.values(floorStates).filter(f => f.state === 'active').length;
            const standbyFloors = Object.values(floorStates).filter(f => f.state === 'standby').length;
            
            console.log(`🏢 États étages:`);
            console.log(`   ⚡ Actifs: ${activeFloors}`);
            console.log(`   💤 Standby: ${standbyFloors}`);
            console.log(`   📊 Total configurés: ${Object.keys(floorStates).length}`);
            
            if (Object.keys(floorStates).length < tower.total_floors) {
                console.log(`⚠️  ATTENTION: ${tower.total_floors - Object.keys(floorStates).length} étages non configurés !`);
            }
        } else {
            console.log(`❌ Tour neuronale: MANQUANTE !`);
        }
        
        // 3. VÉRIFICATION SYSTÈME NEURAL
        console.log('\n🧠 === SYSTÈME NEURAL ===');
        const neural = memoryData.neural_system;
        if (neural) {
            console.log(`✅ Neurones totaux: ${neural.total_neurons?.toLocaleString()}`);
            console.log(`🔗 Synapses: ${neural.synapses?.toLocaleString()}`);
            console.log(`🎯 QI niveau: ${neural.qi_level}`);
            console.log(`⚡ Neurones actifs: ${neural.active_neurons?.toLocaleString()}`);
            console.log(`💤 Neurones standby: ${neural.standby_neurons?.toLocaleString()}`);
            console.log(`😴 Neurones hibernation: ${neural.hibernating_neurons?.toLocaleString()}`);
            
            // Vérifier cohérence
            const total = (neural.active_neurons || 0) + (neural.standby_neurons || 0) + (neural.hibernating_neurons || 0);
            const expected = neural.total_neurons || 0;
            
            if (Math.abs(total - expected) > 1000) {
                console.log(`⚠️  INCOHÉRENCE: Total calculé (${total.toLocaleString()}) ≠ Total attendu (${expected.toLocaleString()})`);
            } else {
                console.log(`✅ Cohérence neuronale: OK`);
            }
            
            // Vérifier stockage neurones
            const storage = neural.neuron_storage;
            if (storage) {
                console.log(`💾 Stockage neurones:`);
                console.log(`   🧠 Neurones stockés: ${storage.neurons?.length || 0}`);
                console.log(`   📊 Capacité: ${storage.storage_capacity?.toLocaleString() || 'N/A'}`);
                console.log(`   📝 Logs création: ${storage.creation_log?.length || 0}`);
                console.log(`   🗜️ Compression: ${storage.compression_enabled ? 'Activée' : 'Désactivée'}`);
            }
        } else {
            console.log(`❌ Système neural: MANQUANT !`);
        }
        
        // 4. VÉRIFICATION QI UNIFIÉ
        console.log('\n🎯 === QI UNIFIÉ ===');
        const qiComponents = neural?.qi_components;
        if (qiComponents) {
            console.log(`✅ Composants QI:`);
            Object.entries(qiComponents).forEach(([comp, value]) => {
                console.log(`   ${comp}: ${value}`);
            });
            
            const calculatedTotal = Object.entries(qiComponents)
                .filter(([key]) => key !== 'total_calculated')
                .reduce((sum, [, value]) => sum + (value || 0), 0);
            
            if (calculatedTotal !== qiComponents.total_calculated) {
                console.log(`⚠️  INCOHÉRENCE QI: Calculé (${calculatedTotal}) ≠ Stocké (${qiComponents.total_calculated})`);
            } else {
                console.log(`✅ Cohérence QI: OK`);
            }
        }
        
        // 5. VÉRIFICATION ACCÉLÉRATEURS
        console.log('\n⚡ === ACCÉLÉRATEURS ===');
        const accelerators = memoryData.accelerators || {};
        const activeAccelerators = Object.values(accelerators).filter(acc => acc.active);
        
        console.log(`📊 Accélérateurs totaux: ${Object.keys(accelerators).length}`);
        console.log(`⚡ Accélérateurs actifs: ${activeAccelerators.length}`);
        
        if (activeAccelerators.length > 0) {
            const totalBoost = activeAccelerators.reduce((sum, acc) => sum + (acc.boost_factor || 0), 0);
            console.log(`🚀 Boost total: ${totalBoost.toFixed(1)}x`);
            
            // Vérifier accélérateurs tour
            const towerAccelerators = activeAccelerators.filter(acc => 
                acc.type?.includes('tower') || acc.type?.includes('neural')
            );
            console.log(`🏗️ Accélérateurs tour: ${towerAccelerators.length}`);
        }
        
        // 6. VÉRIFICATION PROTECTION
        console.log('\n🛡️ === PROTECTION SYSTÈME ===');
        const protection = memoryData.neural_protection;
        if (protection) {
            console.log(`✅ Protection active: ${protection.protect_neural_system}`);
            console.log(`🏗️ Protection tour: ${protection.protect_neural_tower}`);
            console.log(`💾 Protection stockage: ${protection.protect_neuron_storage}`);
            console.log(`🔒 Niveau: ${protection.protection_level}`);
            console.log(`🕐 Dernière vérif: ${protection.last_protection_check}`);
        } else {
            console.log(`❌ Système protection: MANQUANT !`);
        }
        
        // 7. VÉRIFICATION SAUVEGARDES
        console.log('\n💾 === SAUVEGARDES ===');
        const backupFiles = [
            'thermal_memory_backup.json',
            'thermal_memory_backup_1749765256967.json'
        ];
        
        backupFiles.forEach(file => {
            if (fs.existsSync(file)) {
                const stats = fs.statSync(file);
                console.log(`✅ ${file}:`);
                console.log(`   📏 Taille: ${(stats.size / 1024 / 1024).toFixed(2)} MB`);
                console.log(`   🕐 Date: ${stats.mtime.toLocaleString()}`);
            } else {
                console.log(`❌ ${file}: MANQUANT !`);
            }
        });
        
        // 8. RECOMMANDATIONS
        console.log('\n📋 === RECOMMANDATIONS ===');
        
        const issues = [];
        const recommendations = [];
        
        // Vérifications critiques
        if (!memoryData.neural_tower) {
            issues.push("Tour neuronale manquante");
            recommendations.push("Restaurer la tour neuronale complète");
        }
        
        if (!memoryData.neural_protection) {
            issues.push("Système de protection manquant");
            recommendations.push("Ajouter protection maximale");
        }
        
        if (Object.keys(accelerators).length < 5) {
            issues.push("Accélérateurs insuffisants");
            recommendations.push("Installer accélérateurs KYBER complets");
        }
        
        const totalMemoryEntries = Object.values(zones).reduce((sum, zone) => 
            sum + (zone.entries?.length || 0), 0
        );
        
        if (totalMemoryEntries < 50) {
            recommendations.push("Enrichir la mémoire thermique (actuellement " + totalMemoryEntries + " entrées)");
        }
        
        if (issues.length === 0) {
            console.log('✅ SYSTÈME PARFAITEMENT CONFIGURÉ !');
        } else {
            console.log('⚠️  PROBLÈMES DÉTECTÉS:');
            issues.forEach(issue => console.log(`   ❌ ${issue}`));
        }
        
        if (recommendations.length > 0) {
            console.log('\n💡 RECOMMANDATIONS:');
            recommendations.forEach(rec => console.log(`   🔧 ${rec}`));
        }
        
        // 9. RÉSUMÉ FINAL
        console.log('\n🎯 === RÉSUMÉ FINAL ===');
        console.log(`📊 Zones thermiques: ${Object.keys(zones).length}/6`);
        console.log(`🏗️ Tour neuronale: ${tower ? '✅' : '❌'}`);
        console.log(`🧠 Système neural: ${neural ? '✅' : '❌'}`);
        console.log(`⚡ Accélérateurs: ${Object.keys(accelerators).length}`);
        console.log(`🛡️ Protection: ${protection ? '✅' : '❌'}`);
        console.log(`💾 Entrées mémoire: ${totalMemoryEntries}`);
        console.log(`🎯 QI niveau: ${neural?.qi_level || 'N/A'}`);
        
        return {
            zones: Object.keys(zones).length,
            tower: !!tower,
            neural: !!neural,
            accelerators: Object.keys(accelerators).length,
            protection: !!protection,
            memoryEntries: totalMemoryEntries,
            qiLevel: neural?.qi_level,
            issues: issues,
            recommendations: recommendations
        };
        
    } catch (error) {
        console.error(`❌ Erreur vérification: ${error.message}`);
        return null;
    }
}

// Lancer la vérification
if (require.main === module) {
    verificationCompleteThermique();
}

module.exports = { verificationCompleteThermique };
