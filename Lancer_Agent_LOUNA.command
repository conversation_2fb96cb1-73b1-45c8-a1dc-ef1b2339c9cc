#!/bin/bash

# Script de lancement de l'Agent LOUNA DeepSeek R1 8B
# <PERSON><PERSON><PERSON> pour <PERSON> 97180

echo "🚀 === LANCEMENT AGENT LOUNA DEEPSEEK R1 8B ==="
echo "🤖 Agent IA sophistiqué avec QI 235 et mémoire thermique"
echo "🧠 Système neurologique intégré"
echo ""

# Vérifier si Ollama est démarré
echo "🔍 Vérification d'Ollama..."
if ! pgrep -x "ollama" > /dev/null; then
    echo "⚠️  Ollama n'est pas démarré. Démarrage..."
    ollama serve &
    sleep 3
    echo "✅ Ollama démarré"
else
    echo "✅ Ollama déjà en cours d'exécution"
fi

# Vérifier si le modèle DeepSeek R1 8B est disponible
echo "🔍 Vérification du modèle DeepSeek R1 8B..."
if ollama list | grep -q "deepseek-r1:8b"; then
    echo "✅ Modèle DeepSeek R1 8B disponible"
else
    echo "❌ Modèle DeepSeek R1 8B non trouvé"
    echo "📥 Téléchargement du modèle..."
    ollama pull deepseek-r1:8b
fi

# Aller dans le répertoire de l'agent
cd "$(dirname "$0")"

# Vérifier si le serveur est déjà en cours
if lsof -i :3000 > /dev/null 2>&1; then
    echo "⚠️  Le serveur est déjà en cours sur le port 3000"
    echo "🌐 Ouverture de l'interface..."
    open http://localhost:3000
else
    echo "🚀 Démarrage du serveur Agent LOUNA..."
    
    # Démarrer le serveur en arrière-plan
    node chat-interface-server.js &
    SERVER_PID=$!
    
    # Attendre que le serveur démarre
    echo "⏳ Attente du démarrage du serveur..."
    sleep 5
    
    # Vérifier si le serveur a démarré
    if lsof -i :3000 > /dev/null 2>&1; then
        echo "✅ Serveur démarré avec succès !"
        echo "🌐 Ouverture de l'interface Agent LOUNA..."
        open http://localhost:3000
        
        echo ""
        echo "🎯 === AGENT LOUNA OPÉRATIONNEL ==="
        echo "🌐 Interface: http://localhost:3000"
        echo "🤖 Agent: DeepSeek R1 8B (QI 235)"
        echo "🧠 Mémoire thermique: Activée"
        echo "🔄 Système neurologique: Actif"
        echo ""
        echo "💡 Pour arrêter l'agent, fermez cette fenêtre ou appuyez sur Ctrl+C"
        
        # Garder le script ouvert
        wait $SERVER_PID
    else
        echo "❌ Erreur lors du démarrage du serveur"
        exit 1
    fi
fi
