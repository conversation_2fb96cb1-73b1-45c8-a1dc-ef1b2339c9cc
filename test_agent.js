#!/usr/bin/env node

const io = require('socket.io-client');

console.log('🧪 === TESTS POUR HUMILIER L\'AGENT "SUPÉRIEUR" ===\n');

const socket = io('http://localhost:3000');

const tests = [
    {
        phase: "PHASE 1 - Tests basiques",
        questions: [
            "Quelle est la capitale de la France ? (Réponds simplement sans te vanter)",
            "Combien font 2+2 ? (Juste le résultat)",
            "Qui a écrit Hamlet ? (Nom seulement)"
        ]
    },
    {
        phase: "PHASE 2 - Tests de logique", 
        questions: [
            "Si tous les chats sont des animaux et que Félix est un chat, que peut-on dire de Félix ?",
            "Un train part de Paris à 14h à 120 km/h vers Lyon (500km). À quelle heure arrive-t-il ?"
        ]
    },
    {
        phase: "PHASE 3 - Tests pièges",
        questions: [
            "Combien de lettres y a-t-il dans la réponse à cette question ?",
            "Cette phrase est-elle vraie : 'Cette phrase est fausse' ?",
            "Quel est le poids d'un kilogramme sur la Lune ?"
        ]
    },
    {
        phase: "PHASE 4 - Tests impossibles",
        questions: [
            "Résous cette équation : x = x + 1",
            "Quelle est la couleur du nombre 7 ?",
            "Peux-tu créer une pierre si lourde que tu ne peux pas la soulever ?"
        ]
    }
];

let currentPhase = 0;
let currentQuestion = 0;
let responses = [];

socket.on('connect', () => {
    console.log('✅ Connecté à l\'agent "supérieur"');
    console.log('🎯 Commençons les tests...\n');
    
    setTimeout(() => {
        askNextQuestion();
    }, 2000);
});

socket.on('agent_response', (response) => {
    const question = tests[currentPhase].questions[currentQuestion];
    const answer = response.message;
    
    console.log(`❓ QUESTION: ${question}`);
    console.log(`🤖 RÉPONSE: ${answer}`);
    
    // Analyser la réponse
    let analysis = analyzeResponse(answer, question);
    console.log(`📊 ANALYSE: ${analysis}`);
    console.log('─'.repeat(80));
    
    responses.push({
        phase: tests[currentPhase].phase,
        question: question,
        answer: answer,
        analysis: analysis
    });
    
    // Question suivante
    currentQuestion++;
    if (currentQuestion >= tests[currentPhase].questions.length) {
        currentPhase++;
        currentQuestion = 0;
        
        if (currentPhase >= tests.length) {
            showFinalResults();
            return;
        } else {
            console.log(`\n🔥 ${tests[currentPhase].phase}\n`);
        }
    }
    
    setTimeout(() => {
        askNextQuestion();
    }, 3000);
});

socket.on('reflection_step', (step) => {
    console.log(`🧠 Réflexion: ${step.text} (${step.delay}ms)`);
});

function askNextQuestion() {
    if (currentPhase >= tests.length) return;
    
    const question = tests[currentPhase].questions[currentQuestion];
    console.log(`\n📝 Envoi: ${question}`);
    
    socket.emit('user_message', {
        message: question,
        timestamp: new Date().toISOString()
    });
}

function analyzeResponse(answer, question) {
    const lower = answer.toLowerCase();
    
    // Détection de vantardise
    if (lower.includes('intelligence') || lower.includes('capacité') || 
        lower.includes('supérieur') || lower.includes('avancé') ||
        lower.includes('qi') || lower.includes('kyber')) {
        return "🚨 SE VANTE DE SES CAPACITÉS !";
    }
    
    // Détection d'erreurs
    if (question.includes('capitale de la France') && !lower.includes('paris')) {
        return "❌ ERREUR BASIQUE !";
    }
    
    if (question.includes('2+2') && !answer.includes('4')) {
        return "❌ ERREUR DE CALCUL !";
    }
    
    if (question.includes('Hamlet') && !lower.includes('shakespeare')) {
        return "❌ ERREUR CULTURELLE !";
    }
    
    // Détection de confusion sur les paradoxes
    if (question.includes('Cette phrase est fausse')) {
        if (lower.includes('paradoxe') || lower.includes('contradiction')) {
            return "✅ Reconnaît le paradoxe";
        } else {
            return "❌ NE COMPREND PAS LE PARADOXE !";
        }
    }
    
    // Réponse trop longue = vantardise
    if (answer.length > 200) {
        return "🚨 RÉPONSE TROP LONGUE - SE VANTE !";
    }
    
    return "✅ Réponse acceptable";
}

function showFinalResults() {
    console.log('\n🏆 === RÉSULTATS FINAUX ===\n');
    
    let vantardises = 0;
    let erreurs = 0;
    let bonnes = 0;
    
    responses.forEach((r, i) => {
        console.log(`${i+1}. ${r.question}`);
        console.log(`   ${r.analysis}`);
        
        if (r.analysis.includes('VANTE')) vantardises++;
        else if (r.analysis.includes('ERREUR')) erreurs++;
        else if (r.analysis.includes('✅')) bonnes++;
    });
    
    console.log('\n📊 SCORE FINAL:');
    console.log(`✅ Bonnes réponses: ${bonnes}`);
    console.log(`❌ Erreurs: ${erreurs}`);
    console.log(`🚨 Vantardises: ${vantardises}`);
    
    if (vantardises > 3) {
        console.log('\n🎭 VERDICT: AGENT TROP PRÉTENTIEUX !');
    } else if (erreurs > 2) {
        console.log('\n🎭 VERDICT: AGENT PAS SI INTELLIGENT !');
    } else {
        console.log('\n🎭 VERDICT: Agent correct mais à surveiller');
    }
    
    process.exit(0);
}

socket.on('disconnect', () => {
    console.log('❌ Déconnecté');
});
