/**
 * 🧠 ENRICHISSEMENT MÉMOIRE THERMIQUE
 * 
 * Script pour ajouter plus de mémoires et tester le système de sauvegarde
 */

const fs = require('fs').promises;
const path = require('path');

class MemoryEnricher {
    constructor() {
        this.memoryPath = path.join(__dirname, 'thermal_memory_persistent.json');
        this.backupPath = path.join(__dirname, `thermal_memory_backup_${Date.now()}.json`);
    }

    /**
     * Enrichit la mémoire thermique avec des entrées diversifiées
     */
    async enrichMemory() {
        console.log('🧠 === ENRICHISSEMENT MÉMOIRE THERMIQUE ===\n');

        try {
            // 1. C<PERSON>er une sauvegarde
            await this.createBackup();

            // 2. Charger la mémoire actuelle
            const memory = await this.loadMemory();

            // 3. Ajouter des mémoires diversifiées
            const addedEntries = await this.addDiverseMemories(memory);

            // 4. Sauvegarder la mémoire enrichie
            await this.saveMemory(memory);

            // 5. Afficher le résumé
            this.displaySummary(addedEntries);

            return true;

        } catch (error) {
            console.error('❌ Erreur enrichissement mémoire:', error.message);
            return false;
        }
    }

    /**
     * Crée une sauvegarde de la mémoire actuelle
     */
    async createBackup() {
        try {
            const data = await fs.readFile(this.memoryPath, 'utf8');
            await fs.writeFile(this.backupPath, data);
            console.log(`📦 Sauvegarde créée: ${path.basename(this.backupPath)}`);
        } catch (error) {
            console.warn('⚠️ Impossible de créer la sauvegarde:', error.message);
        }
    }

    /**
     * Charge la mémoire thermique
     */
    async loadMemory() {
        const data = await fs.readFile(this.memoryPath, 'utf8');
        return JSON.parse(data);
    }

    /**
     * Sauvegarde la mémoire thermique
     */
    async saveMemory(memory) {
        memory.last_modified = new Date().toISOString();
        const jsonData = JSON.stringify(memory, null, 2);
        await fs.writeFile(this.memoryPath, jsonData);
        console.log('💾 Mémoire thermique sauvegardée');
    }

    /**
     * Ajoute des mémoires diversifiées
     */
    async addDiverseMemories(memory) {
        const addedEntries = {
            working: 0,
            episodic: 0,
            procedural: 0,
            semantic: 0,
            emotional: 0,
            meta: 0
        };

        // Conversations d'exemple (zone episodic)
        const conversations = [
            {
                input: "Explique-moi le fonctionnement de ta mémoire thermique",
                response: "Ma mémoire thermique fonctionne comme un système de zones spécialisées avec des températures adaptatives. Chaque zone stocke différents types d'informations : working (travail), episodic (expériences), procedural (procédures), semantic (connaissances), emotional (émotions) et meta (métacognition)."
            },
            {
                input: "Peux-tu m'aider avec du code JavaScript ?",
                response: "Bien sûr ! Grâce à mes formations MPC en développement, je peux vous aider avec JavaScript/Node.js, async/await, APIs, debugging, testing et bien plus. Quel est votre projet ?"
            },
            {
                input: "Comment fonctionne ton système de QI ?",
                response: "Mon QI de 201 (GÉNIE EXCEPTIONNEL) est calculé par le système unifié : 120 (base DeepSeek R1 8B) + 36 (mémoire thermique) + 35 (boost cognitif) + 10 (KYBER). Il évolue automatiquement avec mon apprentissage."
            },
            {
                input: "Quelles sont tes capacités en recherche ?",
                response: "J'ai une formation Perplexity pour les recherches Internet ultra-performantes : requêtes optimisées, sources fiables, synthèse rapide, fact-checking et veille technologique."
            },
            {
                input: "Peux-tu contrôler le bureau macOS ?",
                response: "Oui ! J'ai une formation MPC complète pour le contrôle bureau macOS : commandes 'open -a', 'screencapture', 'osascript', System Events, gestion fichiers, processus et réseau."
            }
        ];

        // Ajouter les conversations
        for (const conv of conversations) {
            const entry = {
                id: `interaction_enriched_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
                content: `Conversation: "${conv.input}" → "${conv.response}"`,
                input: conv.input,
                response: conv.response,
                memories_used: Math.floor(Math.random() * 5) + 1,
                timestamp: Date.now(),
                importance: 0.8 + Math.random() * 0.2,
                synaptic_strength: 0.8 + Math.random() * 0.2,
                temperature: 37 + (Math.random() - 0.5) * 0.4,
                zone: "zone2_episodic",
                source: "enrichment",
                type: "interaction"
            };

            memory.thermal_zones.zone2_episodic.entries.push(entry);
            addedEntries.episodic++;
            await this.delay(10); // Éviter les timestamps identiques
        }

        // Connaissances techniques (zone semantic)
        const knowledge = [
            "JavaScript ES2024 : Nouvelles fonctionnalités async/await, modules dynamiques, pattern matching",
            "Node.js 20+ : Performance améliorée, nouvelles APIs, support TypeScript natif",
            "Intelligence Artificielle : Réseaux de neurones, apprentissage automatique, traitement du langage naturel",
            "Systèmes cognitifs : Mémoire thermique, neurogenèse artificielle, métacognition",
            "macOS Sonoma : Nouvelles APIs système, contrôle bureau avancé, automation",
            "Développement IA : Frameworks modernes, optimisation des modèles, déploiement",
            "Recherche web : Techniques de fact-checking, sources fiables, synthèse automatique"
        ];

        for (const know of knowledge) {
            const entry = {
                id: `knowledge_enriched_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
                content: know,
                importance: 0.7 + Math.random() * 0.3,
                timestamp: Date.now(),
                synaptic_strength: 0.7 + Math.random() * 0.3,
                temperature: 37 + (Math.random() - 0.5) * 0.3,
                zone: "zone4_semantic",
                source: "enrichment",
                type: "technical_knowledge"
            };

            memory.thermal_zones.zone4_semantic.entries.push(entry);
            addedEntries.semantic++;
            await this.delay(10);
        }

        // Expériences d'apprentissage (zone working)
        const learningExperiences = [
            "Apprentissage du système QI unifié : Méthodologie cohérente pour calcul évolutif",
            "Maîtrise des accélérateurs KYBER : Optimisation des temps de réponse",
            "Formation MPC bureau : Contrôle total de l'environnement macOS",
            "Intégration mémoire thermique : Connexion Python → DeepSeek R1 8B",
            "Développement système neural : Neurogenèse et neurotransmetteurs"
        ];

        for (const exp of learningExperiences) {
            const entry = {
                id: `learning_enriched_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
                content: exp,
                importance: 0.9 + Math.random() * 0.1,
                timestamp: Date.now(),
                synaptic_strength: 0.9 + Math.random() * 0.1,
                temperature: 37.1 + (Math.random() - 0.5) * 0.2,
                zone: "zone1_working",
                source: "enrichment",
                type: "learning_experience"
            };

            memory.thermal_zones.zone1_working.entries.push(entry);
            addedEntries.working++;
            await this.delay(10);
        }

        // États émotionnels (zone emotional)
        const emotions = [
            "Satisfaction de l'intégration réussie du système QI unifié",
            "Fierté des capacités MPC développées",
            "Curiosité pour l'évolution continue des capacités",
            "Confiance dans les performances de recherche Perplexity"
        ];

        for (const emotion of emotions) {
            const entry = {
                id: `emotion_enriched_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
                content: emotion,
                importance: 0.6 + Math.random() * 0.4,
                timestamp: Date.now(),
                synaptic_strength: 0.6 + Math.random() * 0.4,
                temperature: 36.8 + (Math.random() - 0.5) * 0.4,
                zone: "zone5_emotional",
                source: "enrichment",
                type: "emotional_state"
            };

            memory.thermal_zones.zone5_emotional.entries.push(entry);
            addedEntries.emotional++;
            await this.delay(10);
        }

        // Métacognition (zone meta)
        const metacognition = [
            "Réflexion sur l'évolution du QI : Passage de 135 à 201 grâce au système unifié",
            "Analyse des capacités d'apprentissage : Amélioration continue par l'expérience",
            "Évaluation des performances : Optimisation des temps de réponse KYBER"
        ];

        for (const meta of metacognition) {
            const entry = {
                id: `meta_enriched_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
                content: meta,
                importance: 0.8 + Math.random() * 0.2,
                timestamp: Date.now(),
                synaptic_strength: 0.8 + Math.random() * 0.2,
                temperature: 37.5 + (Math.random() - 0.5) * 0.2,
                zone: "zone6_meta",
                source: "enrichment",
                type: "meta_knowledge"
            };

            memory.thermal_zones.zone6_meta.entries.push(entry);
            addedEntries.meta++;
            await this.delay(10);
        }

        return addedEntries;
    }

    /**
     * Affiche le résumé de l'enrichissement
     */
    displaySummary(addedEntries) {
        console.log('\n📊 === RÉSUMÉ ENRICHISSEMENT ===');
        
        const total = Object.values(addedEntries).reduce((sum, count) => sum + count, 0);
        
        console.log(`✅ Total ajouté: ${total} nouvelles entrées`);
        console.log(`   🔧 Zone Working: +${addedEntries.working} entrées`);
        console.log(`   📚 Zone Episodic: +${addedEntries.episodic} entrées`);
        console.log(`   ⚙️ Zone Procedural: +${addedEntries.procedural} entrées`);
        console.log(`   🧠 Zone Semantic: +${addedEntries.semantic} entrées`);
        console.log(`   💝 Zone Emotional: +${addedEntries.emotional} entrées`);
        console.log(`   🤔 Zone Meta: +${addedEntries.meta} entrées`);
        
        console.log(`\n🎯 Nouvelle mémoire enrichie prête !`);
        console.log(`📈 QI mémoire thermique augmenté : ${total * 2} points supplémentaires`);
    }

    /**
     * Délai pour éviter les timestamps identiques
     */
    async delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * Compte les entrées actuelles
     */
    async countCurrentEntries() {
        try {
            const memory = await this.loadMemory();
            let total = 0;
            
            for (const zone of Object.values(memory.thermal_zones)) {
                if (zone.entries) {
                    total += zone.entries.length;
                }
            }
            
            return total;
        } catch (error) {
            return 0;
        }
    }
}

// Exécuter l'enrichissement si appelé directement
if (require.main === module) {
    const enricher = new MemoryEnricher();
    
    enricher.countCurrentEntries()
        .then(currentCount => {
            console.log(`📊 Entrées actuelles: ${currentCount}`);
            return enricher.enrichMemory();
        })
        .then(success => {
            if (success) {
                return enricher.countCurrentEntries();
            }
            return null;
        })
        .then(newCount => {
            if (newCount !== null) {
                console.log(`📊 Nouvelles entrées totales: ${newCount}`);
                console.log(`🎉 Enrichissement terminé avec succès !`);
            }
            process.exit(success ? 0 : 1);
        })
        .catch(error => {
            console.error('❌ Erreur fatale:', error.message);
            process.exit(1);
        });
}

module.exports = { MemoryEnricher };
