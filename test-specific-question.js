#!/usr/bin/env node

/**
 * 🧪 TEST QUESTION SPÉCIFIQUE
 * 
 * Test d'une question spécifique pour vérifier
 * que l'agent donne maintenant les bonnes réponses
 */

const DeepSeekR1IntegratedAgent = require('./deepseek-r1-agent-integrated');

async function testSpecificQuestion() {
    console.log('🧪 === TEST QUESTION SPÉCIFIQUE ===\n');
    
    try {
        // Initialiser l'agent
        console.log('🤖 Initialisation de l\'agent...');
        const agent = new DeepSeekR1IntegratedAgent();
        
        const success = await agent.initialize();
        if (!success) {
            throw new Error('Échec initialisation agent');
        }
        
        console.log('✅ Agent initialisé avec succès\n');
        
        // Test de la question sur la capitale de la France
        console.log('📝 Question: "Quelle est la capitale de la France ?"');
        
        const startTime = Date.now();
        const response = await agent.processMessage("Quelle est la capitale de la France ?");
        const endTime = Date.now();
        
        console.log(`⏱️ Temps de traitement: ${endTime - startTime}ms\n`);
        
        // Afficher la réponse complète
        console.log('📄 === RÉPONSE COMPLÈTE ===');
        console.log(`Message: ${response.message}`);
        console.log('');
        
        if (response.reflection) {
            console.log(`💭 Réflexion: ${response.reflection}`);
            console.log('');
        }
        
        if (response.memory_used && response.memory_used.length > 0) {
            console.log(`🧠 Mémoires utilisées: ${response.memory_used.length}`);
            response.memory_used.forEach((mem, idx) => {
                console.log(`   ${idx + 1}. ${mem.content.substring(0, 80)}...`);
            });
            console.log('');
        }
        
        // Vérifier si la réponse contient "Paris"
        const containsParis = response.message.toLowerCase().includes('paris');
        
        console.log('🎯 === VALIDATION ===');
        if (containsParis) {
            console.log('✅ SUCCÈS : La réponse contient "Paris"');
            console.log('✅ L\'agent donne maintenant les bonnes réponses !');
        } else {
            console.log('❌ ÉCHEC : La réponse ne contient pas "Paris"');
            console.log('❌ L\'agent ne donne pas encore les bonnes réponses');
        }
        
        // Test d'une autre question
        console.log('\n📝 Question: "Bonjour, comment allez-vous ?"');
        
        const response2 = await agent.processMessage("Bonjour, comment allez-vous ?");
        console.log(`📄 Réponse: ${response2.message.substring(0, 150)}...`);
        
        const isGreeting = response2.message.toLowerCase().includes('bonjour') || 
                          response2.message.toLowerCase().includes('bien');
        
        if (isGreeting) {
            console.log('✅ SUCCÈS : Réponse appropriée au salut');
        } else {
            console.log('❌ ÉCHEC : Réponse inappropriée au salut');
        }
        
        console.log('\n🎉 === RÉSULTAT FINAL ===');
        
        if (containsParis && isGreeting) {
            console.log('🎉 EXCELLENT ! L\'agent répond correctement aux questions !');
            console.log('✅ Questions factuelles: OK');
            console.log('✅ Interactions sociales: OK');
            console.log('✅ Mémoire thermique: Intégrée');
            console.log('✅ Système neurologique: Actif');
            console.log('');
            console.log('🚀 L\'interface de chat est maintenant pleinement fonctionnelle !');
        } else {
            console.log('⚠️ L\'agent répond mais les réponses peuvent être améliorées');
        }
        
    } catch (error) {
        console.error(`❌ Erreur test: ${error.message}`);
        console.error(error.stack);
        process.exit(1);
    }
}

// Lancer le test
if (require.main === module) {
    testSpecificQuestion();
}

module.exports = { testSpecificQuestion };
