#!/usr/bin/env node

/**
 * Test simple de vérification de la mémoire thermique de LOUNA
 */

const fs = require('fs');
const path = require('path');

class TestMemoireSimple {
    constructor() {
        this.memoryFile = 'thermal_memory_persistent.json';
    }

    async runTests() {
        console.log('🧠 === TEST MÉMOIRE THERMIQUE LOUNA ===\n');
        
        this.testMemoryFile();
        this.testMemoryContent();
        this.testMemoryStructure();
        this.generateRecommendations();
    }

    testMemoryFile() {
        console.log('📁 === TEST 1: FICHIER MÉMOIRE ===');
        
        if (fs.existsSync(this.memoryFile)) {
            const stats = fs.statSync(this.memoryFile);
            const sizeKB = (stats.size / 1024).toFixed(2);
            
            console.log(`✅ Fichier trouvé: ${this.memoryFile}`);
            console.log(`📊 Taille: ${sizeKB} KB`);
            console.log(`📅 Modifié: ${stats.mtime.toLocaleString()}`);
            
            if (stats.size > 1000) {
                console.log('✅ Taille appropriée pour une mémoire riche');
            } else {
                console.log('⚠️ Fichier petit - mémoire limitée');
            }
        } else {
            console.log(`❌ Fichier mémoire non trouvé: ${this.memoryFile}`);
            return false;
        }
        
        console.log();
        return true;
    }

    testMemoryContent() {
        console.log('🧠 === TEST 2: CONTENU MÉMOIRE ===');
        
        try {
            const content = fs.readFileSync(this.memoryFile, 'utf8');
            const memory = JSON.parse(content);
            
            // Informations de base
            console.log(`📋 Version: ${memory.version || 'N/A'}`);
            console.log(`🤖 Type agent: ${memory.agent_type || 'N/A'}`);
            console.log(`📅 Timestamp: ${memory.timestamp || 'N/A'}`);
            
            // Système neural
            if (memory.neural_system) {
                console.log(`\n🧠 SYSTÈME NEURAL:`);
                console.log(`   🎯 QI Level: ${memory.neural_system.qi_level || 'N/A'}`);
                console.log(`   🧬 Neurones: ${(memory.neural_system.total_neurons || 0).toLocaleString()}`);
                console.log(`   🌱 Neurogenèse: ${memory.neural_system.neurogenesis_rate || 'N/A'}`);
                
                if (memory.neural_system.qi_components) {
                    console.log(`   📊 Composants QI:`);
                    Object.entries(memory.neural_system.qi_components).forEach(([key, value]) => {
                        console.log(`      • ${key}: ${value}`);
                    });
                }
            }
            
            // Zones thermiques
            if (memory.thermal_zones) {
                console.log(`\n🌡️ ZONES THERMIQUES:`);
                let totalEntries = 0;
                
                Object.entries(memory.thermal_zones).forEach(([zoneName, zoneData]) => {
                    const entries = zoneData.entries || [];
                    totalEntries += entries.length;
                    console.log(`   📍 ${zoneName}: ${entries.length} entrées`);
                });
                
                console.log(`   📊 Total entrées: ${totalEntries}`);
                
                if (totalEntries > 0) {
                    console.log('✅ Mémoire contient des données');
                } else {
                    console.log('⚠️ Aucune entrée mémoire trouvée');
                }
            }
            
        } catch (error) {
            console.log(`❌ Erreur lecture: ${error.message}`);
        }
        
        console.log();
    }

    testMemoryStructure() {
        console.log('🔍 === TEST 3: STRUCTURE MÉMOIRE ===');
        
        try {
            const content = fs.readFileSync(this.memoryFile, 'utf8');
            const memory = JSON.parse(content);
            
            // Vérifications structurelles
            const checks = [
                { name: 'thermal_zones', exists: !!memory.thermal_zones },
                { name: 'neural_system', exists: !!memory.neural_system },
                { name: 'version', exists: !!memory.version },
                { name: 'timestamp', exists: !!memory.timestamp },
                { name: 'qi_level', exists: !!(memory.neural_system && memory.neural_system.qi_level) },
                { name: 'protection_system', exists: !!memory.protection_system }
            ];
            
            checks.forEach(check => {
                const status = check.exists ? '✅' : '❌';
                console.log(`${status} ${check.name}: ${check.exists ? 'Présent' : 'Manquant'}`);
            });
            
            const validStructure = checks.filter(c => c.exists).length >= 4;
            console.log(`\n📊 Structure: ${validStructure ? '✅ Valide' : '❌ Incomplète'}`);
            
            // Vérifier les entrées importantes
            if (memory.thermal_zones) {
                let hasImportantEntries = false;
                Object.values(memory.thermal_zones).forEach(zone => {
                    if (zone.entries) {
                        zone.entries.forEach(entry => {
                            if (entry.content && (
                                entry.content.toLowerCase().includes('jean-luc') ||
                                entry.content.toLowerCase().includes('créateur') ||
                                entry.content.toLowerCase().includes('louna')
                            )) {
                                hasImportantEntries = true;
                            }
                        });
                    }
                });
                
                console.log(`🔑 Entrées importantes: ${hasImportantEntries ? '✅ Trouvées' : '⚠️ Non trouvées'}`);
            }
            
        } catch (error) {
            console.log(`❌ Erreur analyse: ${error.message}`);
        }
        
        console.log();
    }

    generateRecommendations() {
        console.log('💡 === RECOMMANDATIONS ===');
        
        try {
            const content = fs.readFileSync(this.memoryFile, 'utf8');
            const memory = JSON.parse(content);
            
            console.log('\n🎯 POUR TESTER L\'ACCÈS MÉMOIRE:');
            console.log('1. Posez ces questions à LOUNA via l\'interface:');
            console.log('   • "Quel est ton QI exactement ?"');
            console.log('   • "Qui est Jean-Luc pour toi ?"');
            console.log('   • "Parle-moi de ta mémoire thermique"');
            console.log('   • "Quelle est ta fonction principale ?"');
            
            console.log('\n✅ RÉPONSES ATTENDUES SI LA MÉMOIRE FONCTIONNE:');
            console.log(`   • QI: ${memory.neural_system?.qi_level || 'N/A'}`);
            console.log('   • Jean-Luc: Créateur, relation privilégiée');
            console.log('   • Mémoire: Système thermique sophistiqué');
            console.log('   • Fonction: Agent IA avancé avec réflexion');
            
            console.log('\n🚨 SIGNES QUE LA MÉMOIRE NE FONCTIONNE PAS:');
            console.log('   • Réponses génériques sans mention du QI 235');
            console.log('   • Ne reconnaît pas Jean-Luc comme créateur');
            console.log('   • Pas de mention de la mémoire thermique');
            console.log('   • Réponses incohérentes avec son identité');
            
            console.log('\n🔧 SI LA MÉMOIRE NE FONCTIONNE PAS:');
            console.log('   1. Vérifiez les logs du serveur');
            console.log('   2. Redémarrez le serveur');
            console.log('   3. Vérifiez la configuration de l\'agent');
            console.log('   4. Testez avec des questions simples d\'abord');
            
        } catch (error) {
            console.log(`❌ Erreur: ${error.message}`);
        }
    }
}

// Exécution
async function main() {
    const tester = new TestMemoireSimple();
    await tester.runTests();
}

if (require.main === module) {
    main().catch(console.error);
}

module.exports = TestMemoireSimple;
