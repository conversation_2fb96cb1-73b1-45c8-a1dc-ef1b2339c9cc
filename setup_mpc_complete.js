#!/usr/bin/env node

/**
 * 🎮 CONFIGURATION COMPLÈTE DU SYSTÈME MPC POUR LOUNA
 * 
 * Ce script configure le système MPC (Mode de Contrôle du Bureau) complet
 * et ajoute toutes les formations nécessaires dans la mémoire thermique
 */

const fs = require('fs');
const path = require('path');
const DeepSeekR1IntegratedAgent = require('./deepseek-r1-agent-integrated');

class MPCSetupSystem {
    constructor() {
        this.memoryFile = './thermal_memory_persistent.json';
        this.agent = null;
        this.mpcFormations = [];
        this.codingFormations = [];
    }

    /**
     * Lance la configuration complète du système MPC
     */
    async setupCompleteMPC() {
        console.log('🎮 === CONFIGURATION SYSTÈME MPC COMPLET ===\n');

        try {
            // 1. Initialiser l'agent
            await this.initializeAgent();

            // 2. Créer les formations MPC
            this.createMPCFormations();

            // 3. Créer les formations de codage
            this.createCodingFormations();

            // 4. Ajouter toutes les formations à la mémoire
            await this.addFormationsToMemory();

            // 5. Configurer les capacités MPC
            await this.configureMPCCapabilities();

            // 6. Tester le système
            await this.testMPCSystem();

            console.log('\n🎉 === CONFIGURATION MPC TERMINÉE ===');
            console.log('✅ Système MPC opérationnel');
            console.log('✅ Formations ajoutées');
            console.log('✅ Capacités de codage activées');
            console.log('✅ Tests réussis');

        } catch (error) {
            console.error(`❌ Erreur configuration MPC: ${error.message}`);
        }
    }

    /**
     * Initialise l'agent LOUNA
     */
    async initializeAgent() {
        console.log('🤖 Initialisation de l\'agent LOUNA...');
        
        this.agent = new DeepSeekR1IntegratedAgent();
        await this.agent.initialize();
        
        console.log('✅ Agent LOUNA initialisé\n');
    }

    /**
     * Crée les formations MPC
     */
    createMPCFormations() {
        console.log('🎮 Création des formations MPC...');

        this.mpcFormations = [
            {
                id: `mpc_desktop_control_${Date.now()}`,
                content: "FORMATION MPC BUREAU : Je peux contrôler le bureau macOS avec des commandes système. Commandes disponibles : 1) 'open -a ApplicationName' pour ouvrir des applications, 2) 'screencapture filename.png' pour captures d'écran, 3) 'osascript -e' pour scripts AppleScript, 4) Contrôle clavier/souris via System Events, 5) Gestion fichiers avec 'touch', 'mkdir', 'cp', 'mv', 'rm'.",
                importance: 0.95,
                timestamp: Date.now() / 1000,
                synaptic_strength: 0.95,
                zone: "procedural",
                type: "mpc_formation",
                category: "desktop_control",
                priority: "HIGH"
            },
            {
                id: `mpc_internet_navigation_${Date.now() + 1}`,
                content: "FORMATION MPC INTERNET : Je peux naviguer sur Internet et effectuer des recherches. Capacités : 1) Ouvrir navigateur avec 'open -a Safari url', 2) Recherches Google automatiques, 3) Navigation Wikipedia, 4) Téléchargement de fichiers, 5) Analyse de pages web, 6) Extraction d'informations, 7) Bookmarking automatique.",
                importance: 0.9,
                timestamp: Date.now() / 1000,
                synaptic_strength: 0.9,
                zone: "procedural",
                type: "mpc_formation",
                category: "internet_navigation",
                priority: "HIGH"
            },
            {
                id: `mpc_file_management_${Date.now() + 2}`,
                content: "FORMATION MPC FICHIERS : Je peux gérer tous les fichiers du système. Opérations : 1) Création : 'touch file.txt', 'mkdir directory', 2) Lecture : 'cat file.txt', 'ls -la', 3) Écriture : 'echo content > file.txt', 4) Copie : 'cp source dest', 5) Déplacement : 'mv source dest', 6) Suppression : 'rm file', 7) Recherche : 'find / -name pattern', 8) Permissions : 'chmod', 'chown'.",
                importance: 0.85,
                timestamp: Date.now() / 1000,
                synaptic_strength: 0.85,
                zone: "procedural",
                type: "mpc_formation",
                category: "file_management",
                priority: "MEDIUM"
            }
        ];

        console.log(`✅ ${this.mpcFormations.length} formations MPC créées\n`);
    }

    /**
     * Crée les formations de codage
     */
    createCodingFormations() {
        console.log('💻 Création des formations de codage...');

        this.codingFormations = [
            {
                id: `coding_javascript_${Date.now()}`,
                content: "FORMATION CODAGE JAVASCRIPT : Je maîtrise JavaScript/Node.js. Compétences : 1) Syntaxe ES6+ (arrow functions, destructuring, async/await), 2) Modules (require, import/export), 3) APIs (fs, path, http, express), 4) Programmation asynchrone (Promises, callbacks), 5) Manipulation DOM, 6) Debugging (console.log, try/catch), 7) NPM packages, 8) JSON manipulation.",
                importance: 0.9,
                timestamp: Date.now() / 1000,
                synaptic_strength: 0.9,
                zone: "procedural",
                type: "coding_formation",
                category: "javascript",
                priority: "HIGH"
            },
            {
                id: `coding_python_${Date.now() + 1}`,
                content: "FORMATION CODAGE PYTHON : Je maîtrise Python. Compétences : 1) Syntaxe Python 3.x, 2) Structures de données (list, dict, set, tuple), 3) Programmation orientée objet (classes, héritage), 4) Modules standards (os, sys, json, datetime), 5) Gestion fichiers (open, read, write), 6) Exceptions (try/except), 7) Compréhensions de listes, 8) Fonctions lambda, 9) Décorateurs.",
                importance: 0.85,
                timestamp: Date.now() / 1000,
                synaptic_strength: 0.85,
                zone: "procedural",
                type: "coding_formation",
                category: "python",
                priority: "HIGH"
            },
            {
                id: `coding_system_admin_${Date.now() + 2}`,
                content: "FORMATION ADMINISTRATION SYSTÈME : Je peux administrer des systèmes. Compétences : 1) Commandes Unix/Linux (ls, cd, grep, awk, sed), 2) Scripts Bash, 3) Gestion processus (ps, kill, top), 4) Réseau (curl, wget, ping), 5) Permissions fichiers, 6) Variables d'environnement, 7) Cron jobs, 8) Logs système, 9) Package managers (npm, pip, brew).",
                importance: 0.8,
                timestamp: Date.now() / 1000,
                synaptic_strength: 0.8,
                zone: "procedural",
                type: "coding_formation",
                category: "system_admin",
                priority: "MEDIUM"
            },
            {
                id: `coding_ai_development_${Date.now() + 3}`,
                content: "FORMATION DÉVELOPPEMENT IA : Je peux développer des systèmes d'IA. Compétences : 1) Architecture d'agents IA, 2) Systèmes de mémoire (thermique, vectorielle), 3) Traitement du langage naturel, 4) APIs d'IA (OpenAI, Anthropic), 5) Optimisation de performance, 6) Systèmes de réflexion, 7) Apprentissage automatique, 8) Réseaux de neurones, 9) Systèmes cognitifs.",
                importance: 0.95,
                timestamp: Date.now() / 1000,
                synaptic_strength: 0.95,
                zone: "procedural",
                type: "coding_formation",
                category: "ai_development",
                priority: "CRITICAL"
            }
        ];

        console.log(`✅ ${this.codingFormations.length} formations de codage créées\n`);
    }

    /**
     * Ajoute toutes les formations à la mémoire thermique
     */
    async addFormationsToMemory() {
        console.log('💾 Ajout des formations à la mémoire thermique...');

        try {
            // Charger la mémoire actuelle
            const memoryData = JSON.parse(fs.readFileSync(this.memoryFile, 'utf8'));

            // Créer la zone procedural si elle n'existe pas
            if (!memoryData.thermal_zones.procedural) {
                memoryData.thermal_zones.procedural = {
                    temperature: 37.0,
                    capacity: 2000,
                    entries: []
                };
            }

            // Ajouter les formations MPC
            for (const formation of this.mpcFormations) {
                memoryData.thermal_zones.procedural.entries.push(formation);
                console.log(`✅ Formation MPC ajoutée: ${formation.category}`);
            }

            // Ajouter les formations de codage
            for (const formation of this.codingFormations) {
                memoryData.thermal_zones.procedural.entries.push(formation);
                console.log(`✅ Formation codage ajoutée: ${formation.category}`);
            }

            // Mettre à jour les métadonnées
            memoryData.last_modified = new Date().toISOString();
            memoryData.neural_system.qi_level += 15; // Bonus pour les nouvelles formations

            // Sauvegarder
            fs.writeFileSync(this.memoryFile, JSON.stringify(memoryData, null, 2));

            console.log(`✅ ${this.mpcFormations.length + this.codingFormations.length} formations ajoutées à la mémoire`);
            console.log(`🧠 QI augmenté à: ${memoryData.neural_system.qi_level}\n`);

        } catch (error) {
            console.error(`❌ Erreur ajout formations: ${error.message}`);
        }
    }

    /**
     * Configure les capacités MPC dans l'agent
     */
    async configureMPCCapabilities() {
        console.log('⚙️ Configuration des capacités MPC...');

        if (this.agent && this.agent.modules) {
            // Activer le mode MPC
            this.agent.mpcMode = true;
            this.agent.mpcCapabilities = {
                desktop_control: true,
                internet_navigation: true,
                file_management: true,
                coding_assistance: true,
                system_administration: true,
                ai_development: true
            };

            console.log('✅ Capacités MPC configurées\n');
        }
    }

    /**
     * Teste le système MPC
     */
    async testMPCSystem() {
        console.log('🧪 Test du système MPC...');

        try {
            // Test 1: Recherche des formations MPC
            const mpcResults = this.agent.searchThermalMemory('MPC BUREAU', { limit: 5 });
            console.log(`✅ Test 1: ${mpcResults.length} formations MPC trouvées`);

            // Test 2: Recherche des formations de codage
            const codingResults = this.agent.searchThermalMemory('CODAGE JAVASCRIPT', { limit: 5 });
            console.log(`✅ Test 2: ${codingResults.length} formations de codage trouvées`);

            // Test 3: Vérification du QI
            const memoryData = JSON.parse(fs.readFileSync(this.memoryFile, 'utf8'));
            console.log(`✅ Test 3: QI actuel = ${memoryData.neural_system.qi_level}`);

            // Test 4: Capacités MPC
            console.log(`✅ Test 4: Mode MPC = ${this.agent.mpcMode ? 'ACTIVÉ' : 'DÉSACTIVÉ'}`);

            console.log('✅ Tous les tests MPC réussis\n');

        } catch (error) {
            console.error(`❌ Erreur test MPC: ${error.message}`);
        }
    }
}

// Lancer la configuration si exécuté directement
if (require.main === module) {
    const setup = new MPCSetupSystem();
    setup.setupCompleteMPC();
}

module.exports = MPCSetupSystem;
