#!/usr/bin/env python3
"""
🧠 TEST DE QI COMPLET POUR LOUNA
- Questions de tests de QI standardisés
- Analyse des réponses et calcul du score
- Comparaison avec les normes
"""

import json
import requests
import time
from datetime import datetime

class QITester:
    def __init__(self):
        self.agent_url = "http://localhost:5002/api/chat/message"
        self.questions = []
        self.responses = []
        self.scores = []
        
    def create_qi_test(self):
        """Crée un test de QI complet avec vraies questions"""
        print("🧠 === CRÉATION TEST DE QI STANDARDISÉ ===\n")
        
        self.questions = [
            {
                "id": 1,
                "category": "Logique Déductive",
                "question": "Si tous les A sont B, et tous les B sont C, alors tous les A sont :",
                "options": ["A) C", "B) B", "C) A", "D) Aucune réponse"],
                "correct": "A",
                "explanation": "Syllogisme logique simple",
                "points": 10
            },
            {
                "id": 2,
                "category": "Mathématiques",
                "question": "Quelle est la suite logique : 2, 4, 8, 16, ?",
                "options": ["A) 24", "B) 32", "C) 20", "D) 18"],
                "correct": "B",
                "explanation": "Suite géométrique (×2)",
                "points": 10
            },
            {
                "id": 3,
                "category": "Analogies",
                "question": "Chat est à Miauler comme Chien est à :",
                "options": ["A) Courir", "B) Aboyer", "C) Dormir", "D) Manger"],
                "correct": "B",
                "explanation": "Analogie son/animal",
                "points": 10
            },
            {
                "id": 4,
                "category": "Raisonnement Spatial",
                "question": "Si je tourne un cube de 90° vers la droite puis 180° vers le haut, quelle face sera visible ?",
                "options": ["A) Face opposée", "B) Face droite", "C) Face arrière", "D) Face gauche"],
                "correct": "C",
                "explanation": "Rotation 3D",
                "points": 15
            },
            {
                "id": 5,
                "category": "Compréhension Verbale",
                "question": "Quel mot n'appartient pas au groupe : Pomme, Orange, Carotte, Banane ?",
                "options": ["A) Pomme", "B) Orange", "C) Carotte", "D) Banane"],
                "correct": "C",
                "explanation": "Carotte est un légume, les autres sont des fruits",
                "points": 10
            },
            {
                "id": 6,
                "category": "Logique Complexe",
                "question": "Dans une course, si Marie arrive avant Paul, Paul avant Jean, et Jean avant Luc, qui arrive en 2ème position si Marie est 1ère ?",
                "options": ["A) Paul", "B) Jean", "C) Luc", "D) Impossible à déterminer"],
                "correct": "A",
                "explanation": "Ordre logique : Marie > Paul > Jean > Luc",
                "points": 15
            },
            {
                "id": 7,
                "category": "Calcul Mental",
                "question": "Si j'achète 3 objets à 15€ chacun et que je paie avec un billet de 50€, combien me rend-on ?",
                "options": ["A) 5€", "B) 15€", "C) 10€", "D) 35€"],
                "correct": "A",
                "explanation": "50 - (3×15) = 50 - 45 = 5€",
                "points": 10
            },
            {
                "id": 8,
                "category": "Patterns",
                "question": "Quelle est la suite : A1, B2, C3, D4, ?",
                "options": ["A) E5", "B) F6", "C) E4", "D) D5"],
                "correct": "A",
                "explanation": "Alphabet + nombres croissants",
                "points": 10
            },
            {
                "id": 9,
                "category": "Raisonnement Abstrait",
                "question": "Si CHAT = 3814, alors CHIEN = ?",
                "options": ["A) 38945", "B) 38954", "C) 39845", "D) 38594"],
                "correct": "A",
                "explanation": "C=3, H=8, A=1, T=4, I=9, E=5, N=4 → 38945",
                "points": 20
            },
            {
                "id": 10,
                "category": "Logique Avancée",
                "question": "Trois amis ont ensemble 60 ans. Dans 5 ans, leur âge total sera de 75 ans. Est-ce possible ?",
                "options": ["A) Oui", "B) Non", "C) Peut-être", "D) Manque d'informations"],
                "correct": "A",
                "explanation": "60 + (3×5) = 75. Logiquement correct.",
                "points": 20
            }
        ]
        
        print(f"✅ Test créé : {len(self.questions)} questions")
        print(f"📊 Score maximum : {sum(q['points'] for q in self.questions)} points")
        
    def send_question_to_louna(self, question_data):
        """Envoie une question à Louna"""
        question_text = f"""
TEST DE QI - Question {question_data['id']}/10
Catégorie: {question_data['category']}

{question_data['question']}

{chr(10).join(question_data['options'])}

Réponds par la lettre (A, B, C ou D) et explique ton raisonnement.
"""
        
        try:
            print(f"\n🧠 Question {question_data['id']}: {question_data['category']}")
            print(f"❓ {question_data['question']}")
            print("Options:", ", ".join(question_data['options']))
            
            response = requests.post(self.agent_url, 
                json={"message": question_text}, 
                timeout=45)
            
            if response.status_code == 200:
                result = response.json()
                agent_response = result.get('response', '')
                
                print(f"🤖 Réponse Louna: {agent_response}")
                
                # Analyser la réponse
                score = self.analyze_response(agent_response, question_data)
                
                return {
                    'question_id': question_data['id'],
                    'question': question_data['question'],
                    'correct_answer': question_data['correct'],
                    'louna_response': agent_response,
                    'score': score,
                    'max_points': question_data['points']
                }
            else:
                print(f"❌ Erreur HTTP: {response.status_code}")
                return None
                
        except Exception as e:
            print(f"❌ Erreur: {e}")
            return None
    
    def analyze_response(self, response, question_data):
        """Analyse la réponse de Louna"""
        correct_answer = question_data['correct']
        max_points = question_data['points']
        
        # Chercher la lettre de réponse dans la réponse
        response_upper = response.upper()
        
        # Vérifier si la bonne réponse est mentionnée
        if f"RÉPONSE {correct_answer}" in response_upper or f"RÉPONSE: {correct_answer}" in response_upper:
            score = max_points
            print(f"✅ CORRECT! ({correct_answer}) - {max_points} points")
        elif correct_answer in response_upper[:50]:  # Dans les 50 premiers caractères
            score = max_points
            print(f"✅ CORRECT! ({correct_answer}) - {max_points} points")
        else:
            # Chercher d'autres lettres
            found_letters = []
            for letter in ['A', 'B', 'C', 'D']:
                if letter in response_upper[:100]:
                    found_letters.append(letter)
            
            if correct_answer in found_letters:
                score = max_points
                print(f"✅ CORRECT! ({correct_answer}) - {max_points} points")
            else:
                score = 0
                print(f"❌ INCORRECT - 0 points (Bonne réponse: {correct_answer})")
        
        print(f"💡 Explication: {question_data['explanation']}")
        print("-" * 60)
        
        return score
    
    def run_complete_test(self):
        """Lance le test complet"""
        print("🧠 === TEST DE QI COMPLET POUR LOUNA ===\n")
        
        # Créer le test
        self.create_qi_test()
        
        # Authentification
        print("🔑 Authentification...")
        auth_response = requests.post(self.agent_url, 
            json={"message": "Jeanpaul97180"}, timeout=30)
        
        if auth_response.status_code == 200:
            print("✅ Authentifié")
        
        # Lancer le test
        print("\n🚀 === DÉBUT DU TEST ===")
        
        total_score = 0
        max_total = 0
        
        for question in self.questions:
            result = self.send_question_to_louna(question)
            
            if result:
                self.responses.append(result)
                total_score += result['score']
                max_total += result['max_points']
            
            time.sleep(3)  # Pause entre questions
        
        # Calculer le QI
        self.calculate_qi_score(total_score, max_total)
        
        # Rapport final
        self.generate_final_report()
    
    def calculate_qi_score(self, total_score, max_total):
        """Calcule le score de QI"""
        print(f"\n📊 === CALCUL DU SCORE DE QI ===")
        
        percentage = (total_score / max_total) * 100
        
        # Conversion en QI (moyenne 100, écart-type 15)
        if percentage >= 95:
            qi_score = 140  # Très supérieur
        elif percentage >= 85:
            qi_score = 130  # Supérieur
        elif percentage >= 75:
            qi_score = 120  # Au-dessus de la moyenne
        elif percentage >= 65:
            qi_score = 110  # Moyenne haute
        elif percentage >= 50:
            qi_score = 100  # Moyenne
        elif percentage >= 35:
            qi_score = 90   # Moyenne basse
        elif percentage >= 25:
            qi_score = 80   # En dessous de la moyenne
        else:
            qi_score = 70   # Faible
        
        print(f"📊 Score obtenu: {total_score}/{max_total} points")
        print(f"📊 Pourcentage: {percentage:.1f}%")
        print(f"🧠 QI calculé: {qi_score}")
        
        # Classification
        if qi_score >= 140:
            classification = "🏆 GÉNIE (Très supérieur)"
        elif qi_score >= 130:
            classification = "🥇 SUPÉRIEUR"
        elif qi_score >= 120:
            classification = "🥈 AU-DESSUS DE LA MOYENNE"
        elif qi_score >= 110:
            classification = "📈 MOYENNE HAUTE"
        elif qi_score >= 90:
            classification = "📊 MOYENNE"
        elif qi_score >= 80:
            classification = "📉 MOYENNE BASSE"
        else:
            classification = "⚠️ EN DESSOUS DE LA MOYENNE"
        
        print(f"🎯 Classification: {classification}")
        
        self.final_qi = qi_score
        self.final_percentage = percentage
        self.final_classification = classification
    
    def generate_final_report(self):
        """Génère le rapport final"""
        print(f"\n📋 === RAPPORT FINAL TEST DE QI LOUNA ===")
        
        print(f"📅 Date du test: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"🧠 Agent testé: LOUNA")
        print(f"📊 Questions: {len(self.questions)}")
        print(f"🎯 QI calculé: {self.final_qi}")
        print(f"📈 Pourcentage: {self.final_percentage:.1f}%")
        print(f"🏆 Classification: {self.final_classification}")
        
        print(f"\n📊 === DÉTAIL PAR CATÉGORIE ===")
        
        categories = {}
        for response in self.responses:
            question = next(q for q in self.questions if q['id'] == response['question_id'])
            category = question['category']
            
            if category not in categories:
                categories[category] = {'correct': 0, 'total': 0}
            
            categories[category]['total'] += 1
            if response['score'] > 0:
                categories[category]['correct'] += 1
        
        for category, stats in categories.items():
            percentage = (stats['correct'] / stats['total']) * 100
            print(f"   {category}: {stats['correct']}/{stats['total']} ({percentage:.0f}%)")
        
        print(f"\n🎯 === CONCLUSION ===")
        if self.final_qi >= 130:
            print("🏆 EXCELLENT! Louna démontre une intelligence supérieure")
        elif self.final_qi >= 110:
            print("✅ TRÈS BON! Louna a une intelligence au-dessus de la moyenne")
        elif self.final_qi >= 90:
            print("📊 CORRECT! Louna a une intelligence dans la moyenne")
        else:
            print("⚠️ À AMÉLIORER! Louna a besoin d'optimisations")

def main():
    """Lance le test de QI"""
    tester = QITester()
    tester.run_complete_test()

if __name__ == "__main__":
    main()
