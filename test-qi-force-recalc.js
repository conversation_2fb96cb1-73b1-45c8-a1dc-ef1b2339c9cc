/**
 * 🧠 TEST QI AVEC RECALCULATION FORCÉE
 * 
 * Force le recalcul du QI pour voir le vrai résultat avec 38 entrées
 */

const { UnifiedQISystem } = require('./unified-qi-system');

async function testQIWithForceRecalc() {
    console.log('🧠 === TEST QI AVEC RECALCULATION FORCÉE ===\n');
    
    try {
        const qiSystem = new UnifiedQISystem();
        
        console.log('🔄 Force recalculation du QI...');
        const result = await qiSystem.forceRecalculation();
        
        console.log('\n🎯 === RÉSULTATS AVEC MÉMOIRE ENRICHIE ===');
        console.log(`   QI Total: ${result.total}`);
        console.log(`   Classification: ${result.classification}`);
        console.log(`   Méthodologie: ${result.methodology}`);
        
        console.log('\n📊 === COMPOSANTS DÉTAILLÉS ===');
        for (const [component, value] of Object.entries(result.components)) {
            console.log(`   ${component}: ${value}`);
        }
        
        console.log('\n📈 === DÉTAIL DU CALCUL ===');
        for (const [description, value] of Object.entries(result.breakdown)) {
            console.log(`   ${description}: ${value}`);
        }
        
        // Calculer l'amélioration
        const oldQI = 201; // QI avec 18 entrées
        const improvement = result.total - oldQI;
        
        console.log('\n📈 === AMÉLIORATION ===');
        console.log(`   QI avant (18 entrées): ${oldQI}`);
        console.log(`   QI après (38 entrées): ${result.total}`);
        console.log(`   Amélioration: ${improvement > 0 ? '+' : ''}${improvement} points`);
        console.log(`   Pourcentage: ${improvement > 0 ? '+' : ''}${((improvement / oldQI) * 100).toFixed(1)}%`);
        
        if (result.total > oldQI) {
            console.log('\n🎉 SUCCÈS ! Le QI a augmenté avec la mémoire enrichie !');
        } else {
            console.log('\n🤔 Le QI n\'a pas changé. Vérification nécessaire...');
        }
        
        return result;
        
    } catch (error) {
        console.error('❌ Erreur test QI:', error.message);
        return null;
    }
}

// Exécuter le test
if (require.main === module) {
    testQIWithForceRecalc()
        .then(result => {
            if (result) {
                console.log(`\n✅ Test terminé. QI final: ${result.total}`);
                process.exit(0);
            } else {
                console.log('\n❌ Test échoué');
                process.exit(1);
            }
        })
        .catch(error => {
            console.error('❌ Erreur fatale:', error.message);
            process.exit(1);
        });
}

module.exports = { testQIWithForceRecalc };
