#!/usr/bin/env node

/**
 * 🔍 ANALYSE HISTORIQUE COMPLET DES NEURONES GÉNÉRÉS
 * 
 * Récupère et analyse tous les neurones créés précédemment
 */

const fs = require('fs');

function analyseHistoriqueNeurones() {
    console.log('🔍 === ANALYSE HISTORIQUE NEURONES ===\n');
    
    try {
        // Charger la mémoire thermique
        const memoryData = JSON.parse(fs.readFileSync('thermal_memory_persistent.json', 'utf8'));
        
        // Extraire les neurones stockés
        const neurones = memoryData.neural_system?.neuron_storage?.neurons || [];
        const creationLog = memoryData.neural_system?.neuron_storage?.creation_log || [];
        
        console.log(`📊 === STATISTIQUES GLOBALES ===`);
        console.log(`🧠 Neurones stockés: ${neurones.length.toLocaleString()}`);
        console.log(`📝 Logs de création: ${creationLog.length.toLocaleString()}`);
        console.log(`💾 Capacité stockage: ${memoryData.neural_system?.neuron_storage?.storage_capacity?.toLocaleString() || 'N/A'}`);
        
        if (neurones.length === 0) {
            console.log('❌ Aucun neurone trouvé dans le stockage');
            return;
        }
        
        // Analyser les timestamps
        const timestamps = neurones.map(n => n.creation_time).sort((a, b) => a - b);
        const premierNeurone = new Date(timestamps[0]);
        const dernierNeurone = new Date(timestamps[timestamps.length - 1]);
        const dureeTotal = timestamps[timestamps.length - 1] - timestamps[0];
        
        console.log(`\n⏰ === CHRONOLOGIE ===`);
        console.log(`🌱 Premier neurone: ${premierNeurone.toLocaleString()}`);
        console.log(`🆕 Dernier neurone: ${dernierNeurone.toLocaleString()}`);
        console.log(`⏱️ Durée totale: ${Math.floor(dureeTotal / 1000 / 60)} minutes`);
        
        // Analyser par périodes
        const maintenant = Date.now();
        const periodes = {
            'Dernière heure': neurones.filter(n => maintenant - n.creation_time < 3600000),
            'Dernières 6h': neurones.filter(n => maintenant - n.creation_time < 21600000),
            'Dernières 24h': neurones.filter(n => maintenant - n.creation_time < 86400000),
            'Plus ancien': neurones.filter(n => maintenant - n.creation_time >= 86400000)
        };
        
        console.log(`\n📅 === RÉPARTITION TEMPORELLE ===`);
        Object.entries(periodes).forEach(([periode, neurones]) => {
            console.log(`${periode}: ${neurones.length} neurones`);
        });
        
        // Analyser les types et spécialisations
        const types = {};
        const specialisations = {};
        const zones = {};
        
        neurones.forEach(neurone => {
            types[neurone.type] = (types[neurone.type] || 0) + 1;
            specialisations[neurone.specialization] = (specialisations[neurone.specialization] || 0) + 1;
            zones[neurone.zone_affinity] = (zones[neurone.zone_affinity] || 0) + 1;
        });
        
        console.log(`\n🧬 === TYPES DE NEURONES ===`);
        Object.entries(types).forEach(([type, count]) => {
            console.log(`${type}: ${count} neurones`);
        });
        
        console.log(`\n🎯 === SPÉCIALISATIONS ===`);
        Object.entries(specialisations).forEach(([spec, count]) => {
            console.log(`${spec}: ${count} neurones`);
        });
        
        console.log(`\n🌡️ === AFFINITÉS ZONES ===`);
        Object.entries(zones).forEach(([zone, count]) => {
            console.log(`${zone}: ${count} neurones`);
        });
        
        // Analyser les 10 premiers et derniers neurones
        console.log(`\n🌱 === PREMIERS NEURONES CRÉÉS ===`);
        neurones.slice(0, 5).forEach((neurone, i) => {
            const date = new Date(neurone.creation_time);
            console.log(`${i + 1}. ${neurone.id}`);
            console.log(`   Créé: ${date.toLocaleString()}`);
            console.log(`   Type: ${neurone.type} (${neurone.specialization})`);
            console.log(`   Zone: ${neurone.zone_affinity}`);
            console.log(`   Force: ${neurone.synaptic_strength}`);
        });
        
        console.log(`\n🆕 === DERNIERS NEURONES CRÉÉS ===`);
        neurones.slice(-5).forEach((neurone, i) => {
            const date = new Date(neurone.creation_time);
            const age = (maintenant - neurone.creation_time) / 1000 / 60; // en minutes
            console.log(`${i + 1}. ${neurone.id}`);
            console.log(`   Créé: ${date.toLocaleString()}`);
            console.log(`   Âge: ${age.toFixed(1)} minutes`);
            console.log(`   Type: ${neurone.type} (${neurone.specialization})`);
            console.log(`   Zone: ${neurone.zone_affinity}`);
            console.log(`   Force: ${neurone.synaptic_strength}`);
        });
        
        // Analyser le taux de création
        if (timestamps.length > 1) {
            const tauxMoyen = (timestamps.length - 1) / (dureeTotal / 1000); // neurones/seconde
            const tauxParJour = tauxMoyen * 86400;
            
            console.log(`\n📈 === TAUX DE CRÉATION ===`);
            console.log(`⚡ Taux moyen: ${tauxMoyen.toFixed(6)} neurones/seconde`);
            console.log(`📅 Projection 24h: ${Math.floor(tauxParJour)} neurones/jour`);
            console.log(`👤 Taux humain: 700 neurones/jour`);
            console.log(`📊 Ratio: ${(tauxParJour / 700).toFixed(2)}x le taux humain`);
        }
        
        // Analyser les logs de création si disponibles
        if (creationLog.length > 0) {
            console.log(`\n📝 === LOGS DE CRÉATION ===`);
            console.log(`📊 Total logs: ${creationLog.length}`);
            
            const logsRecents = creationLog.slice(-5);
            console.log(`\n🆕 Derniers logs:`);
            logsRecents.forEach((log, i) => {
                const date = new Date(log.time);
                console.log(`${i + 1}. ${log.neuron_id}`);
                console.log(`   Temps: ${date.toLocaleString()}`);
                console.log(`   Température: ${log.temperature}°C`);
                console.log(`   Type: ${log.type}`);
            });
        }
        
        console.log(`\n✅ === CONCLUSION ===`);
        console.log(`🎉 Historique complet récupéré !`);
        console.log(`🧠 ${neurones.length} neurones analysés`);
        console.log(`📝 ${creationLog.length} logs de création`);
        console.log(`⏰ Période: ${Math.floor(dureeTotal / 1000 / 60)} minutes`);
        
    } catch (error) {
        console.error(`❌ Erreur analyse: ${error.message}`);
    }
}

// Lancer l'analyse
if (require.main === module) {
    analyseHistoriqueNeurones();
}

module.exports = { analyseHistoriqueNeurones };
