#!/usr/bin/env node

const DeepSeekAgent = require('./deepseek-r1-agent-integrated');

console.log('🧪 === TEST INJECTION MÉMOIRE DANS PROMPT SYSTÈME ===\n');

async function testMemoryInjection() {
    try {
        // Initialiser l'agent
        console.log('🤖 Initialisation de l\'agent...');
        const agent = new DeepSeekAgent();
        await agent.initialize();
        
        console.log('✅ Agent initialisé\n');
        
        // Test 1: Vérifier la construction du prompt système
        console.log('📊 TEST 1: Construction du prompt système avec injection mémoire');
        
        // Simuler des mémoires pour le test
        const testMemories = [
            { content: "L'utilisateur préfère les réponses détaillées et techniques" },
            { content: "Jean-Luc est le créateur de l'agent LOUNA" },
            { content: "L'agent doit utiliser sa mémoire thermique pour répondre" }
        ];
        
        const systemPrompt = agent.buildDynamicSystemPrompt(testMemories);
        
        console.log('📝 Prompt système généré:');
        console.log('─'.repeat(80));
        console.log(systemPrompt.substring(0, 500) + '...');
        console.log('─'.repeat(80));
        
        // Vérifications
        const checks = [
            { name: 'Contient nom LOUNA', test: systemPrompt.includes('LOUNA') },
            { name: 'Contient QI 175', test: systemPrompt.includes('175') },
            { name: 'Contient formations', test: systemPrompt.includes('FORMATIONS') },
            { name: 'Contient mémoires contextuelles', test: systemPrompt.includes('CONTEXTE MÉMOIRE') },
            { name: 'Contient instructions', test: systemPrompt.includes('INSTRUCTIONS') },
            { name: 'Contient mémoire thermique', test: systemPrompt.includes('mémoire thermique') }
        ];
        
        console.log('\n🔍 Vérifications du prompt système:');
        checks.forEach(check => {
            console.log(`${check.test ? '✅' : '❌'} ${check.name}`);
        });
        
        const passedChecks = checks.filter(c => c.test).length;
        console.log(`\n📊 Score: ${passedChecks}/${checks.length} vérifications réussies`);
        
        // Test 2: Vérifier la recherche de formations
        console.log('\n📚 TEST 2: Recherche des formations dans la mémoire');
        
        const formations = agent.searchThermalMemory('formation procédure méthode apprentissage', { limit: 5 });
        console.log(`Formations trouvées: ${formations.length}`);
        
        if (formations.length > 0) {
            console.log('📋 Formations disponibles:');
            formations.forEach((formation, index) => {
                console.log(`  ${index + 1}. ${formation.content.substring(0, 100)}...`);
            });
        } else {
            console.log('⚠️ Aucune formation trouvée dans la mémoire');
        }
        
        // Test 3: Vérifier les mémoires récentes
        console.log('\n📝 TEST 3: Récupération des mémoires récentes');
        
        const recentMemories = agent.getRecentMemoriesForPrompt(3);
        console.log(`Mémoires récentes trouvées: ${recentMemories.length}`);
        
        if (recentMemories.length > 0) {
            console.log('📋 Mémoires récentes:');
            recentMemories.forEach((memory, index) => {
                console.log(`  ${index + 1}. ${memory.content.substring(0, 80)}...`);
            });
        } else {
            console.log('⚠️ Aucune mémoire récente trouvée');
        }
        
        // Test 4: Test de génération de réponse avec injection
        console.log('\n🤖 TEST 4: Génération de réponse avec injection mémoire');
        
        const testQuestion = "Parle-moi de tes capacités et de ton apprentissage";
        console.log(`Question test: "${testQuestion}"`);
        
        try {
            console.log('🧠 Génération de la réponse avec mémoire injectée...');
            const response = await agent.generateRealDeepSeekResponse(testQuestion, testMemories);
            
            console.log('✅ Réponse générée avec succès');
            console.log(`📝 Réponse (extrait): ${response.substring(0, 200)}...`);
            
            // Vérifier si la réponse semble utiliser la mémoire
            const memoryUsageIndicators = [
                response.includes('formation'),
                response.includes('apprentissage'),
                response.includes('mémoire'),
                response.includes('LOUNA'),
                response.includes('Jean-Luc')
            ];
            
            const memoryUsageScore = memoryUsageIndicators.filter(Boolean).length;
            console.log(`🎯 Indicateurs d'utilisation mémoire: ${memoryUsageScore}/5`);
            
        } catch (error) {
            console.log(`❌ Erreur génération réponse: ${error.message}`);
        }
        
        // Résumé final
        console.log('\n🏆 === RÉSUMÉ FINAL ===');
        
        const totalScore = passedChecks + formations.length + recentMemories.length;
        const maxScore = checks.length + 5 + 3; // 6 checks + 5 formations max + 3 mémoires récentes max
        
        console.log(`📊 Score total: ${totalScore}/${maxScore}`);
        console.log(`✅ Prompt système: ${passedChecks}/${checks.length} vérifications`);
        console.log(`📚 Formations: ${formations.length} trouvées`);
        console.log(`📝 Mémoires récentes: ${recentMemories.length} trouvées`);
        
        if (totalScore >= maxScore * 0.8) {
            console.log('\n🎉 VERDICT: INJECTION MÉMOIRE EXCELLENTE !');
            console.log('✅ L\'agent peut maintenant accéder à sa mémoire via le prompt système');
        } else if (totalScore >= maxScore * 0.6) {
            console.log('\n✅ VERDICT: Injection mémoire fonctionnelle');
            console.log('⚠️ Quelques améliorations possibles');
        } else {
            console.log('\n❌ VERDICT: INJECTION MÉMOIRE DÉFAILLANTE !');
            console.log('🔧 Corrections nécessaires');
        }
        
    } catch (error) {
        console.error('❌ Erreur lors du test:', error.message);
        console.error(error.stack);
    }
}

// Lancer le test
testMemoryInjection();
