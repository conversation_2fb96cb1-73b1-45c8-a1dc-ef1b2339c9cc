/**
 * 🚀 EXEMPLE D'INTÉGRATION DANS MAIN.JS ELECTRON
 * Code à ajouter dans votre fichier main.js existant
 * Créé pour Jean-Paul 97180
 */

// ========================================
// IMPORTS À AJOUTER EN HAUT DE VOTRE MAIN.JS
// ========================================

const { app, BrowserWindow, Menu, ipcMain } = require('electron');
const path = require('path');

// Importer l'intégration du chat LOUNA
const { initializeChatIntegration } = require('./electron-chat-integration.js');

// ========================================
// VARIABLES GLOBALES À AJOUTER
// ========================================

let mainWindow;
let chatIntegration; // Instance de l'intégration chat

// ========================================
// FONCTION DE CRÉATION DE FENÊTRE MODIFIÉE
// ========================================

function createWindow() {
    // Créer la fenêtre principale (votre code existant)
    mainWindow = new BrowserWindow({
        width: 1200,
        height: 800,
        webPreferences: {
            nodeIntegration: true,
            contextIsolation: false,
            enableRemoteModule: true
        },
        icon: path.join(__dirname, 'assets/icon.png'), // Ajustez le chemin
        title: 'LOUNA AI - Interface Complète'
    });

    // Charger votre interface principale existante
    mainWindow.loadFile('interface-originale-complete.html'); // Votre fichier existant

    // ========================================
    // INITIALISER L'INTÉGRATION CHAT LOUNA
    // ========================================
    
    console.log('🚀 Initialisation de l\'intégration chat LOUNA...');
    chatIntegration = initializeChatIntegration(mainWindow);
    
    // Injecter le bouton de chat dans votre interface principale
    mainWindow.webContents.once('dom-ready', () => {
        console.log('💬 Injection du bouton de chat...');
        chatIntegration.injectChatButton(mainWindow.webContents);
    });

    // ========================================
    // MENU AVEC OPTION CHAT
    // ========================================
    
    const menuTemplate = [
        {
            label: 'Fichier',
            submenu: [
                {
                    label: 'Quitter',
                    accelerator: process.platform === 'darwin' ? 'Cmd+Q' : 'Ctrl+Q',
                    click: () => {
                        app.quit();
                    }
                }
            ]
        },
        // NOUVEAU MENU CHAT LOUNA
        {
            label: 'Chat LOUNA',
            submenu: [
                {
                    label: '💬 Ouvrir Interface de Chat',
                    accelerator: 'CmdOrCtrl+Shift+C',
                    click: () => {
                        console.log('📱 Ouverture chat via menu...');
                        chatIntegration.openChatInterface();
                    }
                },
                {
                    label: '❌ Fermer Chat',
                    accelerator: 'CmdOrCtrl+Shift+X',
                    click: () => {
                        chatIntegration.closeChatInterface();
                    }
                },
                { type: 'separator' },
                {
                    label: '🔍 Vérifier Serveur',
                    click: () => {
                        const status = chatIntegration.getServerStatus();
                        const message = status ? 
                            '✅ Serveur de chat disponible' : 
                            '❌ Serveur de chat indisponible';
                        
                        // Afficher dans la console ou une notification
                        console.log(message);
                        
                        // Optionnel : Afficher une boîte de dialogue
                        const { dialog } = require('electron');
                        dialog.showMessageBox(mainWindow, {
                            type: status ? 'info' : 'warning',
                            title: 'Statut du Chat LOUNA',
                            message: message,
                            detail: status ? 
                                'L\'interface de chat est prête à être utilisée.' :
                                'Veuillez démarrer le serveur de chat sur le port 3000.'
                        });
                    }
                },
                {
                    label: '📖 Guide d\'Utilisation',
                    click: () => {
                        // Ouvrir le guide d'utilisation
                        require('electron').shell.openExternal('file://' + path.join(__dirname, 'UTILISATION_LOUNA_INTEGRE.md'));
                    }
                }
            ]
        },
        {
            label: 'Aide',
            submenu: [
                {
                    label: 'À propos',
                    click: () => {
                        const { dialog } = require('electron');
                        dialog.showMessageBox(mainWindow, {
                            type: 'info',
                            title: 'À propos de LOUNA',
                            message: 'LOUNA AI - Agent DeepSeek R1 8B',
                            detail: 'Interface complète avec mémoire thermique QI 404\\nCréé par Jean-Paul 97180'
                        });
                    }
                }
            ]
        }
    ];

    const menu = Menu.buildFromTemplate(menuTemplate);
    Menu.setApplicationMenu(menu);

    // Ouvrir les DevTools en développement
    if (process.env.NODE_ENV === 'development') {
        mainWindow.webContents.openDevTools();
    }

    mainWindow.on('closed', () => {
        mainWindow = null;
    });
}

// ========================================
// GESTIONNAIRES IPC POUR LE CHAT
// ========================================

// Gestionnaire pour ouvrir le chat depuis le renderer
ipcMain.handle('open-chat-interface', async () => {
    console.log('📱 Demande d\'ouverture chat via IPC...');
    if (chatIntegration) {
        return chatIntegration.openChatInterface();
    }
    return null;
});

// Gestionnaire pour vérifier le statut du serveur
ipcMain.handle('check-chat-server', async () => {
    if (chatIntegration) {
        return chatIntegration.getServerStatus();
    }
    return false;
});

// ========================================
// ÉVÉNEMENTS ELECTRON STANDARDS
// ========================================

app.whenReady().then(() => {
    createWindow();

    app.on('activate', () => {
        if (BrowserWindow.getAllWindows().length === 0) {
            createWindow();
        }
    });
});

app.on('window-all-closed', () => {
    if (process.platform !== 'darwin') {
        app.quit();
    }
});

// ========================================
// SCRIPT À INJECTER DANS VOTRE RENDERER
// ========================================

/*
AJOUTEZ CE CODE DANS VOTRE FICHIER HTML PRINCIPAL (interface-originale-complete.html) :

<script>
// API pour communiquer avec Electron
window.electronAPI = {
    openChat: () => {
        if (window.require) {
            const { ipcRenderer } = window.require('electron');
            ipcRenderer.invoke('open-chat-interface');
        }
    },
    
    checkChatServer: async () => {
        if (window.require) {
            const { ipcRenderer } = window.require('electron');
            return await ipcRenderer.invoke('check-chat-server');
        }
        return false;
    }
};

// Fonction pour ajouter le bouton de chat manuellement
function addChatButton() {
    // Vérifier si le bouton existe déjà
    if (document.getElementById('lounaChatBtn')) return;
    
    const chatBtn = document.createElement('button');
    chatBtn.id = 'lounaChat Btn';
    chatBtn.innerHTML = '💬 Chat LOUNA DeepSeek';
    chatBtn.style.cssText = `
        position: fixed;
        bottom: 30px;
        right: 30px;
        background: linear-gradient(135deg, #00d4ff, #0099cc);
        color: white;
        border: none;
        padding: 15px 25px;
        border-radius: 50px;
        cursor: pointer;
        font-weight: bold;
        font-size: 16px;
        z-index: 10000;
        box-shadow: 0 6px 20px rgba(0, 212, 255, 0.4);
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: 10px;
    `;
    
    chatBtn.addEventListener('click', () => {
        console.log('💬 Ouverture du chat LOUNA...');
        window.electronAPI.openChat();
    });
    
    chatBtn.addEventListener('mouseenter', () => {
        chatBtn.style.transform = 'translateY(-3px)';
        chatBtn.style.boxShadow = '0 8px 25px rgba(0, 212, 255, 0.6)';
    });
    
    chatBtn.addEventListener('mouseleave', () => {
        chatBtn.style.transform = 'translateY(0)';
        chatBtn.style.boxShadow = '0 6px 20px rgba(0, 212, 255, 0.4)';
    });
    
    document.body.appendChild(chatBtn);
    console.log('✅ Bouton de chat ajouté');
}

// Ajouter le bouton au chargement de la page
document.addEventListener('DOMContentLoaded', addChatButton);
</script>

*/

// ========================================
// INSTRUCTIONS D'INSTALLATION
// ========================================

/*
POUR INTÉGRER DANS VOTRE APPLICATION ELECTRON :

1. Copiez les fichiers suivants dans votre projet :
   - electron-chat-integration.js
   - electron-chat-button.html (optionnel)

2. Modifiez votre main.js en ajoutant le code ci-dessus

3. Ajoutez le script dans votre interface-originale-complete.html

4. Installez les dépendances si nécessaire :
   npm install electron

5. Testez l'intégration :
   - Démarrez votre application Electron
   - Démarrez le serveur de chat (node chat-interface-server.js)
   - Cliquez sur le bouton "💬 Chat LOUNA DeepSeek"

6. Vérifiez que tout fonctionne :
   - Le bouton apparaît en bas à droite
   - Le menu "Chat LOUNA" est disponible
   - L'interface de chat s'ouvre dans une nouvelle fenêtre
   - La mémoire thermique est accessible

RACCOURCIS CLAVIER :
- Ctrl+Shift+C (Cmd+Shift+C sur Mac) : Ouvrir le chat
- Ctrl+Shift+X (Cmd+Shift+X sur Mac) : Fermer le chat
*/
