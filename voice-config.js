/**
 * CONFIGURATION VOIX JARVIS - VOIX MASCULINE NATURELLE
 * 
 * Pour obtenir une voix ultra-naturelle, configurez une de ces options :
 */

// ========================================
// OPTION 1: ELEVENLABS (RECOMMANDÉ)
// ========================================
// 1. Créez un compte sur https://elevenlabs.io
// 2. Obtenez votre clé API gratuite
// 3. Décommentez et configurez :

/*
window.ELEVENLABS_API_KEY = 'votre_clé_elevenlabs_ici';
*/

// Voix recommandées ElevenLabs pour JARVIS :
// - 21m00Tcm4TlvDq8ikWAM (Rachel - peut être adaptée)
// - pNInz6obpgDQGcFmaJgB (Adam - voix masculine)
// - EXAVITQu4vr4xnSDxMaL (Bella - voix claire)

// ========================================
// OPTION 2: AZURE COGNITIVE SERVICES
// ========================================
// 1. Créez un compte Azure
// 2. Activez Speech Services
// 3. Obtenez votre clé API
// 4. Décommentez et configurez :

/*
window.AZURE_SPEECH_KEY = 'votre_clé_azure_ici';
window.AZURE_REGION = 'votre_région_azure'; // ex: 'westeurope'
*/

// Voix Azure recommandées pour JARVIS :
// - fr-FR-HenriNeural (Voix masculine française naturelle)
// - fr-FR-AlainNeural (Voix masculine alternative)
// - fr-FR-ClaudeNeural (Voix masculine professionnelle)

// ========================================
// OPTION 3: GOOGLE CLOUD TEXT-TO-SPEECH
// ========================================
// 1. Créez un projet Google Cloud
// 2. Activez Text-to-Speech API
// 3. Obtenez votre clé API
// 4. Décommentez et configurez :

/*
window.GOOGLE_TTS_API_KEY = 'votre_clé_google_ici';
*/

// ========================================
// CONFIGURATION AVANCÉE
// ========================================

// Paramètres de voix pour JARVIS
window.JARVIS_VOICE_CONFIG = {
    // Paramètres généraux
    language: 'fr-FR',
    gender: 'male',
    
    // Paramètres de style
    rate: 0.85,        // Vitesse (0.1 à 2.0)
    pitch: 0.75,       // Hauteur (0.0 à 2.0) - plus bas = plus masculin
    volume: 0.9,       // Volume (0.0 à 1.0)
    
    // Paramètres ElevenLabs
    elevenlabs: {
        voice_id: '21m00Tcm4TlvDq8ikWAM', // ID de la voix
        model_id: 'eleven_multilingual_v2',
        stability: 0.6,
        similarity_boost: 0.7,
        style: 0.3,
        use_speaker_boost: true
    },
    
    // Paramètres Azure
    azure: {
        voice: 'fr-FR-HenriNeural',
        style: 'friendly',
        rate: '0.85',
        pitch: 'low'
    }
};

// ========================================
// INSTRUCTIONS D'INSTALLATION
// ========================================

console.log(`
🔊 CONFIGURATION VOIX JARVIS

Pour obtenir une voix ultra-naturelle :

1. 🥇 ELEVENLABS (Recommandé)
   - Visitez : https://elevenlabs.io
   - Créez un compte gratuit
   - Copiez votre clé API
   - Décommentez la ligne ELEVENLABS_API_KEY ci-dessus

2. 🥈 AZURE SPEECH
   - Visitez : https://azure.microsoft.com/speech-services
   - Créez un compte Azure
   - Activez Speech Services
   - Copiez votre clé API et région

3. 🥉 GOOGLE CLOUD TTS
   - Visitez : https://cloud.google.com/text-to-speech
   - Créez un projet Google Cloud
   - Activez l'API Text-to-Speech
   - Copiez votre clé API

Sans configuration, JARVIS utilisera la voix système optimisée.
`);

// ========================================
// EXPORT DE LA CONFIGURATION
// ========================================
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        JARVIS_VOICE_CONFIG: window.JARVIS_VOICE_CONFIG
    };
}
