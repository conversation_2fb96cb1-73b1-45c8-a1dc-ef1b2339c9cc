/**
 * 🧠 AJOUT DIRECT D'ENTRÉES MÉMOIRE
 * 
 * Script simple pour ajouter directement des entrées dans la mémoire thermique
 */

const fs = require('fs').promises;
const path = require('path');

async function addMemoryEntries() {
    console.log('🧠 === AJOUT DIRECT ENTRÉES MÉMOIRE ===\n');
    
    const memoryPath = path.join(__dirname, 'thermal_memory_persistent.json');
    
    try {
        // 1. Charger la mémoire
        console.log('📖 Chargement mémoire thermique...');
        const data = await fs.readFile(memoryPath, 'utf8');
        const memory = JSON.parse(data);
        
        // 2. Compter les entrées actuelles
        let totalBefore = 0;
        for (const zone of Object.values(memory.thermal_zones)) {
            if (zone.entries) {
                totalBefore += zone.entries.length;
            }
        }
        console.log(`📊 Entrées actuelles: ${totalBefore}`);
        
        // 3. Ajouter des conversations réalistes (zone episodic)
        console.log('\n📚 Ajout conversations...');
        const conversations = [
            "Conversation: 'Comment optimiser mon code JavaScript ?' → 'Utilisez async/await, évitez les callbacks imbriqués, implémentez le lazy loading et optimisez les boucles avec for...of.'",
            "Conversation: 'Explique-moi l'IA générative' → 'L'IA générative utilise des réseaux de neurones pour créer du contenu original : texte, images, code. Elle apprend des patterns dans les données d'entraînement.'",
            "Conversation: 'Aide-moi avec Git' → 'Git est un système de contrôle de version. Commandes essentielles : git add, git commit, git push, git pull, git branch, git merge.'",
            "Conversation: 'Qu'est-ce que Node.js ?' → 'Node.js est un runtime JavaScript côté serveur. Il permet d'exécuter du JavaScript en dehors du navigateur pour créer des APIs, serveurs web, etc.'",
            "Conversation: 'Comment débugger efficacement ?' → 'Utilisez console.log stratégiquement, le debugger intégré, les breakpoints, et analysez la stack trace des erreurs.'"
        ];
        
        for (let i = 0; i < conversations.length; i++) {
            const entry = {
                id: `conv_${Date.now()}_${i}`,
                content: conversations[i],
                timestamp: Date.now() + i * 1000,
                importance: 0.8,
                synaptic_strength: 0.8,
                temperature: 37.0,
                zone: "zone2_episodic",
                source: "direct_add",
                type: "conversation"
            };
            
            memory.thermal_zones.zone2_episodic.entries.push(entry);
        }
        
        // 4. Ajouter des connaissances techniques (zone semantic)
        console.log('🧠 Ajout connaissances...');
        const knowledge = [
            "JavaScript ES2024: Nouvelles fonctionnalités async/await améliorées, pattern matching, modules dynamiques",
            "Node.js 20+: Performance optimisée, nouvelles APIs crypto, support TypeScript natif",
            "Intelligence Artificielle: Réseaux de neurones, apprentissage automatique, traitement du langage naturel",
            "Développement Web: React 18, Next.js 14, API REST, GraphQL, WebSockets",
            "macOS Development: Swift, Objective-C, Xcode, App Store Connect, TestFlight",
            "DevOps: Docker, Kubernetes, CI/CD, GitHub Actions, AWS, monitoring"
        ];
        
        for (let i = 0; i < knowledge.length; i++) {
            const entry = {
                id: `know_${Date.now()}_${i}`,
                content: knowledge[i],
                timestamp: Date.now() + i * 1000,
                importance: 0.9,
                synaptic_strength: 0.9,
                temperature: 37.0,
                zone: "zone4_semantic",
                source: "direct_add",
                type: "knowledge"
            };
            
            memory.thermal_zones.zone4_semantic.entries.push(entry);
        }
        
        // 5. Ajouter des expériences d'apprentissage (zone working)
        console.log('🔧 Ajout expériences...');
        const experiences = [
            "Apprentissage système QI unifié: Méthodologie cohérente pour calcul évolutif du coefficient intellectuel",
            "Maîtrise accélérateurs KYBER: Optimisation temps de réponse et performance cognitive",
            "Formation MPC bureau: Contrôle total environnement macOS via commandes système",
            "Intégration mémoire thermique: Connexion réussie Python → DeepSeek R1 8B"
        ];
        
        for (let i = 0; i < experiences.length; i++) {
            const entry = {
                id: `exp_${Date.now()}_${i}`,
                content: experiences[i],
                timestamp: Date.now() + i * 1000,
                importance: 0.95,
                synaptic_strength: 0.95,
                temperature: 37.1,
                zone: "zone1_working",
                source: "direct_add",
                type: "experience"
            };
            
            memory.thermal_zones.zone1_working.entries.push(entry);
        }
        
        // 6. Ajouter des états émotionnels (zone emotional)
        console.log('💝 Ajout émotions...');
        const emotions = [
            "Satisfaction: Intégration réussie du système QI unifié",
            "Fierté: Développement des capacités MPC avancées",
            "Curiosité: Exploration continue des possibilités d'évolution"
        ];
        
        for (let i = 0; i < emotions.length; i++) {
            const entry = {
                id: `emo_${Date.now()}_${i}`,
                content: emotions[i],
                timestamp: Date.now() + i * 1000,
                importance: 0.7,
                synaptic_strength: 0.7,
                temperature: 36.8,
                zone: "zone5_emotional",
                source: "direct_add",
                type: "emotion"
            };
            
            memory.thermal_zones.zone5_emotional.entries.push(entry);
        }
        
        // 7. Ajouter de la métacognition (zone meta)
        console.log('🤔 Ajout métacognition...');
        const metacognition = [
            "Réflexion QI: Évolution de 135 à 201 grâce au système unifié",
            "Analyse apprentissage: Amélioration continue par accumulation d'expérience"
        ];
        
        for (let i = 0; i < metacognition.length; i++) {
            const entry = {
                id: `meta_${Date.now()}_${i}`,
                content: metacognition[i],
                timestamp: Date.now() + i * 1000,
                importance: 0.85,
                synaptic_strength: 0.85,
                temperature: 37.5,
                zone: "zone6_meta",
                source: "direct_add",
                type: "metacognition"
            };
            
            memory.thermal_zones.zone6_meta.entries.push(entry);
        }
        
        // 8. Compter les nouvelles entrées
        let totalAfter = 0;
        for (const zone of Object.values(memory.thermal_zones)) {
            if (zone.entries) {
                totalAfter += zone.entries.length;
            }
        }
        
        const added = totalAfter - totalBefore;
        console.log(`\n📊 Entrées ajoutées: ${added}`);
        console.log(`📊 Total final: ${totalAfter}`);
        
        // 9. Sauvegarder
        console.log('\n💾 Sauvegarde...');
        memory.last_modified = new Date().toISOString();
        await fs.writeFile(memoryPath, JSON.stringify(memory, null, 2));
        
        console.log('✅ Mémoire thermique enrichie avec succès !');
        
        // 10. Afficher la répartition finale
        console.log('\n📊 === RÉPARTITION FINALE ===');
        for (const [zoneName, zone] of Object.entries(memory.thermal_zones)) {
            console.log(`   ${zoneName}: ${zone.entries.length} entrées`);
        }
        
        return true;
        
    } catch (error) {
        console.error('❌ Erreur:', error.message);
        return false;
    }
}

// Exécuter si appelé directement
if (require.main === module) {
    addMemoryEntries()
        .then(success => {
            process.exit(success ? 0 : 1);
        })
        .catch(error => {
            console.error('❌ Erreur fatale:', error.message);
            process.exit(1);
        });
}

module.exports = { addMemoryEntries };
