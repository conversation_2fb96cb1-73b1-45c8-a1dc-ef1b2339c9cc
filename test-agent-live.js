#!/usr/bin/env node

/**
 * 🧪 TEST EN DIRECT DE L'AGENT LOUNA
 * 
 * Test l'agent via l'API HTTP pour vérifier qu'il répond correctement
 */

const axios = require('axios');

class TestAgentLive {
    constructor() {
        this.baseUrl = 'http://localhost:3000';
        this.testResults = [];
        this.startTime = Date.now();
    }

    async runTests() {
        console.log('🧪 === TEST EN DIRECT AGENT LOUNA ===\n');
        console.log('🌐 Test via API HTTP sur http://localhost:3000\n');

        try {
            // Vérifier que le serveur répond
            await this.testServerHealth();
            
            // Test de conversation basique
            await this.testBasicConversation();
            
            // Test de questions complexes
            await this.testComplexQuestions();
            
            // Test de performance
            await this.testPerformance();
            
            // Test des statistiques
            await this.testStats();
            
            // Rapport final
            this.generateReport();
            
        } catch (error) {
            console.error(`❌ Erreur fatale: ${error.message}`);
        }
    }

    async testServerHealth() {
        console.log('🔍 Test 1: Santé du serveur');
        
        try {
            const response = await axios.get(`${this.baseUrl}/api/stats`, {
                timeout: 5000
            });
            
            if (response.status === 200 && response.data) {
                console.log('  ✅ Serveur répond correctement');
                console.log(`  📊 Statut: ${response.status}`);
                console.log(`  🧠 Agent actif: ${response.data.agent_active ? 'Oui' : 'Non'}`);
                this.recordTest('Santé serveur', true);
            } else {
                throw new Error('Réponse serveur invalide');
            }
            
        } catch (error) {
            console.log(`  ❌ Échec: ${error.message}`);
            this.recordTest('Santé serveur', false);
        }
        
        console.log('');
    }

    async testBasicConversation() {
        console.log('🔍 Test 2: Conversation basique');
        
        const testMessages = [
            'Bonjour',
            'Comment ça va ?',
            'Quel est ton nom ?',
            'Peux-tu m\'aider ?'
        ];
        
        let successCount = 0;
        
        for (const message of testMessages) {
            try {
                console.log(`  💬 Test: "${message}"`);
                const startTime = Date.now();
                
                const response = await axios.post(`${this.baseUrl}/api/chat/message`, {
                    message: message
                }, {
                    timeout: 10000,
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                const duration = Date.now() - startTime;
                
                if (response.data && response.data.success && response.data.response) {
                    console.log(`    ✅ Réponse reçue en ${duration}ms`);
                    console.log(`    📝 Réponse: "${response.data.response.substring(0, 100)}..."`);
                    successCount++;
                } else {
                    console.log(`    ❌ Réponse invalide`);
                }
                
                // Pause entre les messages
                await new Promise(resolve => setTimeout(resolve, 1000));
                
            } catch (error) {
                console.log(`    ❌ Erreur: ${error.message}`);
            }
        }
        
        if (successCount === testMessages.length) {
            console.log(`  ✅ Conversation basique réussie (${successCount}/${testMessages.length})`);
            this.recordTest('Conversation basique', true);
        } else {
            console.log(`  ⚠️  Conversation partiellement réussie (${successCount}/${testMessages.length})`);
            this.recordTest('Conversation basique', successCount > testMessages.length / 2);
        }
        
        console.log('');
    }

    async testComplexQuestions() {
        console.log('🔍 Test 3: Questions complexes');
        
        const complexQuestions = [
            'Explique-moi le fonctionnement de la mémoire thermique',
            'Quel est ton QI et comment est-il calculé ?',
            'Peux-tu résoudre cette équation : 2x + 5 = 15 ?',
            'Raconte-moi une histoire courte sur l\'intelligence artificielle'
        ];
        
        let successCount = 0;
        
        for (const question of complexQuestions) {
            try {
                console.log(`  🧠 Question complexe: "${question.substring(0, 50)}..."`);
                const startTime = Date.now();
                
                const response = await axios.post(`${this.baseUrl}/api/chat/message`, {
                    message: question
                }, {
                    timeout: 15000,
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                const duration = Date.now() - startTime;
                
                if (response.data && response.data.success && response.data.response) {
                    const responseLength = response.data.response.length;
                    console.log(`    ✅ Réponse complexe générée en ${duration}ms`);
                    console.log(`    📏 Longueur réponse: ${responseLength} caractères`);
                    
                    // Vérifier que la réponse est substantielle
                    if (responseLength > 50) {
                        successCount++;
                        console.log(`    💡 Réponse substantielle`);
                    } else {
                        console.log(`    ⚠️  Réponse trop courte`);
                    }
                } else {
                    console.log(`    ❌ Réponse invalide`);
                }
                
                // Pause plus longue pour les questions complexes
                await new Promise(resolve => setTimeout(resolve, 2000));
                
            } catch (error) {
                console.log(`    ❌ Erreur: ${error.message}`);
            }
        }
        
        if (successCount >= complexQuestions.length * 0.75) {
            console.log(`  ✅ Questions complexes réussies (${successCount}/${complexQuestions.length})`);
            this.recordTest('Questions complexes', true);
        } else {
            console.log(`  ⚠️  Questions complexes partiellement réussies (${successCount}/${complexQuestions.length})`);
            this.recordTest('Questions complexes', false);
        }
        
        console.log('');
    }

    async testPerformance() {
        console.log('🔍 Test 4: Performance');
        
        try {
            const testMessage = 'Test de performance rapide';
            const iterations = 3;
            const times = [];
            
            console.log(`  ⏱️  Test de ${iterations} requêtes rapides...`);
            
            for (let i = 0; i < iterations; i++) {
                const startTime = Date.now();
                
                const response = await axios.post(`${this.baseUrl}/api/chat/message`, {
                    message: `${testMessage} ${i + 1}`
                }, {
                    timeout: 8000,
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                const duration = Date.now() - startTime;
                times.push(duration);
                
                console.log(`    🚀 Requête ${i + 1}: ${duration}ms`);
                
                // Petite pause
                await new Promise(resolve => setTimeout(resolve, 500));
            }
            
            const averageTime = times.reduce((a, b) => a + b, 0) / times.length;
            const minTime = Math.min(...times);
            const maxTime = Math.max(...times);
            
            console.log(`  📊 Temps moyen: ${averageTime.toFixed(0)}ms`);
            console.log(`  🚀 Plus rapide: ${minTime}ms`);
            console.log(`  🐌 Plus lent: ${maxTime}ms`);
            
            if (averageTime < 3000) { // Moins de 3 secondes en moyenne
                console.log(`  ✅ Performance acceptable`);
                this.recordTest('Performance', true);
            } else {
                console.log(`  ⚠️  Performance à améliorer`);
                this.recordTest('Performance', false);
            }
            
        } catch (error) {
            console.log(`  ❌ Échec: ${error.message}`);
            this.recordTest('Performance', false);
        }
        
        console.log('');
    }

    async testStats() {
        console.log('🔍 Test 5: Statistiques système');
        
        try {
            const response = await axios.get(`${this.baseUrl}/api/stats`, {
                timeout: 5000
            });
            
            if (response.data) {
                console.log('  📊 Statistiques système:');
                console.log(`    🧠 Agent actif: ${response.data.agent_active ? 'Oui' : 'Non'}`);
                console.log(`    💬 Messages envoyés: ${response.data.messages_sent || 0}`);
                console.log(`    📈 Messages reçus: ${response.data.messages_received || 0}`);
                console.log(`    🔗 Connexions: ${response.data.connections || 0}`);
                console.log(`    ⏱️  Uptime: ${Math.round((Date.now() - (response.data.uptime_start || Date.now())) / 1000)}s`);
                
                this.recordTest('Statistiques', true);
                console.log('  ✅ Statistiques récupérées');
            } else {
                throw new Error('Pas de données statistiques');
            }
            
        } catch (error) {
            console.log(`  ❌ Échec: ${error.message}`);
            this.recordTest('Statistiques', false);
        }
        
        console.log('');
    }

    recordTest(name, passed) {
        this.testResults.push({ 
            name, 
            passed, 
            timestamp: Date.now() 
        });
    }

    generateReport() {
        const duration = Date.now() - this.startTime;
        const passed = this.testResults.filter(test => test.passed).length;
        const total = this.testResults.length;
        const successRate = (passed / total * 100).toFixed(1);
        
        console.log('📊 === RAPPORT FINAL TEST EN DIRECT ===\n');
        console.log(`🎯 Tests réussis: ${passed}/${total} (${successRate}%)`);
        console.log(`⏱️  Durée totale: ${duration}ms`);
        
        console.log('\n🔍 Détail des tests:');
        this.testResults.forEach(test => {
            const status = test.passed ? '✅' : '❌';
            console.log(`  ${status} ${test.name}`);
        });
        
        if (successRate >= 80) {
            console.log('\n🎉 VERDICT: AGENT LOUNA FONCTIONNE PARFAITEMENT ✅');
            console.log('🚀 L\'agent répond correctement aux requêtes !');
        } else if (successRate >= 60) {
            console.log('\n⚠️  VERDICT: AGENT PARTIELLEMENT FONCTIONNEL');
            console.log('🔧 Quelques améliorations possibles');
        } else {
            console.log('\n❌ VERDICT: PROBLÈMES DÉTECTÉS');
            console.log('🛠️  Vérifications nécessaires');
        }
        
        console.log('\n💡 L\'interface web est disponible sur: http://localhost:3000');
    }
}

// Lancement du test
if (require.main === module) {
    const tester = new TestAgentLive();
    tester.runTests();
}

module.exports = TestAgentLive;
