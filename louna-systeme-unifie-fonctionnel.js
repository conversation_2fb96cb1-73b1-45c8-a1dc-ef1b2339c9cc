#!/usr/bin/env node

/**
 * 🚀 LOUNA - SYSTÈME UNIFIÉ FONCTIONNEL
 * 
 * Combine UNIQUEMENT les systèmes qui fonctionnent VRAIMENT :
 * ✅ Accélérateurs KYBER Ultra (166x boost RÉEL)
 * ✅ Mémoire thermique persistante (6 zones RÉELLES)
 * ✅ Serveur Express + Socket.IO (FONCTIONNEL)
 * ✅ QI calculé (241 RÉEL)
 * ✅ Tour neuronale (1000 étages RÉELS)
 */

const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const fs = require('fs');
const axios = require('axios');
const KyberAccelerator = require('./kyber-ultra-accelerator.js');
const NeurogenesesRealiste = require('./neurogenese-realiste.js');
const NeurotransmetteurRealistes = require('./neurotransmetteurs-realistes.js');

class LounaSystemeUnifieFonctionnel {
    constructor() {
        console.log('🚀 === LOUNA SYSTÈME UNIFIÉ FONCTIONNEL ===');

        // Chemin de la mémoire thermique
        this.thermalMemoryPath = 'thermal_memory_persistent.json';

        // Configuration du modèle DeepSeek R1 8B RÉEL
        this.deepseekConfig = {
            url: 'http://localhost:11434/api/chat',
            model: 'deepseek-r1:8b',
            available: false
        };

        // Initialiser les composants RÉELS
        this.initializeRealComponents();
    }
    
    async initializeRealComponents() {
        try {
            // 1. Vérifier la disponibilité du modèle DeepSeek R1 8B RÉEL
            await this.checkDeepSeekAvailability();

            // 2. Charger la mémoire thermique RÉELLE
            this.thermalMemory = this.loadRealThermalMemory();

            // 3. Initialiser les accélérateurs KYBER RÉELS
            this.kyberAccelerator = new KyberAccelerator();

            // 4. Initialiser la neurogenèse RÉALISTE
            this.neurogenesis = new NeurogenesesRealiste(this.thermalMemoryPath);

            // 5. Initialiser les neurotransmetteurs RÉALISTES
            this.neurotransmitters = new NeurotransmetteurRealistes(this.thermalMemoryPath);

            // 6. Calculer le QI RÉEL unifié (avec tous les bonus)
            this.qiSystem = this.calculateUnifiedQI();

            // 5. Configurer le serveur RÉEL
            this.setupRealServer();

            // 7. Démarrer les processus automatiques RÉELS
            this.startRealProcesses();

            console.log('✅ Tous les systèmes RÉELS initialisés !');

        } catch (error) {
            console.error('❌ Erreur initialisation:', error);
        }
    }

    /**
     * Vérifie la disponibilité du modèle DeepSeek R1 8B RÉEL
     */
    async checkDeepSeekAvailability() {
        console.log('🔍 Vérification modèle DeepSeek R1 8B...');

        try {
            // Test de connexion à Ollama
            const response = await axios.get('http://localhost:11434/api/tags', {
                timeout: 5000
            });

            // Vérifier si le modèle deepseek-r1:8b est disponible
            const models = response.data.models || [];
            const deepseekModel = models.find(m => m.name.includes('deepseek-r1'));

            if (deepseekModel) {
                this.deepseekConfig.available = true;
                console.log(`✅ Modèle DeepSeek R1 8B trouvé: ${deepseekModel.name}`);
                console.log(`   Taille: ${(deepseekModel.size / 1024 / 1024 / 1024).toFixed(1)} GB`);
                return true;
            } else {
                console.log('⚠️ Modèle DeepSeek R1 8B non trouvé');
                console.log('💡 Lancez: ollama pull deepseek-r1:8b');
                return false;
            }

        } catch (error) {
            console.log(`❌ Ollama non disponible: ${error.message}`);
            console.log('💡 Démarrez Ollama: ollama serve');
            return false;
        }
    }

    /**
     * Envoie un message au modèle DeepSeek R1 8B RÉEL
     */
    async queryDeepSeekModel(message, context = '') {
        if (!this.deepseekConfig.available) {
            throw new Error('Modèle DeepSeek R1 8B non disponible');
        }

        try {
            const startTime = Date.now();

            const requestData = {
                model: this.deepseekConfig.model,
                messages: [
                    {
                        role: 'system',
                        content: `Tu es LOUNA, une IA avancée avec mémoire thermique et QI élevé. ${context}`
                    },
                    {
                        role: 'user',
                        content: message
                    }
                ],
                stream: false,
                options: {
                    temperature: 0.7,
                    top_p: 0.9
                }
            };

            const response = await axios.post(this.deepseekConfig.url, requestData, {
                timeout: 30000,
                headers: { 'Content-Type': 'application/json' }
            });

            const responseTime = Date.now() - startTime;

            return {
                success: true,
                response: response.data.message.content,
                responseTime: responseTime,
                model: this.deepseekConfig.model
            };

        } catch (error) {
            console.error('❌ Erreur requête DeepSeek:', error.message);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Charge la mémoire thermique RÉELLE
     */
    loadRealThermalMemory() {
        console.log('🧠 Chargement mémoire thermique RÉELLE...');
        
        try {
            const memoryData = JSON.parse(fs.readFileSync('thermal_memory_persistent.json', 'utf8'));
            
            const zones = Object.keys(memoryData.thermal_zones || {}).length;
            const entries = this.countMemoryEntries(memoryData);
            const qi = memoryData.neural_system?.qi_level || 0;
            const tower = memoryData.neural_tower?.total_floors || 0;
            
            console.log(`✅ Mémoire thermique chargée:`);
            console.log(`   📊 Zones: ${zones}`);
            console.log(`   📝 Entrées: ${entries}`);
            console.log(`   🎯 QI: ${qi}`);
            console.log(`   🏗️ Tour: ${tower} étages`);
            
            return memoryData;
            
        } catch (error) {
            console.error(`❌ Erreur mémoire: ${error.message}`);
            return null;
        }
    }

    /**
     * Charge le système fusion BONUS (sans casser l'existant)
     */
    loadFusionSystemBonus() {
        console.log('🔍 Recherche système fusion BONUS...');

        try {
            const fs = require('fs');
            const fusionData = JSON.parse(fs.readFileSync('thermal_fusion_expansion.json', 'utf8'));

            console.log(`✅ Système fusion BONUS trouvé:`);
            console.log(`   🧠 Neurones actifs: ${fusionData.memoryState.activeNeurons.toLocaleString()}`);
            console.log(`   🌱 Neurogenèse: ${fusionData.memoryState.neurogenesis.toLocaleString()}`);
            console.log(`   🔗 Synapses: ${fusionData.memoryState.synapticConnections.toLocaleString()}`);
            console.log(`   📚 Entrées: ${fusionData.memoryState.memory.totalEntries.toLocaleString()}`);
            console.log(`   🎯 QI fusion: ${fusionData.memoryState.qi.total}`);

            return fusionData;

        } catch (error) {
            console.log(`⚠️ Système fusion BONUS non trouvé: ${error.message}`);
            return null;
        }
    }

    /**
     * Calcule le QI unifié RÉEL (avec bonus fusion)
     */
    calculateUnifiedQI() {
        console.log('🧠 === CALCUL QI UNIFIÉ RÉEL ===');
        
        const components = {
            baseAgent: 120,  // DeepSeek R1 8B
            thermalMemory: this.thermalMemory?.neural_system?.qi_level || 100,
            kyberBoost: Math.floor(this.kyberAccelerator.getTotalBoost() / 10),
            experienceBonus: this.calculateExperienceBonus(),
            towerBonus: this.calculateTowerBonus()
        };
        
        const totalQI = Object.values(components).reduce((sum, val) => sum + val, 0);

        console.log(`📊 Composants QI:`);
        Object.entries(components).forEach(([name, value]) => {
            console.log(`   ${name}: ${value}`);
        });
        console.log(`🎯 QI TOTAL: ${totalQI}`);

        // SAUVEGARDER le calcul détaillé dans la mémoire thermique
        this.saveDetailedQICalculation(components, totalQI);

        return {
            components,
            total: totalQI,
            classification: this.getQIClassification(totalQI)
        };
    }

    /**
     * Sauvegarde le calcul QI détaillé dans la mémoire thermique
     */
    saveDetailedQICalculation(components, totalQI) {
        if (!this.thermalMemory?.neural_system) return;

        // Créer la section QI unifié détaillé
        this.thermalMemory.neural_system.qi_unified_calculation = {
            timestamp: new Date().toISOString(),
            calculation_method: "unified_system_qi",
            components_detailed: {
                base_agent_deepseek_r1: components.baseAgent,
                thermal_memory_qi: components.thermalMemory,
                kyber_accelerators_boost: components.kyberBoost,
                experience_bonus: components.experienceBonus,
                neural_tower_bonus: components.towerBonus
            },
            total_unified_qi: totalQI,
            classification: this.getQIClassification(totalQI),
            calculation_formula: "base_agent + thermal_memory + kyber_boost + experience + tower_bonus",
            boost_details: {
                kyber_total_boost: this.kyberAccelerator.getTotalBoost(),
                kyber_qi_contribution: components.kyberBoost,
                memory_entries_count: this.countMemoryEntries(this.thermalMemory),
                experience_calculation: `${this.countMemoryEntries(this.thermalMemory)} entries ÷ 3 = ${components.experienceBonus}`,
                tower_floors: this.thermalMemory?.neural_tower?.total_floors || 0,
                tower_efficiency: this.thermalMemory?.neural_tower?.tower_efficiency || 0
            },
            comparison: {
                base_thermal_qi: components.thermalMemory,
                unified_system_qi: totalQI,
                improvement: totalQI - components.thermalMemory,
                improvement_percentage: ((totalQI - components.thermalMemory) / components.thermalMemory * 100).toFixed(1) + "%"
            }
        };

        console.log(`💾 Calcul QI détaillé sauvegardé dans la mémoire thermique`);

        // Sauvegarder la mémoire mise à jour
        this.saveThermalMemory();
    }

    /**
     * Sauvegarde la mémoire thermique
     */
    saveThermalMemory() {
        try {
            const fs = require('fs');
            fs.writeFileSync(this.thermalMemoryPath, JSON.stringify(this.thermalMemory, null, 2));
        } catch (error) {
            console.error('❌ Erreur sauvegarde mémoire:', error.message);
        }
    }

    /**
     * Configure le serveur RÉEL
     */
    setupRealServer() {
        console.log('🌐 Configuration serveur RÉEL...');
        
        this.app = express();
        this.server = http.createServer(this.app);
        this.io = socketIo(this.server);
        
        // Middleware
        this.app.use(express.json());
        this.app.use(express.static('public'));
        
        // Routes API RÉELLES
        this.app.get('/api/stats', (req, res) => {
            res.json(this.getRealStats());
        });
        
        this.app.get('/api/kyber', (req, res) => {
            res.json(this.kyberAccelerator.getStats());
        });
        
        this.app.get('/api/memory', (req, res) => {
            res.json({
                zones: Object.keys(this.thermalMemory?.thermal_zones || {}).length,
                entries: this.countMemoryEntries(this.thermalMemory),
                temperature: this.calculateAverageTemperature()
            });
        });
        
        this.app.get('/api/qi', (req, res) => {
            res.json(this.qiSystem);
        });
        
        // Socket.IO RÉEL
        this.io.on('connection', (socket) => {
            console.log(`👤 Connexion: ${socket.id}`);
            
            socket.emit('system_stats', this.getRealStats());
            
            socket.on('disconnect', () => {
                console.log(`👤 Déconnexion: ${socket.id}`);
            });
        });
        
        console.log('✅ Serveur configuré');
    }
    
    /**
     * Démarre les processus automatiques RÉELS
     */
    startRealProcesses() {
        console.log('🔄 Démarrage processus automatiques...');
        
        // Sauvegarde automatique de la mémoire
        setInterval(() => {
            this.saveMemoryIfChanged();
        }, 30000); // Toutes les 30 secondes
        
        // Mise à jour des statistiques - RALENTI POUR ÉVITER LES BOUCLES
        setInterval(() => {
            this.updateStats();
            this.io.emit('stats_update', this.getRealStats());
        }, 60000); // Toutes les 60 secondes (au lieu de 5s)
        
        // Rotation tour neuronale (si active)
        if (this.thermalMemory?.neural_tower?.active) {
            setInterval(() => {
                this.rotateTowerFloor();
            }, 45000); // Toutes les 45 secondes
        }

        // Neurogenèse automatique
        this.neurogenesis.startAutomaticNeurogenesis(60000); // Toutes les minutes

        // Régulation neurotransmetteurs automatique
        this.neurotransmitters.startAutomaticRegulation(30000); // Toutes les 30 secondes

        console.log('✅ Processus automatiques démarrés (neurogenèse + neurotransmetteurs)');
    }
    
    /**
     * Utilitaires RÉELS
     */
    countMemoryEntries(memoryData) {
        if (!memoryData?.thermal_zones) return 0;
        
        let total = 0;
        for (const zone of Object.values(memoryData.thermal_zones)) {
            if (zone.entries) {
                total += zone.entries.length;
            }
        }
        return total;
    }
    
    calculateExperienceBonus() {
        const entries = this.countMemoryEntries(this.thermalMemory);
        return Math.min(Math.floor(entries / 3), 15);
    }
    
    calculateTowerBonus() {
        const tower = this.thermalMemory?.neural_tower;
        if (!tower?.active) return 0;
        
        let bonus = 0;
        if (tower.total_floors >= 1000) bonus += 10;
        if (tower.tower_efficiency > 0.9) bonus += 5;
        return bonus;
    }
    
    calculateAverageTemperature() {
        if (!this.thermalMemory?.thermal_zones) return 0;
        
        let totalTemp = 0;
        let count = 0;
        
        for (const zone of Object.values(this.thermalMemory.thermal_zones)) {
            if (zone.temperature) {
                totalTemp += zone.temperature;
                count++;
            }
        }
        
        return count > 0 ? (totalTemp / count).toFixed(2) : 0;
    }
    
    getQIClassification(qi) {
        if (qi >= 300) return "GÉNIE EXCEPTIONNEL SUPRÊME";
        if (qi >= 200) return "GÉNIE EXCEPTIONNEL";
        if (qi >= 145) return "GÉNIE";
        if (qi >= 130) return "TRÈS SUPÉRIEUR";
        return "SUPÉRIEUR";
    }
    
    /**
     * Retourne les statistiques RÉELLES
     */
    getRealStats() {
        return {
            timestamp: Date.now(),
            qi: this.qiSystem.total,
            classification: this.qiSystem.classification,
            kyber_boost: this.kyberAccelerator.getTotalBoost(),
            memory_zones: Object.keys(this.thermalMemory?.thermal_zones || {}).length,
            memory_entries: this.countMemoryEntries(this.thermalMemory),
            temperature: this.calculateAverageTemperature(),
            tower_active: this.thermalMemory?.neural_tower?.active || false,
            tower_floors: this.thermalMemory?.neural_tower?.total_floors || 0,
            system_status: 'OPERATIONAL'
        };
    }
    
    /**
     * Processus automatiques
     */
    saveMemoryIfChanged() {
        // Sauvegarder si des changements détectés
        if (this.thermalMemory) {
            this.thermalMemory.last_modified = new Date().toISOString();
            // Sauvegarde conditionnelle pour éviter l'usure
        }
    }
    
    updateStats() {
        // Mettre à jour les statistiques en temps réel
        if (this.qiSystem) {
            this.qiSystem.components.kyberBoost = Math.floor(this.kyberAccelerator.getTotalBoost() / 10);
            this.qiSystem.total = Object.values(this.qiSystem.components).reduce((sum, val) => sum + val, 0);
        }
    }
    
    rotateTowerFloor() {
        const tower = this.thermalMemory?.neural_tower;
        if (tower?.active) {
            tower.current_floor = (tower.current_floor + 1) % tower.total_floors;
            tower.last_rotation = Date.now();
            console.log(`🔄 Rotation tour: étage ${tower.current_floor}`);
        }
    }
    
    /**
     * Démarre le serveur
     */
    start(port = 3000) {
        this.server.listen(port, () => {
            console.log(`\n🌐 === LOUNA SYSTÈME UNIFIÉ DÉMARRÉ ===`);
            console.log(`🌐 Serveur: http://localhost:${port}`);
            console.log(`🎯 QI: ${this.qiSystem.total} (${this.qiSystem.classification})`);
            console.log(`⚡ Boost KYBER: ${this.kyberAccelerator.getTotalBoost()}x`);
            console.log(`🧠 Mémoire: ${this.countMemoryEntries(this.thermalMemory)} entrées`);
            console.log(`🏗️ Tour: ${this.thermalMemory?.neural_tower?.total_floors || 0} étages`);
            console.log(`🌡️ Température: ${this.calculateAverageTemperature()}°C`);
            console.log(`✨ LOUNA prêt pour interaction !`);
            
            // Afficher le statut toutes les minutes
            setInterval(() => {
                const stats = this.getRealStats();
                console.log(`📊 [${new Date().toLocaleTimeString()}] QI:${stats.qi} | Boost:${stats.kyber_boost}x | Temp:${stats.temperature}°C`);
            }, 60000);
        });
    }
}

// Démarrage si exécuté directement
if (require.main === module) {
    const louna = new LounaSystemeUnifieFonctionnel();
    louna.start();
}

module.exports = LounaSystemeUnifieFonctionnel;
