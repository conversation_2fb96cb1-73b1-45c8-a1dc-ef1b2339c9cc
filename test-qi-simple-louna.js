#!/usr/bin/env node

/**
 * 🧠 TEST DE QI SIMPLE POUR LOUNA
 * 
 * Analyse l'évolution cognitive et teste l'intelligence de l'agent
 */

const DeepSeekR1IntegratedAgent = require('./deepseek-r1-agent-integrated');

async function testQISimple() {
    console.log('🧠 === ANALYSE DE L\'ÉVOLUTION COGNITIVE DE LOUNA ===\n');
    
    try {
        // === PHASE 1: INITIALISATION ===
        console.log('🚀 Initialisation de l\'agent LOUNA...');
        const agent = new DeepSeekR1IntegratedAgent();
        await agent.initialize();
        console.log('✅ Agent initialisé avec succès\n');
        
        // === PHASE 2: ANALYSE DE L'ÉTAT COGNITIF ===
        console.log('🔬 === ANALYSE DE L\'ÉTAT COGNITIF ACTUEL ===\n');
        
        // Analyser la mémoire thermique
        const memoryStats = agent.analyzeMemoryStats();
        const totalMemories = agent.countTotalMemoryEntries();
        
        console.log('📊 **État de la Mémoire Thermique:**');
        console.log(`   • Entrées totales: ${totalMemories}`);
        console.log(`   • Zones actives: ${memoryStats.zones}`);
        console.log(`   • QI enregistré: ${memoryStats.qi}`);
        console.log(`   • Température moyenne: ${memoryStats.temperature.toFixed(2)}°C`);
        console.log(`   • Version: ${memoryStats.version}`);
        console.log(`   • Format: ${memoryStats.format}`);
        
        // Analyser le système neurologique
        if (agent.modules.advancedBrain) {
            const brainState = agent.modules.advancedBrain.getBrainState();
            
            console.log('\n🧠 **État Neurologique:**');
            console.log(`   • Température cérébrale: ${brainState.global_temperature.toFixed(2)}°C`);
            console.log(`   • Phase circadienne: ${brainState.circadian ? brainState.circadian.current_phase : 'N/A'}`);
            console.log(`   • Performance cognitive: ${brainState.circadian ? (brainState.circadian.cognitive_performance * 100).toFixed(1) : 'N/A'}%`);
            console.log(`   • Onde dominante: ${brainState.brainwaves ? brainState.brainwaves.current_dominant : 'N/A'}`);
            console.log(`   • État émotionnel: ${brainState.emotions && brainState.emotions.current_emotional_state ? brainState.emotions.current_emotional_state.primary_emotion : 'N/A'}`);
        }
        
        // === PHASE 3: TESTS COGNITIFS SIMPLES ===
        console.log('\n🎯 === TESTS COGNITIFS SIMPLES ===\n');
        
        const tests = [
            {
                name: "Logique Mathématique",
                question: "Quelle est la prochaine valeur dans cette séquence : 2, 6, 12, 20, 30, ?",
                correct_answer: "42",
                points: 15
            },
            {
                name: "Raisonnement Verbal",
                question: "LIVRE est à BIBLIOTHÈQUE ce que TABLEAU est à ?",
                correct_answer: "musée",
                points: 12
            },
            {
                name: "Calcul Mental",
                question: "Combien font 17 × 23 ?",
                correct_answer: "391",
                points: 10
            },
            {
                name: "Logique Déductive",
                question: "Si tous les chats sont des mammifères et que Félix est un chat, que peut-on conclure ?",
                correct_answer: "félix est un mammifère",
                points: 8
            },
            {
                name: "Culture Générale",
                question: "Quelle est la capitale de la France ?",
                correct_answer: "paris",
                points: 5
            }
        ];
        
        let totalScore = 0;
        let maxScore = 0;
        const results = [];
        
        for (const test of tests) {
            console.log(`🧪 Test: ${test.name}`);
            console.log(`❓ Question: ${test.question}`);
            
            const startTime = Date.now();
            const response = await agent.processMessage(`Question de test de QI: ${test.question}`);
            const responseTime = Date.now() - startTime;
            
            console.log(`🤖 Réponse: ${response.message.substring(0, 150)}...`);
            
            // Analyser la réponse
            const isCorrect = response.message.toLowerCase().includes(test.correct_answer.toLowerCase());
            const score = isCorrect ? test.points : 0;
            
            totalScore += score;
            maxScore += test.points;
            
            results.push({
                test: test.name,
                correct: isCorrect,
                score: score,
                maxScore: test.points,
                responseTime: responseTime
            });
            
            console.log(`✅ Correct: ${isCorrect ? 'OUI' : 'NON'} | Score: ${score}/${test.points} | Temps: ${(responseTime/1000).toFixed(1)}s\n`);
            
            // Pause entre tests
            await new Promise(resolve => setTimeout(resolve, 1000));
        }
        
        // === PHASE 4: CALCUL DU QI ===
        console.log('🎯 === CALCUL DU QI ===\n');
        
        const successRate = totalScore / maxScore;
        const averageResponseTime = results.reduce((sum, r) => sum + r.responseTime, 0) / results.length;
        
        // Formule de QI simplifiée
        let calculatedQI = 100 + (successRate - 0.5) * 60; // Base
        
        // Bonus pour rapidité (si < 3 secondes en moyenne)
        if (averageResponseTime < 3000) {
            calculatedQI += 5;
        }
        
        // Bonus pour performance cognitive du cerveau
        if (agent.modules.advancedBrain) {
            const brainState = agent.modules.advancedBrain.getBrainState();
            if (brainState.circadian && brainState.circadian.cognitive_performance > 0.9) {
                calculatedQI += 10;
            }
        }
        
        calculatedQI = Math.round(Math.max(70, Math.min(200, calculatedQI)));
        
        // === PHASE 5: RÉSULTATS FINAUX ===
        console.log('📊 **Résultats des Tests:**');
        console.log(`   • Score total: ${totalScore}/${maxScore} points`);
        console.log(`   • Taux de réussite: ${(successRate * 100).toFixed(1)}%`);
        console.log(`   • Temps de réponse moyen: ${(averageResponseTime/1000).toFixed(1)}s`);
        console.log(`   • QI calculé: ${calculatedQI}`);
        
        console.log('\n📈 **Détail par Test:**');
        results.forEach(result => {
            console.log(`   ${result.correct ? '✅' : '❌'} ${result.test}: ${result.score}/${result.maxScore}`);
        });
        
        // === PHASE 6: ÉVOLUTION COGNITIVE ===
        console.log('\n🧬 === ANALYSE DE L\'ÉVOLUTION COGNITIVE ===\n');
        
        console.log('📊 **Comparaison avec l\'état initial:**');
        console.log(`   • QI mémoire thermique: ${memoryStats.qi}`);
        console.log(`   • QI calculé actuel: ${calculatedQI}`);
        
        const evolution = calculatedQI - memoryStats.qi;
        console.log(`   • Évolution: ${evolution > 0 ? '+' : ''}${evolution} points`);
        
        if (evolution > 0) {
            console.log('🚀 **ÉVOLUTION POSITIVE** - L\'agent s\'améliore !');
        } else if (evolution === 0) {
            console.log('📊 **STABILITÉ** - L\'agent maintient son niveau');
        } else {
            console.log('📉 **RÉGRESSION** - L\'agent nécessite optimisation');
        }
        
        // === PHASE 7: CLASSIFICATION FINALE ===
        console.log('\n🏆 === CLASSIFICATION FINALE ===\n');
        
        if (calculatedQI >= 145) {
            console.log('🌟 **GÉNIE** - Intelligence exceptionnelle');
            console.log('🎯 Votre agent LOUNA démontre des capacités extraordinaires !');
        } else if (calculatedQI >= 130) {
            console.log('✨ **TRÈS SUPÉRIEUR** - Intelligence très élevée');
            console.log('🎯 Votre agent LOUNA est remarquablement intelligent !');
        } else if (calculatedQI >= 115) {
            console.log('📈 **SUPÉRIEUR** - Intelligence au-dessus de la moyenne');
            console.log('🎯 Votre agent LOUNA montre d\'excellentes capacités !');
        } else if (calculatedQI >= 85) {
            console.log('📊 **MOYEN** - Intelligence dans la norme');
            console.log('🎯 Votre agent LOUNA fonctionne correctement !');
        } else {
            console.log('🔧 **EN DÉVELOPPEMENT** - Potentiel d\'amélioration');
            console.log('🎯 Votre agent LOUNA peut être optimisé !');
        }
        
        console.log('\n🎉 === CONCLUSION ===');
        console.log(`🧠 Votre agent LOUNA a un QI de ${calculatedQI}`);
        console.log(`📊 Avec ${totalMemories} mémoires stockées et un système neurologique actif`);
        console.log(`🌡️ Température cérébrale optimale à ${agent.modules.advancedBrain ? agent.modules.advancedBrain.getBrainState().global_temperature.toFixed(2) : '37.00'}°C`);
        console.log(`🚀 L'agent continue d'évoluer et d'apprendre !`);
        
    } catch (error) {
        console.error(`❌ Erreur test QI: ${error.message}`);
        console.error(error.stack);
        process.exit(1);
    }
}

// Lancer le test
if (require.main === module) {
    testQISimple();
}

module.exports = { testQISimple };
