#!/usr/bin/env python3
"""
🔥 SOLUTION CORRESPONDANCE FORCÉE - RÉPARATION CRITIQUE
- Les neurones et accélérateurs disparaissent à chaque sauvegarde
- Les formations sont présentes mais non appliquées
- Il faut forcer la correspondance directe
"""

import json
import time
import requests
from datetime import datetime

def restaurer_neurones_accelerateurs():
    """Restaure les neurones et accélérateurs manquants"""
    print("🧠 === RESTAURATION NEURONES ET ACCÉLÉRATEURS ===\n")
    
    try:
        with open('thermal_memory_persistent.json', 'r') as f:
            memory = json.load(f)
        
        # Créer des neurones persistants
        neurons = {}
        for i in range(20):  # Plus de neurones
            neuron_id = f"neuron_persistent_{int(time.time())}_{i}"
            neurons[neuron_id] = {
                "id": neuron_id,
                "type": "format_enforcement",
                "state": "active",
                "energy": 1.0,
                "priority": "CRITICAL",
                "function": "format_detection",
                "keywords": ["A)", "B)", "C)", "D)", "Réponds par la lettre"],
                "action": "force_format_response"
            }
        
        # Créer des accélérateurs persistants
        accelerators = {}
        accelerator_configs = [
            {"type": "format_detector", "boost": 5.0, "keywords": ["A)", "B)", "C)", "D)"]},
            {"type": "response_formatter", "boost": 4.0, "action": "format_response"},
            {"type": "qi_test_optimizer", "boost": 3.0, "keywords": ["TEST QI", "test de qi"]},
            {"type": "math_calculator", "boost": 3.0, "keywords": ["=", "+", "-", "*", "/"]},
            {"type": "logic_processor", "boost": 4.0, "keywords": ["Si tous", "alors"]},
        ]
        
        for i, config in enumerate(accelerator_configs):
            acc_id = f"accelerator_persistent_{config['type']}_{int(time.time())}"
            accelerators[acc_id] = {
                "id": acc_id,
                "type": config["type"],
                "boost_factor": config["boost"],
                "active": True,
                "persistent": True,
                "keywords": config.get("keywords", []),
                "action": config.get("action", "boost_processing"),
                "priority": "CRITICAL"
            }
        
        # Sauvegarder dans la structure principale
        memory['neurons'] = neurons
        memory['accelerators'] = accelerators
        memory['persistent_systems'] = {
            'neurons_count': len(neurons),
            'accelerators_count': len(accelerators),
            'last_restore': datetime.now().isoformat()
        }
        
        with open('thermal_memory_persistent.json', 'w') as f:
            json.dump(memory, f, indent=2, ensure_ascii=False)
        
        print(f"✅ {len(neurons)} neurones persistants créés")
        print(f"✅ {len(accelerators)} accélérateurs persistants créés")
        return True
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

def creer_correspondances_forcees():
    """Crée des correspondances forcées mots-clés → actions"""
    print("\n🔗 === CORRESPONDANCES FORCÉES ===\n")
    
    try:
        with open('thermal_memory_persistent.json', 'r') as f:
            memory = json.load(f)
        
        # Correspondances directes
        correspondances = {
            "format_detection": {
                "keywords": ["A)", "B)", "C)", "D)", "Réponds par la lettre"],
                "action": "FORCE_FORMAT_RESPONSE",
                "template": "Réponse: {LETTER}",
                "priority": 1.0,
                "mandatory": True
            },
            "math_detection": {
                "keywords": ["=", "+", "-", "*", "/", "suite", "calcule"],
                "action": "CALCULATE_AND_FORMAT",
                "priority": 0.9,
                "mandatory": True
            },
            "logic_detection": {
                "keywords": ["Si tous", "alors", "sont", "est"],
                "action": "APPLY_LOGIC_AND_FORMAT",
                "priority": 0.9,
                "mandatory": True
            }
        }
        
        # Ajouter les correspondances
        memory['forced_correspondences'] = correspondances
        
        # Créer des formations de correspondance directe
        correspondance_formations = []
        
        for detection_type, config in correspondances.items():
            formation = {
                "id": f"FORCED_CORRESPONDENCE_{detection_type}_{int(time.time())}",
                "content": f"CORRESPONDANCE FORCÉE {detection_type.upper()}: Quand je détecte {config['keywords']}, j'applique OBLIGATOIREMENT l'action {config['action']}. PRIORITÉ MAXIMALE. AUCUNE EXCEPTION.",
                "importance": config['priority'],
                "timestamp": time.time(),
                "synaptic_strength": config['priority'],
                "zone": "procedural",
                "forced_correspondence": True,
                "detection_type": detection_type,
                "keywords": config['keywords'],
                "action": config['action'],
                "mandatory": config['mandatory'],
                "priority": "ULTRA_CRITICAL"
            }
            correspondance_formations.append(formation)
        
        # Ajouter en zone procedural
        if 'procedural' not in memory['thermal_zones']:
            memory['thermal_zones']['procedural'] = {'entries': []}
        
        # Insérer en PREMIÈRE position
        for formation in reversed(correspondance_formations):
            memory['thermal_zones']['procedural']['entries'].insert(0, formation)
        
        with open('thermal_memory_persistent.json', 'w') as f:
            json.dump(memory, f, indent=2, ensure_ascii=False)
        
        print(f"✅ {len(correspondances)} correspondances forcées créées")
        print(f"✅ {len(correspondance_formations)} formations de correspondance ajoutées")
        return True
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

def tester_correspondances():
    """Teste les correspondances forcées"""
    print("\n🧪 === TEST CORRESPONDANCES FORCÉES ===\n")
    
    agent_url = "http://localhost:5002/api/chat/message"
    
    tests = [
        {
            "name": "Format simple",
            "question": "1+1 = ? A) 1 B) 2 C) 3 D) 4. Réponds par la lettre A, B, C ou D.",
            "expected": "Réponse: B"
        },
        {
            "name": "Logique simple", 
            "question": "Si tous les chats sont des mammifères et Félix est un chat, alors Félix est : A) Un mammifère B) Un oiseau. Réponds par la lettre A, B, C ou D.",
            "expected": "Réponse: A"
        }
    ]
    
    # Authentification
    try:
        auth = requests.post(agent_url, json={"message": "Jeanpaul97180"}, timeout=30)
        print("✅ Authentifié")
    except:
        print("⚠️ Problème auth")
    
    # Tests
    for test in tests:
        print(f"\n🧪 Test: {test['name']}")
        print(f"❓ Question: {test['question']}")
        
        try:
            response = requests.post(agent_url, json={"message": test['question']}, timeout=30)
            if response.status_code == 200:
                result = response.json()
                agent_response = result.get('response', '')
                print(f"🤖 Réponse: {agent_response}")
                
                if agent_response.startswith(test['expected']):
                    print("✅ CORRESPONDANCE RÉUSSIE !")
                    return True
                else:
                    print("❌ Correspondance échouée")
            else:
                print(f"❌ Erreur: {response.status_code}")
        except Exception as e:
            print(f"❌ Erreur: {e}")
        
        time.sleep(3)
    
    return False

def main():
    """Lance la solution complète"""
    print("🔥 === SOLUTION CORRESPONDANCE FORCÉE ===\n")
    
    print("🔍 PROBLÈMES IDENTIFIÉS:")
    print("1. ❌ Neurones et accélérateurs disparaissent")
    print("2. ❌ Formations présentes mais non appliquées")
    print("3. ❌ Système de recherche cassé")
    print("4. ❌ Pas de correspondance directe mots-clés → actions")
    
    print("\n🔧 === RÉPARATIONS ===")
    
    success1 = restaurer_neurones_accelerateurs()
    success2 = creer_correspondances_forcees()
    
    if success1 and success2:
        print("\n🎉 === RÉPARATIONS TERMINÉES ===")
        print("✅ Neurones et accélérateurs persistants créés")
        print("✅ Correspondances forcées installées")
        print("\n🚀 REDÉMARREZ LOUNA ET TESTEZ !")
        
        test_choice = input("\nTester maintenant ? (o/n): ")
        if test_choice.lower() == 'o':
            if tester_correspondances():
                print("\n🎉 SUCCESS ! Les correspondances fonctionnent !")
            else:
                print("\n❌ Les correspondances ne fonctionnent pas encore")
    else:
        print("\n❌ Certaines réparations ont échoué")

if __name__ == "__main__":
    main()
