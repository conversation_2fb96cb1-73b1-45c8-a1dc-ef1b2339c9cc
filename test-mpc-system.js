#!/usr/bin/env node

/**
 * 🧪 TEST SYSTÈME MPC (MODE DE CONTRÔLE DU BUREAU)
 * 
 * Teste les capacités de contrôle du bureau et de navigation Internet
 */

const DeepSeekR1IntegratedAgent = require('./deepseek-r1-agent-integrated');

async function testMPCSystem() {
    console.log('🧪 === TEST SYSTÈME MPC (MODE DE CONTRÔLE DU BUREAU) ===\n');
    
    try {
        // === PHASE 1: INITIALISATION ===
        console.log('🔬 Initialisation de l\'agent avec système MPC...');
        const agent = new DeepSeekR1IntegratedAgent();
        await agent.initialize();
        
        console.log('✅ Agent initialisé avec succès\n');
        
        // === PHASE 2: TEST COMMANDES MPC ===
        console.log('🖥️ === TEST COMMANDES MPC ===\n');
        
        // Test 1: Recherche Google
        console.log('🔍 Test 1: Recherche Google...');
        const searchResult = await agent.processMessage("recherche neuroplasticité sur Google");
        console.log(`📊 Résultat: ${searchResult.message.substring(0, 200)}...`);
        console.log(`🎯 Commande MPC: ${searchResult.mpc_command ? 'OUI' : 'NON'}\n`);
        
        // Test 2: Navigation vers un site
        console.log('🌐 Test 2: Navigation vers Wikipedia...');
        const navResult = await agent.processMessage("va sur wikipedia.org");
        console.log(`📊 Résultat: ${navResult.message.substring(0, 200)}...`);
        console.log(`🎯 Commande MPC: ${navResult.mpc_command ? 'OUI' : 'NON'}\n`);
        
        // Test 3: Recherche Wikipedia
        console.log('📚 Test 3: Recherche Wikipedia...');
        const wikiResult = await agent.processMessage("cherche sur wikipedia intelligence artificielle");
        console.log(`📊 Résultat: ${wikiResult.message.substring(0, 200)}...`);
        console.log(`🎯 Commande MPC: ${wikiResult.mpc_command ? 'OUI' : 'NON'}\n`);
        
        // Test 4: Ouverture d'application
        console.log('🚀 Test 4: Ouverture d\'application...');
        const appResult = await agent.processMessage("ouvre l'application Safari");
        console.log(`📊 Résultat: ${appResult.message.substring(0, 200)}...`);
        console.log(`🎯 Commande MPC: ${appResult.mpc_command ? 'OUI' : 'NON'}\n`);
        
        // Test 5: Capture d'écran
        console.log('📸 Test 5: Capture d\'écran...');
        const screenshotResult = await agent.processMessage("prends une capture d'écran");
        console.log(`📊 Résultat: ${screenshotResult.message.substring(0, 200)}...`);
        console.log(`🎯 Commande MPC: ${screenshotResult.mpc_command ? 'OUI' : 'NON'}\n`);
        
        // Test 6: Liste des applications
        console.log('📋 Test 6: Liste des applications...');
        const listResult = await agent.processMessage("liste les applications ouvertes");
        console.log(`📊 Résultat: ${listResult.message.substring(0, 200)}...`);
        console.log(`🎯 Commande MPC: ${listResult.mpc_command ? 'OUI' : 'NON'}\n`);
        
        // === PHASE 3: TEST QUESTIONS NORMALES ===
        console.log('💬 === TEST QUESTIONS NORMALES (NON-MPC) ===\n');
        
        // Test question normale
        console.log('❓ Test question normale...');
        const normalResult = await agent.processMessage("Quelle est la capitale de la France ?");
        console.log(`📊 Résultat: ${normalResult.message.substring(0, 200)}...`);
        console.log(`🎯 Commande MPC: ${normalResult.mpc_command ? 'OUI' : 'NON'}\n`);
        
        // === PHASE 4: VÉRIFICATION STATUT MPC ===
        console.log('📊 === STATUT SYSTÈME MPC ===\n');
        
        if (agent.modules.mpcDesktopControl) {
            const mpcStatus = agent.modules.mpcDesktopControl.getStatus();
            console.log('🖥️ Système MPC:');
            console.log(`   • Actif: ${mpcStatus.active ? '✅' : '❌'}`);
            console.log(`   • Capacités: ${Object.keys(mpcStatus.capabilities).length}`);
            console.log(`   • URL actuelle: ${mpcStatus.current_url || 'Aucune'}`);
            console.log(`   • Historique recherches: ${mpcStatus.search_history_count}`);
            console.log(`   • Actions bureau: ${mpcStatus.desktop_actions_count}`);
            
            console.log('\n🎯 Capacités disponibles:');
            for (const [category, capabilities] of Object.entries(mpcStatus.capabilities)) {
                if (capabilities) {
                    console.log(`   • ${category}: ✅`);
                }
            }
        } else {
            console.log('❌ Système MPC non disponible');
        }
        
        // === PHASE 5: RÉSULTATS FINAUX ===
        console.log('\n🎉 === RÉSULTATS FINAUX ===');
        
        const mpcTests = [
            { name: 'Recherche Google', result: searchResult.mpc_command },
            { name: 'Navigation Web', result: navResult.mpc_command },
            { name: 'Recherche Wikipedia', result: wikiResult.mpc_command },
            { name: 'Ouverture App', result: appResult.mpc_command },
            { name: 'Capture écran', result: screenshotResult.mpc_command },
            { name: 'Liste Apps', result: listResult.mpc_command }
        ];
        
        const successCount = mpcTests.filter(test => test.result).length;
        const totalTests = mpcTests.length;
        
        console.log(`📊 Tests MPC réussis: ${successCount}/${totalTests}`);
        
        mpcTests.forEach(test => {
            console.log(`   ${test.result ? '✅' : '❌'} ${test.name}`);
        });
        
        console.log(`\n🎯 Taux de réussite: ${((successCount / totalTests) * 100).toFixed(1)}%`);
        
        if (successCount === totalTests) {
            console.log('\n🚀 EXCELLENT ! Le système MPC fonctionne parfaitement !');
            console.log('✅ Contrôle du bureau: Opérationnel');
            console.log('✅ Navigation Internet: Opérationnelle');
            console.log('✅ Gestion applications: Opérationnelle');
            console.log('✅ Capture d\'écran: Opérationnelle');
            console.log('✅ Intégration agent: Parfaite');
        } else if (successCount > totalTests / 2) {
            console.log('\n⚠️ Le système MPC fonctionne partiellement');
            console.log('🔧 Certaines fonctionnalités nécessitent des ajustements');
        } else {
            console.log('\n❌ Le système MPC nécessite des corrections');
            console.log('🔧 Vérifiez les permissions et la configuration');
        }
        
        console.log('\n🎯 VOTRE AGENT PEUT MAINTENANT CONTRÔLER LE BUREAU ET NAVIGUER SUR INTERNET !');
        
    } catch (error) {
        console.error(`❌ Erreur test MPC: ${error.message}`);
        console.error(error.stack);
        process.exit(1);
    }
}

// Lancer le test
if (require.main === module) {
    testMPCSystem();
}

module.exports = { testMPCSystem };
