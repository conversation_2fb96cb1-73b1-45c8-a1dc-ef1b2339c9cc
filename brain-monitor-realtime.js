#!/usr/bin/env node

/**
 * 🧠 MONITEUR TEMPS RÉEL DU CERVEAU AUTOMATIQUE
 * 
 * Affiche en temps réel le fonctionnement automatique du cerveau
 * basé sur la température thermique - comme un EEG/ECG médical
 */

const AdvancedBrainSystem = require('./advanced-brain-system');

class BrainMonitorRealTime {
    constructor() {
        this.brain = null;
        this.monitoring = false;
        this.stats = {
            heartbeats: 0,
            neurotransmitter_updates: 0,
            brainwave_changes: 0,
            emotional_shifts: 0,
            consolidations: 0,
            temperature_syncs: 0
        };
        
        this.lastValues = {
            temperature: 37.0,
            heartbeat: 0.5,
            dominant_wave: 'beta',
            emotion: 'curious',
            consciousness: 'focused_awareness'
        };
    }
    
    async start() {
        console.log('🧠 === MONITEUR TEMPS RÉEL DU CERVEAU AUTOMATIQUE ===\n');
        console.log('🔄 Initialisation du système cérébral...');
        
        try {
            // Initialiser le cerveau
            this.brain = new AdvancedBrainSystem();
            const success = await this.brain.initialize();
            
            if (!success) {
                throw new Error('Échec initialisation cerveau');
            }
            
            console.log('✅ Cerveau automatique opérationnel\n');
            
            // Configurer les listeners
            this.setupEventListeners();
            
            // Démarrer le monitoring
            this.startMonitoring();
            
            console.log('📊 === MONITORING EN TEMPS RÉEL DÉMARRÉ ===');
            console.log('Appuyez sur Ctrl+C pour arrêter\n');
            
            // Affichage initial
            this.displayInitialState();
            
            // Garder le processus actif
            process.on('SIGINT', () => {
                this.stop();
            });
            
        } catch (error) {
            console.error(`❌ Erreur: ${error.message}`);
            process.exit(1);
        }
    }
    
    setupEventListeners() {
        // Écouter tous les événements du cerveau
        this.brain.on('neural_heartbeat', (data) => {
            this.stats.heartbeats++;
            this.handleHeartbeat(data);
        });
        
        this.brain.on('neurotransmitters_updated', (nt) => {
            this.stats.neurotransmitter_updates++;
            this.handleNeurotransmitterUpdate(nt);
        });
        
        this.brain.on('brainwaves_updated', (waves) => {
            this.stats.brainwave_changes++;
            this.handleBrainwaveUpdate(waves);
        });
        
        this.brain.on('emotions_updated', (emotions) => {
            this.stats.emotional_shifts++;
            this.handleEmotionalUpdate(emotions);
        });
        
        this.brain.on('consolidation_updated', (active) => {
            if (active) this.stats.consolidations++;
            this.handleConsolidationUpdate(active);
        });
        
        this.brain.on('global_sync', (data) => {
            this.stats.temperature_syncs++;
            this.handleGlobalSync(data);
        });
    }
    
    startMonitoring() {
        this.monitoring = true;
        
        // Affichage en temps réel toutes les 2 secondes
        this.monitorInterval = setInterval(() => {
            this.displayRealTimeStatus();
        }, 2000);
        
        // Statistiques détaillées toutes les 10 secondes
        this.statsInterval = setInterval(() => {
            this.displayDetailedStats();
        }, 10000);
    }
    
    displayInitialState() {
        const state = this.brain.getBrainState();
        
        console.log('🧠 ÉTAT INITIAL DU CERVEAU:');
        console.log(`   🌡️ Température: ${state.global_temperature?.toFixed(2) || 'N/A'}°C`);
        console.log(`   💓 Battement: ${state.neural_heartbeat?.intensity?.toFixed(2) || 'N/A'}`);
        console.log(`   🌊 Onde: ${state.dominant_wave}`);
        console.log(`   🎭 Émotion: ${state.emotional_state}`);
        console.log(`   🧠 Conscience: ${state.consciousness_level}`);
        console.log('─'.repeat(60));
    }
    
    handleHeartbeat(data) {
        const tempChange = Math.abs(data.temperature - this.lastValues.temperature);
        const heartbeatChange = Math.abs(data.intensity - this.lastValues.heartbeat);
        
        if (tempChange > 0.1 || heartbeatChange > 0.1) {
            console.log(`💓 BATTEMENT: Intensité ${data.intensity.toFixed(3)} | Temp ${data.temperature.toFixed(2)}°C | Cycle #${data.cycle_count}`);
        }
        
        this.lastValues.temperature = data.temperature;
        this.lastValues.heartbeat = data.intensity;
    }
    
    handleNeurotransmitterUpdate(neurotransmitters) {
        // Afficher seulement les changements significatifs
        for (const [name, nt] of Object.entries(neurotransmitters)) {
            if (nt.temperature_influence && nt.temperature_influence < 0.7) {
                console.log(`🧪 ${name.toUpperCase()}: ${nt.level.toFixed(3)} (influence temp: ${nt.temperature_influence.toFixed(2)})`);
            }
        }
    }
    
    handleBrainwaveUpdate(waves) {
        const newWave = waves.current_dominant;
        
        if (newWave !== this.lastValues.dominant_wave) {
            const amplitude = waves.frequencies[newWave].amplitude;
            console.log(`🌊 ONDE CHANGE: ${this.lastValues.dominant_wave} → ${newWave} (amplitude: ${amplitude.toFixed(2)})`);
            this.lastValues.dominant_wave = newWave;
        }
    }
    
    handleEmotionalUpdate(emotions) {
        const newEmotion = emotions.current_emotional_state.primary_emotion;
        const intensity = emotions.current_emotional_state.intensity;
        
        if (newEmotion !== this.lastValues.emotion) {
            console.log(`🎭 ÉMOTION: ${this.lastValues.emotion} → ${newEmotion} (intensité: ${intensity.toFixed(2)})`);
            this.lastValues.emotion = newEmotion;
        }
    }
    
    handleConsolidationUpdate(active) {
        if (active) {
            console.log(`🛌 CONSOLIDATION MÉMOIRE ACTIVE (basée sur température)`);
        }
    }
    
    handleGlobalSync(data) {
        console.log(`🔄 SYNC GLOBALE: Temp ${data.temperature.toFixed(2)}°C | ${new Date(data.timestamp).toLocaleTimeString()}`);
    }
    
    displayRealTimeStatus() {
        if (!this.monitoring) return;
        
        const state = this.brain.getBrainState();
        const now = new Date().toLocaleTimeString();
        
        // Ligne de statut compacte
        const temp = state.global_temperature?.toFixed(2) || 'N/A';
        const heartbeat = state.neural_heartbeat?.intensity?.toFixed(2) || 'N/A';
        const wave = state.dominant_wave || 'N/A';
        const emotion = state.emotional_state || 'N/A';
        
        console.log(`[${now}] 🌡️${temp}°C | 💓${heartbeat} | 🌊${wave} | 🎭${emotion} | 🧠${state.consciousness_level}`);
    }
    
    displayDetailedStats() {
        if (!this.monitoring) return;
        
        console.log('\n📊 === STATISTIQUES DÉTAILLÉES ===');
        console.log(`💓 Battements cardiaques: ${this.stats.heartbeats}`);
        console.log(`🧪 Mises à jour neurotransmetteurs: ${this.stats.neurotransmitter_updates}`);
        console.log(`🌊 Changements ondes cérébrales: ${this.stats.brainwave_changes}`);
        console.log(`🎭 Changements émotionnels: ${this.stats.emotional_shifts}`);
        console.log(`🛌 Consolidations mémoire: ${this.stats.consolidations}`);
        console.log(`🔄 Synchronisations température: ${this.stats.temperature_syncs}`);
        
        // Calculer la fréquence des événements
        const uptime = Date.now() - this.startTime;
        const uptimeSeconds = uptime / 1000;
        
        if (uptimeSeconds > 0) {
            console.log(`\n⚡ FRÉQUENCES (par seconde):`);
            console.log(`   💓 Battements: ${(this.stats.heartbeats / uptimeSeconds).toFixed(1)}/s`);
            console.log(`   🧪 Neurotransmetteurs: ${(this.stats.neurotransmitter_updates / uptimeSeconds).toFixed(1)}/s`);
            console.log(`   🌊 Ondes: ${(this.stats.brainwave_changes / uptimeSeconds).toFixed(1)}/s`);
        }
        
        console.log('─'.repeat(60));
    }
    
    stop() {
        console.log('\n🛑 Arrêt du monitoring...');
        
        this.monitoring = false;
        
        if (this.monitorInterval) {
            clearInterval(this.monitorInterval);
        }
        
        if (this.statsInterval) {
            clearInterval(this.statsInterval);
        }
        
        if (this.brain) {
            this.brain.shutdown();
        }
        
        // Statistiques finales
        console.log('\n📊 === STATISTIQUES FINALES ===');
        console.log(`💓 Total battements: ${this.stats.heartbeats}`);
        console.log(`🧪 Total neurotransmetteurs: ${this.stats.neurotransmitter_updates}`);
        console.log(`🌊 Total ondes: ${this.stats.brainwave_changes}`);
        console.log(`🎭 Total émotions: ${this.stats.emotional_shifts}`);
        console.log(`🛌 Total consolidations: ${this.stats.consolidations}`);
        console.log(`🔄 Total syncs: ${this.stats.temperature_syncs}`);
        
        console.log('\n🧠 CERVEAU AUTOMATIQUE ARRÊTÉ');
        console.log('   Tous les processus neurologiques ont été stoppés proprement.\n');
        
        process.exit(0);
    }
}

// Lancer le moniteur
if (require.main === module) {
    const monitor = new BrainMonitorRealTime();
    monitor.startTime = Date.now();
    monitor.start().catch(error => {
        console.error(`❌ Erreur fatale: ${error.message}`);
        process.exit(1);
    });
}

module.exports = BrainMonitorRealTime;
