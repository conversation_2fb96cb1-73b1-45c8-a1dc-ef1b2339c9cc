#!/usr/bin/env python3
"""
🔍 VÉRIFICATION COMPLÈTE ET MINUTIEUSE DU SYSTÈME MÉMOIRE LOUNA
- Intégrité des données
- Mécanismes de sauvegarde
- Prévention de perte de données
- Performance et optimisation
- Tests de stress
"""

import json
import os
import time
import hashlib
import shutil
from datetime import datetime

class MemorySystemAuditor:
    def __init__(self):
        self.memory_file = 'thermal_memory_persistent.json'
        self.backup_dir = 'memory_backups'
        self.errors = []
        self.warnings = []
        self.stats = {}
        
    def run_complete_audit(self):
        """Exécute un audit complet du système mémoire"""
        print("🔍 === AUDIT COMPLET DU SYSTÈME MÉMOIRE LOUNA ===\n")
        
        # Tests de base
        self.test_file_integrity()
        self.test_json_structure()
        self.test_memory_zones()
        self.test_data_consistency()
        
        # Tests avancés
        self.test_backup_system()
        self.test_memory_performance()
        self.test_data_persistence()
        self.test_corruption_resistance()
        
        # Tests de stress
        self.stress_test_memory()
        self.test_concurrent_access()
        
        # Rapport final
        self.generate_report()
        
    def test_file_integrity(self):
        """Teste l'intégrité du fichier de mémoire"""
        print("📁 === TEST INTÉGRITÉ FICHIER ===")
        
        if not os.path.exists(self.memory_file):
            self.errors.append("❌ Fichier de mémoire manquant")
            return
            
        # Taille du fichier
        file_size = os.path.getsize(self.memory_file)
        print(f"📊 Taille fichier: {file_size:,} bytes")
        
        if file_size < 1000:
            self.warnings.append("⚠️ Fichier de mémoire très petit")
        elif file_size > 10_000_000:
            self.warnings.append("⚠️ Fichier de mémoire très volumineux")
        else:
            print("✅ Taille fichier normale")
            
        # Permissions
        if os.access(self.memory_file, os.R_OK | os.W_OK):
            print("✅ Permissions lecture/écriture OK")
        else:
            self.errors.append("❌ Problème de permissions")
            
        self.stats['file_size'] = file_size
        print()
        
    def test_json_structure(self):
        """Teste la structure JSON"""
        print("🔧 === TEST STRUCTURE JSON ===")
        
        try:
            with open(self.memory_file, 'r', encoding='utf-8') as f:
                memory = json.load(f)
            print("✅ JSON valide")
            
            # Structure requise
            required_keys = ['thermal_zones', 'timestamp']
            for key in required_keys:
                if key in memory:
                    print(f"✅ Clé '{key}' présente")
                else:
                    self.errors.append(f"❌ Clé manquante: {key}")
                    
            # Zones thermiques requises
            required_zones = ['sensory', 'working', 'short_term', 'episodic', 'semantic', 'procedural']
            thermal_zones = memory.get('thermal_zones', {})
            
            for zone in required_zones:
                if zone in thermal_zones:
                    print(f"✅ Zone '{zone}' présente")
                else:
                    self.errors.append(f"❌ Zone manquante: {zone}")
                    
            self.stats['memory_data'] = memory
            
        except json.JSONDecodeError as e:
            self.errors.append(f"❌ JSON invalide: {e}")
        except Exception as e:
            self.errors.append(f"❌ Erreur lecture: {e}")
            
        print()
        
    def test_memory_zones(self):
        """Teste chaque zone mémoire"""
        print("🧠 === TEST ZONES MÉMOIRE ===")
        
        if 'memory_data' not in self.stats:
            print("❌ Données mémoire non disponibles")
            return
            
        memory = self.stats['memory_data']
        thermal_zones = memory.get('thermal_zones', {})
        total_entries = 0
        
        for zone_name, zone_data in thermal_zones.items():
            entries = zone_data.get('entries', [])
            entry_count = len(entries)
            total_entries += entry_count
            
            print(f"📊 Zone {zone_name}: {entry_count} entrées")
            
            # Vérifier la structure des entrées
            valid_entries = 0
            for entry in entries:
                if self.validate_entry(entry):
                    valid_entries += 1
                    
            if valid_entries == entry_count:
                print(f"✅ Toutes les entrées de {zone_name} sont valides")
            else:
                self.warnings.append(f"⚠️ {entry_count - valid_entries} entrées invalides dans {zone_name}")
                
        print(f"📊 Total entrées: {total_entries}")
        self.stats['total_entries'] = total_entries
        print()
        
    def validate_entry(self, entry):
        """Valide une entrée mémoire"""
        required_fields = ['id', 'content', 'importance', 'timestamp']
        return all(field in entry for field in required_fields)
        
    def test_data_consistency(self):
        """Teste la cohérence des données"""
        print("🔍 === TEST COHÉRENCE DONNÉES ===")
        
        if 'memory_data' not in self.stats:
            return
            
        memory = self.stats['memory_data']
        
        # Vérifier les IDs uniques
        all_ids = []
        for zone_data in memory.get('thermal_zones', {}).values():
            for entry in zone_data.get('entries', []):
                all_ids.append(entry.get('id'))
                
        unique_ids = set(all_ids)
        if len(all_ids) == len(unique_ids):
            print("✅ Tous les IDs sont uniques")
        else:
            self.errors.append(f"❌ {len(all_ids) - len(unique_ids)} IDs dupliqués")
            
        # Vérifier les timestamps
        invalid_timestamps = 0
        for zone_data in memory.get('thermal_zones', {}).values():
            for entry in zone_data.get('entries', []):
                timestamp = entry.get('timestamp')
                if not self.validate_timestamp(timestamp):
                    invalid_timestamps += 1
                    
        if invalid_timestamps == 0:
            print("✅ Tous les timestamps sont valides")
        else:
            self.warnings.append(f"⚠️ {invalid_timestamps} timestamps invalides")
            
        print()
        
    def validate_timestamp(self, timestamp):
        """Valide un timestamp"""
        if isinstance(timestamp, (int, float)):
            return timestamp > 0
        elif isinstance(timestamp, str):
            try:
                datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
                return True
            except:
                return False
        return False
        
    def test_backup_system(self):
        """Teste le système de sauvegarde"""
        print("💾 === TEST SYSTÈME SAUVEGARDE ===")
        
        # Créer le dossier de backup s'il n'existe pas
        if not os.path.exists(self.backup_dir):
            os.makedirs(self.backup_dir)
            print(f"📁 Dossier backup créé: {self.backup_dir}")
            
        # Créer une sauvegarde de test
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_file = f"{self.backup_dir}/memory_backup_{timestamp}.json"
        
        try:
            shutil.copy2(self.memory_file, backup_file)
            print(f"✅ Sauvegarde créée: {backup_file}")
            
            # Vérifier l'intégrité de la sauvegarde
            if self.verify_backup_integrity(self.memory_file, backup_file):
                print("✅ Intégrité sauvegarde vérifiée")
            else:
                self.errors.append("❌ Sauvegarde corrompue")
                
        except Exception as e:
            self.errors.append(f"❌ Erreur sauvegarde: {e}")
            
        print()
        
    def verify_backup_integrity(self, original, backup):
        """Vérifie l'intégrité d'une sauvegarde"""
        try:
            with open(original, 'rb') as f1, open(backup, 'rb') as f2:
                hash1 = hashlib.md5(f1.read()).hexdigest()
                hash2 = hashlib.md5(f2.read()).hexdigest()
                return hash1 == hash2
        except:
            return False
            
    def test_memory_performance(self):
        """Teste les performances mémoire"""
        print("⚡ === TEST PERFORMANCE MÉMOIRE ===")
        
        # Test de lecture
        start_time = time.time()
        try:
            with open(self.memory_file, 'r') as f:
                json.load(f)
            read_time = time.time() - start_time
            print(f"📊 Temps lecture: {read_time:.3f}s")
            
            if read_time < 0.1:
                print("✅ Performance lecture excellente")
            elif read_time < 0.5:
                print("✅ Performance lecture bonne")
            else:
                self.warnings.append("⚠️ Performance lecture lente")
                
        except Exception as e:
            self.errors.append(f"❌ Erreur test lecture: {e}")
            
        print()
        
    def test_data_persistence(self):
        """Teste la persistance des données"""
        print("🔒 === TEST PERSISTANCE DONNÉES ===")
        
        # Créer une entrée de test
        test_entry = {
            "id": f"test_persistence_{int(time.time())}",
            "content": "Test de persistance des données",
            "importance": 0.5,
            "timestamp": time.time(),
            "zone": "working",
            "test_marker": True
        }
        
        try:
            # Ajouter l'entrée
            if self.add_test_entry(test_entry):
                print("✅ Entrée test ajoutée")
                
                # Vérifier qu'elle persiste
                if self.verify_test_entry(test_entry['id']):
                    print("✅ Persistance vérifiée")
                    
                    # Nettoyer
                    if self.remove_test_entry(test_entry['id']):
                        print("✅ Nettoyage effectué")
                    else:
                        self.warnings.append("⚠️ Problème nettoyage")
                else:
                    self.errors.append("❌ Persistance échouée")
            else:
                self.errors.append("❌ Ajout entrée test échoué")
                
        except Exception as e:
            self.errors.append(f"❌ Erreur test persistance: {e}")
            
        print()

    def add_test_entry(self, entry):
        """Ajoute une entrée de test"""
        try:
            with open(self.memory_file, 'r') as f:
                memory = json.load(f)

            zone = entry['zone']
            if zone not in memory['thermal_zones']:
                memory['thermal_zones'][zone] = {'entries': []}
            if 'entries' not in memory['thermal_zones'][zone]:
                memory['thermal_zones'][zone]['entries'] = []

            memory['thermal_zones'][zone]['entries'].append(entry)
            memory['timestamp'] = datetime.now().isoformat()

            with open(self.memory_file, 'w') as f:
                json.dump(memory, f, indent=2, ensure_ascii=False)

            return True
        except:
            return False

    def verify_test_entry(self, entry_id):
        """Vérifie qu'une entrée de test existe"""
        try:
            with open(self.memory_file, 'r') as f:
                memory = json.load(f)

            for zone_data in memory.get('thermal_zones', {}).values():
                for entry in zone_data.get('entries', []):
                    if entry.get('id') == entry_id:
                        return True
            return False
        except:
            return False

    def remove_test_entry(self, entry_id):
        """Supprime une entrée de test"""
        try:
            with open(self.memory_file, 'r') as f:
                memory = json.load(f)

            for zone_data in memory.get('thermal_zones', {}).values():
                entries = zone_data.get('entries', [])
                zone_data['entries'] = [e for e in entries if e.get('id') != entry_id]

            memory['timestamp'] = datetime.now().isoformat()

            with open(self.memory_file, 'w') as f:
                json.dump(memory, f, indent=2, ensure_ascii=False)

            return True
        except:
            return False

    def test_corruption_resistance(self):
        """Teste la résistance à la corruption"""
        print("🛡️ === TEST RÉSISTANCE CORRUPTION ===")

        # Créer une copie de sauvegarde
        backup_temp = f"{self.memory_file}.temp_backup"
        try:
            shutil.copy2(self.memory_file, backup_temp)

            # Simuler une corruption légère
            with open(self.memory_file, 'r') as f:
                content = f.read()

            # Corruption simulée (enlever une accolade)
            corrupted_content = content[:-10] + content[-9:]

            with open(self.memory_file, 'w') as f:
                f.write(corrupted_content)

            # Tenter de lire le fichier corrompu
            try:
                with open(self.memory_file, 'r') as f:
                    json.load(f)
                self.warnings.append("⚠️ Corruption non détectée")
            except json.JSONDecodeError:
                print("✅ Corruption détectée correctement")

            # Restaurer le fichier
            shutil.move(backup_temp, self.memory_file)
            print("✅ Fichier restauré")

        except Exception as e:
            self.errors.append(f"❌ Erreur test corruption: {e}")
            # Restaurer en cas d'erreur
            if os.path.exists(backup_temp):
                shutil.move(backup_temp, self.memory_file)

        print()

    def stress_test_memory(self):
        """Test de stress du système mémoire"""
        print("💪 === TEST DE STRESS MÉMOIRE ===")

        # Test de lecture répétée
        print("🔄 Test lecture répétée...")
        start_time = time.time()
        for i in range(100):
            try:
                with open(self.memory_file, 'r') as f:
                    json.load(f)
            except:
                self.errors.append(f"❌ Échec lecture #{i}")
                break

        read_stress_time = time.time() - start_time
        print(f"📊 100 lectures en {read_stress_time:.3f}s")

        if read_stress_time < 5.0:
            print("✅ Performance stress excellente")
        else:
            self.warnings.append("⚠️ Performance stress dégradée")

        print()

    def test_concurrent_access(self):
        """Teste l'accès concurrent"""
        print("🔀 === TEST ACCÈS CONCURRENT ===")

        # Simuler un accès concurrent simple
        try:
            # Ouvrir le fichier en lecture
            with open(self.memory_file, 'r') as f1:
                # Tenter d'ouvrir en écriture simultanément
                try:
                    with open(self.memory_file, 'r') as f2:
                        data1 = json.load(f1)
                        data2 = json.load(f2)

                    if data1 == data2:
                        print("✅ Lecture concurrent OK")
                    else:
                        self.warnings.append("⚠️ Incohérence lecture concurrent")

                except Exception as e:
                    self.warnings.append(f"⚠️ Problème accès concurrent: {e}")

        except Exception as e:
            self.errors.append(f"❌ Erreur test concurrent: {e}")

        print()

    def generate_report(self):
        """Génère le rapport final"""
        print("📋 === RAPPORT FINAL AUDIT MÉMOIRE ===")

        # Statistiques
        print(f"📊 Entrées totales: {self.stats.get('total_entries', 'N/A')}")
        print(f"📊 Taille fichier: {self.stats.get('file_size', 'N/A'):,} bytes")

        # Erreurs
        if self.errors:
            print(f"\n❌ ERREURS CRITIQUES ({len(self.errors)}):")
            for error in self.errors:
                print(f"   {error}")
        else:
            print("\n✅ AUCUNE ERREUR CRITIQUE")

        # Avertissements
        if self.warnings:
            print(f"\n⚠️ AVERTISSEMENTS ({len(self.warnings)}):")
            for warning in self.warnings:
                print(f"   {warning}")
        else:
            print("\n✅ AUCUN AVERTISSEMENT")

        # Score global
        total_tests = 10
        failed_tests = len(self.errors)
        warning_tests = len(self.warnings)

        score = max(0, (total_tests - failed_tests - warning_tests * 0.5) / total_tests * 100)

        print(f"\n🎯 SCORE GLOBAL: {score:.1f}%")

        if score >= 95:
            print("🏆 EXCELLENT - Système mémoire parfait")
        elif score >= 85:
            print("🥇 TRÈS BON - Système mémoire fiable")
        elif score >= 70:
            print("🥈 BON - Système mémoire acceptable")
        elif score >= 50:
            print("🥉 MOYEN - Améliorations nécessaires")
        else:
            print("❌ CRITIQUE - Intervention urgente requise")

        # Recommandations
        print(f"\n💡 RECOMMANDATIONS:")
        if not self.errors and not self.warnings:
            print("   ✅ Système optimal, aucune action requise")
        else:
            if self.errors:
                print("   🔧 Corriger les erreurs critiques immédiatement")
            if self.warnings:
                print("   ⚠️ Surveiller les points d'attention")
            print("   💾 Maintenir les sauvegardes régulières")
            print("   🔍 Effectuer des audits périodiques")

def main():
    """Fonction principale"""
    auditor = MemorySystemAuditor()
    auditor.run_complete_audit()

if __name__ == "__main__":
    main()
