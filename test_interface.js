#!/usr/bin/env node

/**
 * Test de l'interface Socket.IO
 * Simule une interaction utilisateur avec l'agent
 */

const io = require('socket.io-client');

console.log('🧪 === TEST DE L\'INTERFACE SOCKET.IO ===\n');

// Connexion au serveur
const socket = io('http://localhost:3000');

socket.on('connect', () => {
    console.log('✅ Connecté au serveur Socket.IO');
    console.log('🆔 Socket ID:', socket.id);
    
    // Test 1: Question simple
    setTimeout(() => {
        console.log('\n📤 Test 1: Envoi de la question "Quelle est la capitale de la France ?"');
        
        const messageData = {
            id: 1,
            message: "Quelle est la capitale de la France ?",
            timestamp: Date.now()
        };
        
        socket.emit('user_message', messageData);
    }, 1000);
    
    // Test 2: Question sur l'agent
    setTimeout(() => {
        console.log('\n📤 Test 2: Envoi de la question "Qui es-tu ?"');
        
        const messageData = {
            id: 2,
            message: "Qui es-tu ?",
            timestamp: Date.now()
        };
        
        socket.emit('user_message', messageData);
    }, 5000);
    
    // Test 3: Question sur le QI
    setTimeout(() => {
        console.log('\n📤 Test 3: Envoi de la question "Quel est ton QI ?"');
        
        const messageData = {
            id: 3,
            message: "Quel est ton QI ?",
            timestamp: Date.now()
        };
        
        socket.emit('user_message', messageData);
    }, 10000);
    
    // Fermeture après 15 secondes
    setTimeout(() => {
        console.log('\n🔚 Fin des tests, fermeture de la connexion');
        socket.disconnect();
        process.exit(0);
    }, 15000);
});

socket.on('disconnect', () => {
    console.log('👋 Déconnecté du serveur');
});

socket.on('connect_error', (error) => {
    console.error('❌ Erreur de connexion:', error.message);
});

// Écoute des réponses de l'agent
socket.on('agent_response', (data) => {
    console.log('\n📥 === RÉPONSE DE L\'AGENT ===');
    console.log('💬 Message:', data.message);
    if (data.reflection) {
        console.log('🧠 Réflexion:', data.reflection);
    }
    if (data.memory_used && data.memory_used.length > 0) {
        console.log('💾 Mémoires utilisées:', data.memory_used.length);
    }
    console.log('⏰ Timestamp:', new Date(data.timestamp).toLocaleTimeString());
    console.log('='.repeat(50));
});

// Écoute des étapes de réflexion
socket.on('reflection_step', (data) => {
    console.log(`🧠 [${data.status.toUpperCase()}] ${data.text}`);
    if (data.reflection) {
        console.log(`   💭 ${data.reflection}`);
    }
});

// Écoute des erreurs
socket.on('error', (error) => {
    console.error('❌ Erreur Socket.IO:', error);
});

console.log('🔄 Connexion en cours...');
