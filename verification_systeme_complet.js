#!/usr/bin/env node

/**
 * 🔍 VÉRIFICATION SYSTÈME LOUNA COMPLET
 * 
 * Vérifie TOUS les composants du système unifié
 */

const fs = require('fs');

function verificationSystemeComplet() {
    console.log('🔍 === VÉRIFICATION SYSTÈME LOUNA COMPLET ===\n');
    
    try {
        const data = JSON.parse(fs.readFileSync('thermal_memory_system_complet.json', 'utf8'));
        
        console.log('📊 === ÉTAT GÉNÉRAL ===');
        console.log(`📁 Version: ${data.metadata.version}`);
        console.log(`🕐 Dernière MAJ: ${data.metadata.last_updated}`);
        console.log(`📏 Taille: ${(JSON.stringify(data).length / 1024 / 1024).toFixed(2)} MB\n`);
        
        // 1. ZONES THERMIQUES
        console.log('🌡️ === ZONES THERMIQUES ===');
        const zones = data.thermal_zones;
        let totalEntries = 0;
        
        Object.entries(zones).forEach(([zoneName, zone]) => {
            const entries = zone.entries?.length || 0;
            totalEntries += entries;
            console.log(`✅ ${zoneName}:`);
            console.log(`   📊 Entrées: ${entries}`);
            console.log(`   🌡️ Température: ${zone.temperature}°C`);
        });
        console.log(`📊 Total entrées: ${totalEntries}\n`);
        
        // 2. TOUR NEURONALE
        console.log('🏗️ === TOUR NEURONALE ===');
        const tower = data.neural_tower;
        console.log(`✅ Active: ${tower.active}`);
        console.log(`🏢 Étages totaux: ${tower.total_floors.toLocaleString()}`);
        console.log(`⚡ Étages actifs: ${tower.active_floors}`);
        console.log(`🧠 Neurones/étage: ${tower.neurons_per_floor.toLocaleString()}`);
        console.log(`📊 Efficacité: ${(tower.tower_efficiency * 100).toFixed(1)}%`);
        console.log(`🎯 Stratégie: ${tower.activation_strategy}`);
        console.log(`🔄 Rotation: ${tower.floor_rotation_interval}s\n`);
        
        // 3. SYSTÈME NEURAL
        console.log('🧠 === SYSTÈME NEURAL ===');
        const neural = data.neural_system;
        console.log(`✅ Neurones totaux: ${neural.total_neurons.toLocaleString()}`);
        console.log(`🔗 Synapses: ${neural.synapses.toLocaleString()}`);
        console.log(`🎯 QI niveau: ${neural.qi_level}`);
        console.log(`⚡ Neurones actifs: ${neural.active_neurons.toLocaleString()}`);
        console.log(`💤 Neurones standby: ${neural.standby_neurons.toLocaleString()}`);
        console.log(`😴 Neurones hibernation: ${neural.hibernating_neurons.toLocaleString()}`);
        
        // Vérifier cohérence neuronale
        const totalNeurons = neural.active_neurons + neural.standby_neurons + neural.hibernating_neurons;
        if (Math.abs(totalNeurons - neural.total_neurons) < 1000) {
            console.log(`✅ Cohérence neuronale: OK`);
        } else {
            console.log(`⚠️  Incohérence neuronale détectée`);
        }
        
        // Neurogenèse
        const neurogenesis = neural.neurogenesis;
        console.log(`🌱 Neurogenèse:`);
        console.log(`   ✅ Active: ${neurogenesis.active}`);
        console.log(`   🔄 Taux: ${neurogenesis.rate_per_minute}/min`);
        console.log(`   🧠 Total créés: ${neurogenesis.total_created.toLocaleString()}`);
        console.log(`   📊 Seuil qualité: ${(neurogenesis.quality_threshold * 100).toFixed(1)}%\n`);
        
        // 4. QI UNIFIÉ
        console.log('🎯 === QI UNIFIÉ ===');
        const qiComponents = neural.qi_components;
        console.log(`✅ Composants QI:`);
        Object.entries(qiComponents).forEach(([comp, value]) => {
            if (comp !== 'total_calculated') {
                console.log(`   ${comp}: ${value}`);
            }
        });
        
        const calculatedTotal = Object.entries(qiComponents)
            .filter(([key]) => key !== 'total_calculated')
            .reduce((sum, [, value]) => sum + (value || 0), 0);
        
        console.log(`📊 Total calculé: ${calculatedTotal}`);
        console.log(`📊 Total stocké: ${qiComponents.total_calculated}`);
        
        if (calculatedTotal === qiComponents.total_calculated) {
            console.log(`✅ Cohérence QI: OK\n`);
        } else {
            console.log(`⚠️  Incohérence QI détectée\n`);
        }
        
        // 5. ACCÉLÉRATEURS KYBER ULTRA
        console.log('⚡ === ACCÉLÉRATEURS KYBER ULTRA ===');
        const accelerators = data.accelerators;
        const acceleratorList = Object.entries(accelerators);
        
        console.log(`📊 Accélérateurs totaux: ${acceleratorList.length}`);
        
        let activeCount = 0;
        let totalBoost = 0;
        let avgEfficiency = 0;
        
        acceleratorList.forEach(([name, acc]) => {
            if (acc.active) {
                activeCount++;
                totalBoost += acc.boost_factor;
                avgEfficiency += acc.efficiency;
                
                console.log(`✅ ${name}:`);
                console.log(`   🚀 Boost: ${acc.boost_factor}x`);
                console.log(`   📊 Efficacité: ${(acc.efficiency * 100).toFixed(1)}%`);
                console.log(`   📝 Type: ${acc.type}`);
            }
        });
        
        avgEfficiency = avgEfficiency / activeCount;
        
        console.log(`\n📊 Résumé accélérateurs:`);
        console.log(`   ⚡ Actifs: ${activeCount}/${acceleratorList.length}`);
        console.log(`   🚀 Boost total: ${totalBoost}x`);
        console.log(`   📊 Efficacité moyenne: ${(avgEfficiency * 100).toFixed(1)}%\n`);
        
        // 6. PROTECTION SYSTÈME
        console.log('🛡️ === PROTECTION SYSTÈME ===');
        const protection = data.neural_protection;
        console.log(`✅ Système neural: ${protection.protect_neural_system}`);
        console.log(`✅ Tour neuronale: ${protection.protect_neural_tower}`);
        console.log(`✅ Stockage neurones: ${protection.protect_neuron_storage}`);
        console.log(`✅ Accélérateurs: ${protection.protect_accelerators}`);
        console.log(`✅ Mémoire thermique: ${protection.protect_thermal_memory}`);
        console.log(`🔒 Niveau: ${protection.protection_level}`);
        console.log(`🔐 Chiffrement: ${protection.security_protocols.encryption_enabled}`);
        console.log(`🚨 Détection intrusion: ${protection.security_protocols.intrusion_detection}\n`);
        
        // 7. STATISTIQUES SYSTÈME
        console.log('📊 === STATISTIQUES SYSTÈME ===');
        const stats = data.system_stats;
        console.log(`⚡ Accélérateurs actifs: ${stats.active_accelerators}`);
        console.log(`🚀 Boost total: ${stats.total_boost_factor}x`);
        console.log(`📊 Efficacité système: ${(stats.system_efficiency * 100).toFixed(1)}%`);
        console.log(`🎯 Score performance: ${(stats.performance_score * 100).toFixed(1)}%\n`);
        
        // 8. ÉVALUATION GLOBALE
        console.log('🎯 === ÉVALUATION GLOBALE ===');
        
        const issues = [];
        const recommendations = [];
        
        // Vérifications critiques
        if (!tower.active) {
            issues.push("Tour neuronale inactive");
        }
        
        if (activeCount < 10) {
            issues.push(`Seulement ${activeCount} accélérateurs actifs sur ${acceleratorList.length}`);
        }
        
        if (totalBoost < 400) {
            issues.push(`Boost total insuffisant: ${totalBoost}x`);
        }
        
        if (protection.protection_level !== 'MAXIMUM') {
            issues.push("Protection non maximale");
        }
        
        if (totalEntries < 30) {
            recommendations.push(`Enrichir mémoire thermique (${totalEntries} entrées)`);
        }
        
        if (issues.length === 0) {
            console.log('✅ SYSTÈME PARFAITEMENT CONFIGURÉ !');
            console.log('🎉 Tous les composants fonctionnent optimalement');
        } else {
            console.log('⚠️  PROBLÈMES DÉTECTÉS:');
            issues.forEach(issue => console.log(`   ❌ ${issue}`));
        }
        
        if (recommendations.length > 0) {
            console.log('\n💡 RECOMMANDATIONS:');
            recommendations.forEach(rec => console.log(`   🔧 ${rec}`));
        }
        
        // 9. RÉSUMÉ FINAL
        console.log('\n🎯 === RÉSUMÉ FINAL ===');
        console.log(`📊 Zones thermiques: ${Object.keys(zones).length}/6 ✅`);
        console.log(`🏗️ Tour neuronale: ${tower.active ? '✅' : '❌'} (${tower.total_floors} étages)`);
        console.log(`🧠 Système neural: ✅ (${neural.total_neurons.toLocaleString()} neurones)`);
        console.log(`⚡ Accélérateurs: ${activeCount}/${acceleratorList.length} (${totalBoost}x boost)`);
        console.log(`🛡️ Protection: ${protection.protection_level} ✅`);
        console.log(`💾 Entrées mémoire: ${totalEntries}`);
        console.log(`🎯 QI niveau: ${neural.qi_level} (Génie Exceptionnel)`);
        console.log(`📊 Performance: ${(stats.performance_score * 100).toFixed(1)}%`);
        
        return {
            success: true,
            issues: issues,
            recommendations: recommendations,
            stats: {
                zones: Object.keys(zones).length,
                tower_active: tower.active,
                total_neurons: neural.total_neurons,
                active_accelerators: activeCount,
                total_boost: totalBoost,
                qi_level: neural.qi_level,
                performance_score: stats.performance_score
            }
        };
        
    } catch (error) {
        console.error(`❌ Erreur vérification: ${error.message}`);
        return { success: false, error: error.message };
    }
}

// Lancer la vérification
if (require.main === module) {
    verificationSystemeComplet();
}

module.exports = { verificationSystemeComplet };
