#!/usr/bin/env node

/**
 * 🚀 LANCEUR AGENT DEEPSEEK R1 8B AVEC MÉMOIRE THERMIQUE
 * 
 * Lance l'agent DeepSeek R1 8B avec la mémoire thermique
 * de l'agent Python intégrée
 */

const DeepSeekR1IntegratedAgent = require('./deepseek-r1-agent-integrated');
const readline = require('readline');

class DeepSeekAgentLauncher {
    constructor() {
        this.agent = null;
        this.rl = null;
        this.isRunning = false;
    }
    
    async start() {
        console.log('🚀 === LANCEMENT AGENT DEEPSEEK R1 8B INTÉGRÉ ===\n');
        
        try {
            // 1. Créer et initialiser l'agent
            console.log('🔄 Initialisation de l\'agent...');
            this.agent = new DeepSeekR1IntegratedAgent({
                debug: false // Mode production
            });
            
            const initResult = await this.agent.initialize();
            if (!initResult) {
                throw new Error('Échec de l\'initialisation de l\'agent');
            }
            
            // 2. Afficher les informations de l'agent
            this.displayAgentInfo();
            
            // 3. Configurer l'interface de chat
            this.setupChatInterface();
            
            // 4. Démarrer la boucle de conversation
            this.isRunning = true;
            console.log('\n💬 === INTERFACE DE CHAT ACTIVÉE ===');
            console.log('Tapez votre message et appuyez sur Entrée');
            console.log('Commandes spéciales:');
            console.log('  /help    - Afficher l\'aide');
            console.log('  /memory  - Rechercher dans la mémoire');
            console.log('  /stats   - Afficher les statistiques');
            console.log('  /quit    - Quitter l\'agent');
            console.log('─'.repeat(60));
            
            this.promptUser();
            
        } catch (error) {
            console.error(`❌ Erreur de lancement: ${error.message}`);
            process.exit(1);
        }
    }
    
    displayAgentInfo() {
        const stats = this.agent.analyzeMemoryStats();
        
        console.log('\n📊 === INFORMATIONS AGENT ===');
        console.log(`🤖 Nom: ${this.agent.config.name}`);
        console.log(`🔢 Version: ${this.agent.config.version}`);
        console.log(`🧠 Modèle: ${this.agent.config.model}`);
        console.log(`📁 Mémoire: ${stats.totalEntries} entrées (${stats.format})`);
        console.log(`🌡️ Température: ${stats.temperature}°`);
        console.log(`🧮 QI: ${stats.qi}`);
        console.log(`🔗 Connexion: ${this.agent.config.connection.type}`);
        console.log(`⚡ Accélérateurs: ${Object.keys(this.agent.thermalMemoryData.accelerators || {}).length}`);
        console.log(`🛡️ Protection: ${this.agent.thermalMemoryData.protection_system ? 'ACTIVE' : 'INACTIVE'}`);
    }
    
    setupChatInterface() {
        this.rl = readline.createInterface({
            input: process.stdin,
            output: process.stdout,
            prompt: '🤖 DeepSeek R1 8B > '
        });
        
        this.rl.on('line', async (input) => {
            await this.handleUserInput(input.trim());
        });
        
        this.rl.on('close', () => {
            this.shutdown();
        });
    }
    
    async handleUserInput(input) {
        if (!input) {
            this.promptUser();
            return;
        }
        
        try {
            // Commandes spéciales
            if (input.startsWith('/')) {
                await this.handleCommand(input);
                this.promptUser();
                return;
            }
            
            // Traitement normal avec mémoire intégrée
            console.log('\n🧠 Réflexion en cours...');

            const startTime = Date.now();

            // Rechercher dans la mémoire avec seuil plus bas pour trouver plus de résultats
            const memories = this.agent.searchThermalMemory(input, {
                limit: 3,
                minImportance: 0.1  // Seuil plus bas pour capturer plus de souvenirs
            });

            // Effectuer la réflexion avec les mémoires trouvées
            const reflection = await this.agent.performReflection(input, memories, {});

            const processingTime = Date.now() - startTime;
            
            // Afficher la réponse
            console.log('\n💭 === RÉFLEXION ===');
            console.log(`📝 Analyse: ${reflection.input_analysis.type} (complexité: ${reflection.input_analysis.complexity.toFixed(2)})`);
            console.log(`🔍 Mémoires utilisées: ${memories.length}`);
            console.log(`⏱️ Temps de traitement: ${processingTime}ms`);
            
            if (memories.length > 0) {
                console.log('\n🧠 === CONTEXTE MÉMOIRE ===');
                for (let i = 0; i < memories.length; i++) {
                    const memory = memories[i];
                    console.log(`${i + 1}. ${memory.content.substring(0, 120)}...`);
                    console.log(`   📍 Zone: ${memory.zone} | 🎯 Importance: ${memory.importance} | 🔗 Pertinence: ${memory.relevance.toFixed(2)}`);
                }
            } else {
                console.log('\n🧠 === CONTEXTE MÉMOIRE ===');
                console.log('⚠️ Aucune mémoire pertinente trouvée pour cette requête');
            }
            
            console.log('\n🤖 === RÉPONSE ===');
            console.log('Basé sur ma mémoire thermique et ma réflexion:');
            
            // Générer une réponse basée sur la mémoire et la réflexion
            const response = this.generateContextualResponse(input, memories, reflection);
            console.log(response);
            
            // Sauvegarder l'interaction
            await this.saveInteraction(input, response, memories);
            
        } catch (error) {
            console.error(`❌ Erreur: ${error.message}`);
        }
        
        console.log('\n' + '─'.repeat(60));
        this.promptUser();
    }
    
    async handleCommand(command) {
        const [cmd, ...args] = command.split(' ');
        
        switch (cmd) {
            case '/help':
                this.showHelp();
                break;
                
            case '/memory':
                await this.searchMemory(args.join(' '));
                break;
                
            case '/stats':
                this.showStats();
                break;
                
            case '/quit':
                console.log('\n👋 Au revoir !');
                this.shutdown();
                break;
                
            default:
                console.log(`❌ Commande inconnue: ${cmd}`);
                console.log('Tapez /help pour voir les commandes disponibles');
        }
    }
    
    showHelp() {
        console.log('\n📖 === AIDE ===');
        console.log('Commandes disponibles:');
        console.log('  /help              - Afficher cette aide');
        console.log('  /memory <terme>    - Rechercher dans la mémoire thermique');
        console.log('  /stats             - Afficher les statistiques détaillées');
        console.log('  /quit              - Quitter l\'agent');
        console.log('\nPour une conversation normale, tapez simplement votre message.');
    }
    
    async searchMemory(query) {
        if (!query) {
            console.log('❌ Veuillez spécifier un terme de recherche');
            return;
        }
        
        console.log(`\n🔍 Recherche dans la mémoire: "${query}"`);
        const results = this.agent.searchThermalMemory(query, { limit: 5 });
        
        if (results.length === 0) {
            console.log('Aucun résultat trouvé');
            return;
        }
        
        console.log(`\n📋 ${results.length} résultat(s) trouvé(s):`);
        for (let i = 0; i < results.length; i++) {
            const result = results[i];
            console.log(`\n${i + 1}. ${result.id}`);
            console.log(`   Zone: ${result.zone}`);
            console.log(`   Importance: ${result.importance}`);
            console.log(`   Pertinence: ${result.relevance.toFixed(2)}`);
            console.log(`   Contenu: ${result.content}`);
        }
    }
    
    showStats() {
        const stats = this.agent.analyzeMemoryStats();
        const neural = this.agent.thermalMemoryData.neural_system;
        const accelerators = Object.values(this.agent.thermalMemoryData.accelerators || {});
        
        console.log('\n📊 === STATISTIQUES DÉTAILLÉES ===');
        console.log(`🧠 Mémoire Thermique:`);
        console.log(`   - Format: ${stats.format}`);
        console.log(`   - Version: ${stats.version}`);
        console.log(`   - Entrées totales: ${stats.totalEntries}`);
        console.log(`   - Zones: ${stats.zones}`);
        console.log(`   - Température: ${stats.temperature}°`);
        
        if (neural) {
            console.log(`\n🧮 Système Neuronal:`);
            console.log(`   - Neurones: ${neural.total_neurons?.toLocaleString() || 'N/A'}`);
            console.log(`   - QI: ${neural.qi_level}`);
            console.log(`   - Intégration Python: ${neural.python_integration ? '✅' : '❌'}`);
        }
        
        if (accelerators.length > 0) {
            console.log(`\n⚡ Accélérateurs (${accelerators.length}):`);
            for (const acc of accelerators) {
                console.log(`   - ${acc.type}: ${acc.boost_factor}x (${acc.priority})`);
            }
        }
    }
    
    generateContextualResponse(input, memories, reflection) {
        // Réponse basée sur la mémoire et la réflexion
        let response = '';
        
        if (memories.length > 0) {
            response += 'En me basant sur ma mémoire thermique, ';
        }
        
        // Réponse contextuelle simple (en attendant l'intégration API)
        if (input.toLowerCase().includes('mémoire thermique')) {
            response += 'la mémoire thermique est un système sophistiqué de stockage cognitif avec des zones spécialisées et une température adaptative. ';
        } else if (input.toLowerCase().includes('deepseek')) {
            response += 'DeepSeek R1 8B est un modèle de raisonnement avancé maintenant intégré avec ma mémoire thermique. ';
        } else {
            response += 'je traite votre demande en utilisant ma réflexion intégrée et mes souvenirs pertinents. ';
        }
        
        if (reflection.input_analysis.complexity > 0.7) {
            response += 'Cette question nécessite une analyse approfondie de mes connaissances.';
        }
        
        return response;
    }
    
    async saveInteraction(input, response, memories) {
        // Sauvegarder l'interaction dans la mémoire thermique
        const savedInteraction = await this.agent.saveInteractionToMemory(input, response, memories);

        if (savedInteraction) {
            console.log(`💾 Interaction sauvegardée: ${savedInteraction.id}`);
            console.log(`   📊 Importance: ${savedInteraction.importance.toFixed(2)} | 🧠 Mémoires utilisées: ${memories.length}`);
        } else {
            console.log('⚠️ Erreur lors de la sauvegarde de l\'interaction');
        }
    }
    
    promptUser() {
        if (this.isRunning) {
            this.rl.prompt();
        }
    }
    
    shutdown() {
        this.isRunning = false;
        if (this.rl) {
            this.rl.close();
        }
        console.log('\n🛑 Agent arrêté');
        process.exit(0);
    }
}

// Lancement de l'agent
if (require.main === module) {
    const launcher = new DeepSeekAgentLauncher();
    launcher.start().catch(error => {
        console.error(`❌ Erreur fatale: ${error.message}`);
        process.exit(1);
    });
}

module.exports = DeepSeekAgentLauncher;
