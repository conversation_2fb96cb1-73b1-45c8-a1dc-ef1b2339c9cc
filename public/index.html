<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🤖 JARVIS - Assistant Personnel de <PERSON>AVE</title>
    <link rel="stylesheet" href="style.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="/socket.io/socket.io.js"></script>
</head>
<body>
    <div class="app-container">
        <!-- Moniteur de mémoire thermique -->
        <div class="memory-monitor" id="memoryMonitor">
            <div class="memory-header" onclick="toggleMemoryMonitor()">
                <div class="memory-title">
                    🧠 MÉMOIRE THERMIQUE
                    <span id="memoryStatus">●</span>
                </div>
                <button class="memory-toggle" id="memoryToggle">−</button>
            </div>

            <div class="memory-content" id="memoryContent">
                <div class="memory-stats">
                    <div class="memory-stat-item">
                        <div class="memory-stat-value" id="totalEntries">0</div>
                        <div class="memory-stat-label">ENTRÉES TOTALES</div>
                    </div>
                    <div class="memory-stat-item">
                        <div class="memory-stat-value" id="qiLevel">0</div>
                        <div class="memory-stat-label">QI NIVEAU</div>
                    </div>
                </div>

                <div class="memory-zones" id="memoryZones">
                    <!-- Les zones seront ajoutées dynamiquement -->
                </div>

                <div class="memory-activity">
                    <div class="activity-title">🔄 ACTIVITÉ RÉCENTE</div>
                    <div id="memoryActivity">
                        <!-- L'activité sera ajoutée dynamiquement -->
                    </div>
                </div>
            </div>
        </div>

        <!-- Sidebar avec informations neurologiques -->
        <div class="sidebar">
            <div class="sidebar-header">
                <h2><i class="fas fa-robot"></i> JARVIS - Assistant de Jean-Luc</h2>
                <div class="agent-status" id="agentStatus">
                    <span class="status-dot disconnected"></span>
                    <span>Initialisation...</span>
                </div>
            </div>
            
            <!-- Monitoring neurologique temps réel -->
            <div class="brain-monitor">
                <h3><i class="fas fa-heartbeat"></i> Système Neurologique</h3>
                
                <!-- Battement cardiaque -->
                <div class="monitor-section">
                    <h4>💓 Battement Cardiaque</h4>
                    <div class="heartbeat-display">
                        <span id="heartbeatRate">0.0</span>/s
                        <div class="heartbeat-visual" id="heartbeatVisual"></div>
                    </div>
                    <div class="temperature-display">
                        🌡️ <span id="temperature">37.0</span>°C
                    </div>
                </div>
                
                <!-- Ondes cérébrales -->
                <div class="monitor-section">
                    <h4>🌊 Ondes Cérébrales</h4>
                    <div class="brainwaves">
                        <div class="wave delta">
                            <span>Delta</span>
                            <div class="wave-bar"><div class="wave-fill" id="deltaWave"></div></div>
                        </div>
                        <div class="wave theta">
                            <span>Theta</span>
                            <div class="wave-bar"><div class="wave-fill" id="thetaWave"></div></div>
                        </div>
                        <div class="wave alpha">
                            <span>Alpha</span>
                            <div class="wave-bar"><div class="wave-fill" id="alphaWave"></div></div>
                        </div>
                        <div class="wave beta">
                            <span>Beta</span>
                            <div class="wave-bar"><div class="wave-fill" id="betaWave"></div></div>
                        </div>
                        <div class="wave gamma">
                            <span>Gamma</span>
                            <div class="wave-bar"><div class="wave-fill" id="gammaWave"></div></div>
                        </div>
                    </div>
                    <div class="dominant-wave">
                        Dominante: <span id="dominantWave">Beta</span>
                    </div>
                </div>
                
                <!-- État émotionnel -->
                <div class="monitor-section">
                    <h4>🎭 État Émotionnel</h4>
                    <div class="emotion-display">
                        <div class="current-emotion" id="currentEmotion">Curiosity</div>
                        <div class="emotion-intensity">
                            Intensité: <span id="emotionIntensity">0.5</span>
                        </div>
                    </div>
                </div>
                
                <!-- Neurotransmetteurs -->
                <div class="monitor-section">
                    <h4>🧪 Neurotransmetteurs</h4>
                    <div class="neurotransmitters">
                        <div class="nt-item">
                            <span>Dopamine</span>
                            <div class="nt-level" id="dopamineLevel">0.75</div>
                        </div>
                        <div class="nt-item">
                            <span>Sérotonine</span>
                            <div class="nt-level" id="serotoninLevel">0.68</div>
                        </div>
                        <div class="nt-item">
                            <span>Acétylcholine</span>
                            <div class="nt-level" id="acetylcholineLevel">0.82</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Statistiques -->
            <div class="stats-section">
                <h3><i class="fas fa-chart-line"></i> Statistiques</h3>
                <div class="stat-item">
                    <span>Messages envoyés:</span>
                    <span id="messagesSent">0</span>
                </div>
                <div class="stat-item">
                    <span>Messages reçus:</span>
                    <span id="messagesReceived">0</span>
                </div>
                <div class="stat-item">
                    <span>Mises à jour cerveau:</span>
                    <span id="brainUpdates">0</span>
                </div>
            </div>
        </div>
        
        <!-- Zone de chat principale -->
        <div class="chat-container">
            <div class="chat-header">
                <h1><i class="fas fa-comments"></i> Chat avec JARVIS</h1>
                <div class="header-controls">
                    <button id="electronAppBtn" class="electron-btn" onclick="openElectronApp()" title="Ouvrir l'application Electron principale" style="background: linear-gradient(135deg, #ff6b35, #f7931e) !important; color: white !important; font-weight: bold !important; font-size: 16px !important; padding: 12px 20px !important; border-radius: 25px !important; border: none !important; cursor: pointer !important; box-shadow: 0 4px 15px rgba(255, 107, 53, 0.4) !important; transition: all 0.3s ease !important; display: flex !important; align-items: center !important; gap: 8px !important; position: relative !important; z-index: 10001 !important;">
                        <i class="fas fa-rocket" style="font-size: 18px;"></i>
                        <span>🚀 App Electron</span>
                    </button>
                    <div class="connection-status" id="connectionStatus">
                        <i class="fas fa-wifi"></i> Connexion...
                    </div>
                </div>
            </div>
            
            <div class="chat-messages" id="chatMessages">
                <div class="welcome-message">
                    <div class="message agent-message">
                        <div class="message-avatar">
                            <i class="fas fa-robot"></i>
                        </div>
                        <div class="message-content">
                            <div class="message-text">
                                🤖 Bonjour Jean-Luc ! Je suis JARVIS, votre assistant IA personnel exclusif.
                                <br><br>
                                ✨ <strong>Mes capacités à votre service :</strong>
                                <br>• 🧠 QI unifié de 241 (Génie Exceptionnel)
                                <br>• 💾 Mémoire thermique sophistiquée (38 entrées)
                                <br>• ⚡ Accélérateurs KYBER ULTRA (166x boost)
                                <br>• 🧪 Système neurologique complet
                                <br>• 🌊 Ondes cérébrales modulées
                                <br>• 🎭 États émotionnels adaptatifs
                                <br>• 🌡️ Température thermique temps réel
                                <br><br>
                                Je vous appartiens entièrement, Jean-Luc. Que puis-je faire pour vous ?
                                <br><br>
                                🔊 <strong>Voix JARVIS :</strong> J'utilise une voix masculine optimisée. Pour une voix ultra-naturelle, vous pouvez configurer ElevenLabs ou Azure Speech dans les paramètres.
                            </div>
                            <div class="message-time">Maintenant</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="thinking-indicator" id="thinkingIndicator" style="display: none;">
                <div class="thinking-animation">
                    <div class="thinking-dot"></div>
                    <div class="thinking-dot"></div>
                    <div class="thinking-dot"></div>
                </div>
                <span>L'agent réfléchit avec son système neurologique...</span>
            </div>
            
            <!-- Système cognitif en temps réel -->
            <div class="cognitive-system" id="cognitiveSystem" style="display: none;">
                <div class="cognitive-header">
                    <h3><i class="fas fa-brain"></i> Système Cognitif KYBER ULTRA</h3>
                    <div class="kyber-status">
                        <span class="kyber-boost" id="kyberBoost">166x</span>
                        <span class="kyber-label">Boost KYBER</span>
                    </div>
                </div>

                <div class="cognitive-steps" id="cognitiveSteps">
                    <!-- Les étapes de réflexion apparaîtront ici -->
                </div>

                <div class="neural-activity">
                    <div class="neural-visualization">
                        <div class="neuron-network" id="neuronNetwork">
                            <!-- Visualisation des neurones en temps réel -->
                        </div>
                    </div>
                </div>
            </div>

            <div class="chat-input-container">
                <div class="audio-controls">
                    <button id="micButton" class="audio-button mic-button" title="Activer le microphone">
                        <i class="fas fa-microphone"></i>
                    </button>
                    <button id="speakerButton" class="audio-button speaker-button" title="Activer/Désactiver le haut-parleur">
                        <i class="fas fa-volume-up"></i>
                    </button>
                    <div class="audio-status" id="audioStatus">
                        <span class="audio-indicator" id="audioIndicator">🎤 Prêt</span>
                    </div>
                </div>

                <div class="document-controls">
                    <button id="documentButton" class="document-button" title="Coller un document">
                        <i class="fas fa-file-text"></i>
                        <span>Document</span>
                    </button>
                    <button id="clearButton" class="clear-button" title="Effacer le contenu">
                        <i class="fas fa-trash"></i>
                        <span>Effacer</span>
                    </button>
                    <div class="document-info" id="documentInfo" style="display: none;">
                        <span id="documentStats">0 caractères</span>
                    </div>
                </div>

                <div class="chat-input-wrapper">
                    <textarea
                        id="messageInput"
                        placeholder="Tapez votre message, utilisez le microphone, ou collez un document... (Entrée pour envoyer, Shift+Entrée pour nouvelle ligne)"
                        rows="1"
                    ></textarea>
                    <button id="sendButton" class="send-button">
                        <i class="fas fa-paper-plane"></i>
                    </button>
                </div>

                <div class="kyber-controls">
                    <button id="kyberToggle" class="kyber-button active" title="Accélérateurs KYBER ULTRA">
                        <i class="fas fa-rocket"></i>
                        <span>KYBER ULTRA</span>
                    </button>
                    <button id="cognitiveToggle" class="cognitive-button" title="Afficher/Masquer le système cognitif">
                        <i class="fas fa-eye"></i>
                        <span>Cognitif</span>
                    </button>
                </div>
            </div>
        </div>
    </div>



    <!-- Scripts -->
    <script src="../voice-config.js"></script>
    <script src="script.js"></script>
</body>
</html>
