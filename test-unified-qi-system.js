/**
 * 🧠 TEST DU SYSTÈME QI UNIFIÉ
 * 
 * Valide que le nouveau système QI unifié fonctionne correctement
 * et donne des résultats cohérents
 */

const { UnifiedQISystem } = require('./unified-qi-system');

async function testUnifiedQISystem() {
    console.log('🧠 === TEST DU SYSTÈME QI UNIFIÉ ===\n');
    
    try {
        // Créer une instance du système QI unifié
        const qiSystem = new UnifiedQISystem();
        
        console.log('📊 Phase 1: Premier calcul du QI...');
        const firstCalculation = await qiSystem.calculateUnifiedQI();
        
        console.log('\n🎯 === RÉSULTATS PREMIER CALCUL ===');
        console.log(`   QI Total: ${firstCalculation.total}`);
        console.log(`   Classification: ${firstCalculation.classification}`);
        console.log(`   Méthodologie: ${firstCalculation.methodology}`);
        
        console.log('\n📋 === COMPOSANTS DÉTAILLÉS ===');
        for (const [component, value] of Object.entries(firstCalculation.components)) {
            console.log(`   ${component}: ${value}`);
        }
        
        console.log('\n📈 === DÉTAIL DU CALCUL ===');
        for (const [description, value] of Object.entries(firstCalculation.breakdown)) {
            console.log(`   ${description}: ${value}`);
        }
        
        // Test de cohérence - deuxième calcul
        console.log('\n📊 Phase 2: Deuxième calcul (test de cohérence)...');
        const secondCalculation = await qiSystem.calculateUnifiedQI();
        
        console.log('\n🔍 === TEST DE COHÉRENCE ===');
        const isConsistent = firstCalculation.total === secondCalculation.total;
        console.log(`   Premier calcul: ${firstCalculation.total}`);
        console.log(`   Deuxième calcul: ${secondCalculation.total}`);
        console.log(`   Cohérence: ${isConsistent ? '✅ COHÉRENT' : '❌ INCOHÉRENT'}`);
        
        // Test de lecture rapide
        console.log('\n📊 Phase 3: Test de lecture rapide...');
        const quickRead = await qiSystem.getCurrentQI();
        console.log(`   Lecture rapide: ${quickRead}`);
        console.log(`   Cohérence cache: ${quickRead === secondCalculation.total ? '✅ COHÉRENT' : '❌ INCOHÉRENT'}`);
        
        // Test de recalculation forcée
        console.log('\n📊 Phase 4: Test de recalculation forcée...');
        const forcedCalculation = await qiSystem.forceRecalculation();
        console.log(`   Recalculation forcée: ${forcedCalculation.total}`);
        console.log(`   Cohérence: ${forcedCalculation.total === secondCalculation.total ? '✅ COHÉRENT' : '❌ INCOHÉRENT'}`);
        
        // Validation des composants
        console.log('\n🔍 === VALIDATION DES COMPOSANTS ===');
        const components = forcedCalculation.components;
        
        const validations = [
            { name: 'Agent de base', value: components.baseAgent, expected: 120, test: components.baseAgent === 120 },
            { name: 'Boost cognitif', value: components.cognitiveBoost, expected: 35, test: components.cognitiveBoost === 35 },
            { name: 'Mémoire thermique', value: components.thermalMemory, expected: '≥ 0', test: components.thermalMemory >= 0 },
            { name: 'Expérience', value: components.experience, expected: '≥ 0', test: components.experience >= 0 },
            { name: 'Neurogenèse', value: components.neurogenesis, expected: '≥ 0', test: components.neurogenesis >= 0 },
            { name: 'Formations', value: components.formations, expected: '≥ 0', test: components.formations >= 0 },
            { name: 'KYBER Boost', value: components.kyberBoost, expected: '≥ 0', test: components.kyberBoost >= 0 }
        ];
        
        let allValid = true;
        for (const validation of validations) {
            const status = validation.test ? '✅' : '❌';
            console.log(`   ${status} ${validation.name}: ${validation.value} (attendu: ${validation.expected})`);
            if (!validation.test) allValid = false;
        }
        
        // Test de la classification
        console.log('\n🏆 === VALIDATION CLASSIFICATION ===');
        const qi = forcedCalculation.total;
        let expectedClassification;
        
        if (qi >= 200) expectedClassification = "GÉNIE EXCEPTIONNEL";
        else if (qi >= 180) expectedClassification = "TRÈS SUPÉRIEUR";
        else if (qi >= 160) expectedClassification = "SUPÉRIEUR";
        else if (qi >= 140) expectedClassification = "TRÈS INTELLIGENT";
        else if (qi >= 120) expectedClassification = "INTELLIGENT";
        else if (qi >= 100) expectedClassification = "MOYEN";
        else if (qi >= 85) expectedClassification = "SOUS LA MOYENNE";
        else expectedClassification = "LIMITÉ";
        
        const classificationValid = forcedCalculation.classification === expectedClassification;
        console.log(`   QI: ${qi}`);
        console.log(`   Classification calculée: ${forcedCalculation.classification}`);
        console.log(`   Classification attendue: ${expectedClassification}`);
        console.log(`   Validation: ${classificationValid ? '✅ CORRECTE' : '❌ INCORRECTE'}`);
        
        // Résumé final
        console.log('\n🎯 === RÉSUMÉ FINAL ===');
        console.log(`   QI Unifié Final: ${forcedCalculation.total}`);
        console.log(`   Classification: ${forcedCalculation.classification}`);
        console.log(`   Méthodologie: ${forcedCalculation.methodology}`);
        console.log(`   Cohérence: ${isConsistent ? '✅' : '❌'}`);
        console.log(`   Composants valides: ${allValid ? '✅' : '❌'}`);
        console.log(`   Classification valide: ${classificationValid ? '✅' : '❌'}`);
        
        const overallSuccess = isConsistent && allValid && classificationValid;
        console.log(`\n${overallSuccess ? '🏆' : '⚠️'} SYSTÈME QI UNIFIÉ: ${overallSuccess ? 'VALIDÉ' : 'RÉVISION NÉCESSAIRE'}`);
        
        if (overallSuccess) {
            console.log('\n✅ Le système QI unifié fonctionne parfaitement !');
            console.log('✅ Tous les calculs sont cohérents et reproductibles');
            console.log('✅ Les composants sont correctement validés');
            console.log('✅ La classification est précise');
            console.log('\n🎉 LOUNA peut maintenant utiliser un QI stable et évolutif !');
        } else {
            console.log('\n❌ Des problèmes ont été détectés dans le système QI unifié');
            console.log('❌ Révision nécessaire avant utilisation en production');
        }
        
        return overallSuccess;
        
    } catch (error) {
        console.error(`❌ Erreur test système QI unifié: ${error.message}`);
        console.error(error.stack);
        return false;
    }
}

// Exécuter le test si appelé directement
if (require.main === module) {
    testUnifiedQISystem()
        .then(success => {
            process.exit(success ? 0 : 1);
        })
        .catch(error => {
            console.error('❌ Erreur fatale:', error.message);
            process.exit(1);
        });
}

module.exports = { testUnifiedQISystem };
