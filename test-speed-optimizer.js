#!/usr/bin/env node

/**
 * 🚀 TEST SPÉCIFIQUE DE L'OPTIMISEUR DE VITESSE
 * 
 * Test détaillé de l'agent-speed-optimizer.js pour vérifier
 * toutes ses fonctionnalités
 */

const AgentSpeedOptimizer = require('./agent-speed-optimizer.js');

class TestSpeedOptimizer {
    constructor() {
        this.optimizer = null;
        this.testResults = [];
    }

    async runTests() {
        console.log('🚀 === TEST OPTIMISEUR DE VITESSE AGENT LOUNA ===\n');

        try {
            // 1. Test de création
            await this.testCreation();
            
            // 2. Test de configuration
            await this.testConfiguration();
            
            // 3. Test de cache
            await this.testCache();
            
            // 4. Test de traitement de messages
            await this.testMessageProcessing();
            
            // 5. Test de performance
            await this.testPerformance();
            
            // 6. Test du mode turbo
            await this.testTurboMode();
            
            // Rapport final
            this.generateReport();
            
        } catch (error) {
            console.error(`❌ Erreur fatale: ${error.message}`);
        }
    }

    async testCreation() {
        console.log('🔍 Test 1: Création de l\'optimiseur');
        
        try {
            this.optimizer = new AgentSpeedOptimizer();
            
            if (this.optimizer && this.optimizer.config) {
                console.log('  ✅ Optimiseur créé avec succès');
                console.log(`  📊 Vitesse cible: ${this.optimizer.config.speed.targetLatency}ms`);
                console.log(`  🧠 Réflexion: ${this.optimizer.config.reflection.enabled ? 'Activée' : 'Désactivée'}`);
                console.log(`  💾 Cache: ${this.optimizer.config.cache.enabled ? 'Activé' : 'Désactivé'}`);
                this.recordTest('Création', true);
            } else {
                throw new Error('Optimiseur non créé correctement');
            }
        } catch (error) {
            console.log(`  ❌ Échec: ${error.message}`);
            this.recordTest('Création', false);
        }
        
        console.log('');
    }

    async testConfiguration() {
        console.log('🔍 Test 2: Configuration');
        
        try {
            const config = this.optimizer.config;
            
            // Vérifier les paramètres essentiels
            const checks = [
                { name: 'Vitesse cible', value: config.speed.targetLatency, expected: 'number' },
                { name: 'Connexions directes', value: config.connections.directAPI, expected: true },
                { name: 'Cache activé', value: config.cache.enabled, expected: true },
                { name: 'Réflexion activée', value: config.reflection.enabled, expected: true }
            ];
            
            let allValid = true;
            for (const check of checks) {
                if (typeof check.value !== typeof check.expected || check.value !== check.expected) {
                    if (typeof check.expected === 'string' && typeof check.value !== check.expected) {
                        allValid = false;
                        console.log(`  ❌ ${check.name}: type incorrect`);
                    }
                }
            }
            
            if (allValid) {
                console.log('  ✅ Configuration valide');
                console.log(`  ⚡ Mode connexion: ${config.connections.directAPI ? 'Direct' : 'Ollama'}`);
                console.log(`  🎯 Latence max: ${config.speed.maxLatency}ms`);
                this.recordTest('Configuration', true);
            } else {
                throw new Error('Configuration invalide');
            }
            
        } catch (error) {
            console.log(`  ❌ Échec: ${error.message}`);
            this.recordTest('Configuration', false);
        }
        
        console.log('');
    }

    async testCache() {
        console.log('🔍 Test 3: Système de cache');
        
        try {
            // Test de mise en cache
            const testMessage = 'Bonjour test cache';
            const testResponse = 'Réponse test cache';
            
            this.optimizer.cacheResponse(testMessage, testResponse, 'quick');
            
            // Test de récupération
            const cachedResponse = this.optimizer.getCachedResponse(testMessage);
            
            if (cachedResponse && cachedResponse.response === testResponse) {
                console.log('  ✅ Cache fonctionne correctement');
                console.log(`  💾 Réponse mise en cache et récupérée`);
                console.log(`  📊 Taille cache instant: ${this.optimizer.cache.instant.size}`);
                console.log(`  📊 Taille cache rapide: ${this.optimizer.cache.quick.size}`);
                this.recordTest('Cache', true);
            } else {
                throw new Error('Cache ne fonctionne pas');
            }
            
        } catch (error) {
            console.log(`  ❌ Échec: ${error.message}`);
            this.recordTest('Cache', false);
        }
        
        console.log('');
    }

    async testMessageProcessing() {
        console.log('🔍 Test 4: Traitement de messages');
        
        const testMessages = [
            'Bonjour',
            'Comment ça va ?',
            'Quelle est la capitale de la France ?',
            'Peux-tu m\'aider avec un problème technique ?'
        ];
        
        let successCount = 0;
        
        for (const message of testMessages) {
            try {
                console.log(`  🔄 Test message: "${message}"`);
                const startTime = Date.now();
                
                const result = await this.optimizer.processMessage(message);
                
                const duration = Date.now() - startTime;
                
                if (result && result.success && result.response) {
                    console.log(`    ✅ Réponse générée en ${duration}ms`);
                    console.log(`    📝 Source: ${result.source}`);
                    successCount++;
                } else {
                    console.log(`    ❌ Échec traitement`);
                }
                
            } catch (error) {
                console.log(`    ❌ Erreur: ${error.message}`);
            }
        }
        
        if (successCount === testMessages.length) {
            console.log(`  ✅ Tous les messages traités (${successCount}/${testMessages.length})`);
            this.recordTest('Traitement messages', true);
        } else {
            console.log(`  ⚠️  Messages partiellement traités (${successCount}/${testMessages.length})`);
            this.recordTest('Traitement messages', successCount > testMessages.length / 2);
        }
        
        console.log('');
    }

    async testPerformance() {
        console.log('🔍 Test 5: Performance');
        
        try {
            const stats = this.optimizer.getPerformanceStats();
            
            console.log('  📊 Statistiques de performance:');
            console.log(`    ⏱️  Latence moyenne: ${stats.averageLatency}ms`);
            console.log(`    🚀 Réponse la plus rapide: ${stats.fastestResponse}ms`);
            console.log(`    🐌 Réponse la plus lente: ${stats.slowestResponse}ms`);
            console.log(`    💾 Taux de cache hit: ${stats.cacheHitRate.toFixed(1)}%`);
            console.log(`    📈 Gains d'optimisation: ${stats.optimizationGains.toFixed(1)}%`);
            console.log(`    🎯 Niveau d'optimisation: ${stats.optimizationLevel}`);
            
            // Vérifier les performances
            const performanceChecks = [
                stats.averageLatency < 2000, // Moins de 2s
                stats.optimizationGains > 0, // Gains positifs
                stats.optimizationLevel !== 'slow' // Pas lent
            ];
            
            const performanceScore = performanceChecks.filter(check => check).length;
            
            if (performanceScore >= 2) {
                console.log('  ✅ Performance acceptable');
                this.recordTest('Performance', true);
            } else {
                console.log('  ⚠️  Performance à améliorer');
                this.recordTest('Performance', false);
            }
            
        } catch (error) {
            console.log(`  ❌ Échec: ${error.message}`);
            this.recordTest('Performance', false);
        }
        
        console.log('');
    }

    async testTurboMode() {
        console.log('🔍 Test 6: Mode Turbo');
        
        try {
            // Sauvegarder les stats avant
            const statsBefore = this.optimizer.getPerformanceStats();
            
            // Activer le mode turbo
            this.optimizer.enableTurboMode();
            
            // Vérifier les changements
            const statsAfter = this.optimizer.getPerformanceStats();
            
            console.log('  🚀 Mode Turbo activé');
            console.log(`    ⏱️  Latence cible: ${this.optimizer.config.speed.targetLatency}ms`);
            console.log(`    🧠 Temps réflexion max: ${this.optimizer.config.reflection.maxThinkingTime}ms`);
            console.log(`    📈 Gains optimisation: ${statsAfter.optimizationGains.toFixed(1)}%`);
            
            // Test de performance en mode turbo
            const testStart = Date.now();
            const turboResult = await this.optimizer.processMessage('Test mode turbo');
            const turboDuration = Date.now() - testStart;
            
            if (turboResult && turboResult.success && turboDuration < 1000) {
                console.log(`  ✅ Mode Turbo fonctionnel (${turboDuration}ms)`);
                this.recordTest('Mode Turbo', true);
            } else {
                console.log(`  ⚠️  Mode Turbo partiellement fonctionnel`);
                this.recordTest('Mode Turbo', false);
            }
            
        } catch (error) {
            console.log(`  ❌ Échec: ${error.message}`);
            this.recordTest('Mode Turbo', false);
        }
        
        console.log('');
    }

    recordTest(name, passed) {
        this.testResults.push({ name, passed, timestamp: Date.now() });
    }

    generateReport() {
        const passed = this.testResults.filter(test => test.passed).length;
        const total = this.testResults.length;
        const successRate = (passed / total * 100).toFixed(1);
        
        console.log('📊 === RAPPORT FINAL OPTIMISEUR DE VITESSE ===\n');
        console.log(`🎯 Tests réussis: ${passed}/${total} (${successRate}%)`);
        
        if (this.optimizer) {
            const finalStats = this.optimizer.getPerformanceStats();
            console.log('\n📈 Statistiques finales:');
            console.log(`  ⏱️  Latence moyenne: ${finalStats.averageLatency}ms`);
            console.log(`  🚀 Niveau optimisation: ${finalStats.optimizationLevel}`);
            console.log(`  💾 Cache hit rate: ${finalStats.cacheHitRate.toFixed(1)}%`);
            console.log(`  📊 Gains optimisation: ${finalStats.optimizationGains.toFixed(1)}%`);
        }
        
        console.log('\n🔍 Détail des tests:');
        this.testResults.forEach(test => {
            const status = test.passed ? '✅' : '❌';
            console.log(`  ${status} ${test.name}`);
        });
        
        if (successRate >= 80) {
            console.log('\n🎉 VERDICT: OPTIMISEUR DE VITESSE FONCTIONNEL ✅');
        } else if (successRate >= 60) {
            console.log('\n⚠️  VERDICT: OPTIMISEUR PARTIELLEMENT FONCTIONNEL');
        } else {
            console.log('\n❌ VERDICT: OPTIMISEUR NON FONCTIONNEL');
        }
    }
}

// Lancement du test
if (require.main === module) {
    const tester = new TestSpeedOptimizer();
    tester.runTests();
}

module.exports = TestSpeedOptimizer;
