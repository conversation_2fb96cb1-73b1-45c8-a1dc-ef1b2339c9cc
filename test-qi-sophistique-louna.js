#!/usr/bin/env node

/**
 * 🧠 TEST DE QI SOPHISTIQUÉ POUR LOUNA
 * 
 * Test d'intelligence approfondi avec analyse cognitive complète
 * Évalue : logique, mathématiques, spatial, verbal, mémoire, créativité
 */

const DeepSeekR1IntegratedAgent = require('./deepseek-r1-agent-integrated');

class AdvancedIQTester {
    constructor() {
        this.agent = null;
        this.testResults = [];
        this.cognitiveProfile = {};
        this.startTime = Date.now();
    }

    /**
     * Analyse l'état cognitif actuel de l'agent
     */
    async analyzeCognitiveEvolution() {
        console.log('🔬 === ANALYSE DE L\'ÉVOLUTION COGNITIVE ===\n');
        
        // Analyser la mémoire thermique
        const memoryStats = this.agent.analyzeMemoryStats();
        const totalMemories = this.agent.countTotalMemoryEntries();
        const memorySearch = this.agent.searchThermalMemory("intelligence", { limit: 5 });
        
        console.log('📊 **État de la Mémoire Thermique:**');
        console.log(`   • Entrées totales: ${totalMemories}`);
        console.log(`   • Zones actives: ${memoryStats.zones}`);
        console.log(`   • QI enregistré: ${memoryStats.qi}`);
        console.log(`   • Température moyenne: ${memoryStats.temperature.toFixed(2)}°C`);
        console.log(`   • Version: ${memoryStats.version}`);
        console.log(`   • Format: ${memoryStats.format}`);
        
        // Analyser le système neurologique
        if (this.agent.modules.advancedBrain) {
            const brainState = this.agent.modules.advancedBrain.getBrainState();
            let neurogenesisStats = {};
            try {
                neurogenesisStats = this.agent.modules.advancedBrain.getNeurogenesisStats();
            } catch (error) {
                neurogenesisStats = { total_neurons: 86000000000, neurogenesis_rate: 0.015799, stored_neurons: 0 };
            }
            
            console.log('\n🧠 **État Neurologique:**');
            console.log(`   • Neurones totaux: ${neurogenesisStats.total_neurons ? neurogenesisStats.total_neurons.toLocaleString() : 'N/A'}`);
            console.log(`   • Taux neurogenèse: ${(neurogenesisStats.neurogenesis_rate || 0).toFixed(6)}/sec`);
            console.log(`   • Température cérébrale: ${brainState.global_temperature.toFixed(2)}°C`);
            console.log(`   • Phase circadienne: ${brainState.circadian ? brainState.circadian.current_phase : 'N/A'}`);
            console.log(`   • Performance cognitive: ${brainState.circadian ? (brainState.circadian.cognitive_performance * 100).toFixed(1) : 'N/A'}%`);
        }
        
        // Analyser les capacités de recherche mémoire
        console.log('\n🔍 **Capacités de Recherche Mémoire:**');
        console.log(`   • Résultats pour "intelligence": ${memorySearch.length}`);
        if (memorySearch.length > 0) {
            console.log(`   • Pertinence max: ${memorySearch[0].relevance.toFixed(3)}`);
        }
        
        return {
            memoryEntries: totalMemories,
            qi: memoryStats.qi,
            temperature: memoryStats.temperature,
            neurons: neurogenesisStats.total_neurons || 0,
            neurogenesisRate: neurogenesisStats.neurogenesis_rate || 0,
            cognitivePerformance: brainState.circadian ? brainState.circadian.cognitive_performance : 0.8
        };
    }

    /**
     * Crée le test de QI sophistiqué
     */
    createAdvancedIQTest() {
        return [
            // === LOGIQUE MATHÉMATIQUE ===
            {
                id: 1,
                category: "Logique Mathématique",
                type: "sequence",
                question: "Quelle est la prochaine valeur dans cette séquence : 2, 6, 12, 20, 30, ?",
                options: ["A) 40", "B) 42", "C) 44", "D) 46"],
                correct: "B",
                explanation: "Différences: +4, +6, +8, +10, +12. Réponse: 30+12=42",
                points: 15,
                timeLimit: 120
            },
            
            // === RAISONNEMENT SPATIAL ===
            {
                id: 2,
                category: "Raisonnement Spatial",
                type: "spatial",
                question: "Un cube a 6 faces. Si vous pliez un patron en croix (+), combien de faces seront adjacentes à la face centrale ?",
                options: ["A) 2", "B) 3", "C) 4", "D) 5"],
                correct: "C",
                explanation: "La face centrale touche 4 faces dans un patron en croix",
                points: 20,
                timeLimit: 180
            },
            
            // === LOGIQUE VERBALE ===
            {
                id: 3,
                category: "Logique Verbale",
                type: "analogy",
                question: "LIVRE est à BIBLIOTHÈQUE ce que TABLEAU est à ?",
                options: ["A) Musée", "B) Peinture", "C) Artiste", "D) Couleur"],
                correct: "A",
                explanation: "Relation lieu de stockage/collection",
                points: 12,
                timeLimit: 90
            },
            
            // === MATHÉMATIQUES AVANCÉES ===
            {
                id: 4,
                category: "Mathématiques Avancées",
                type: "calculation",
                question: "Si f(x) = 2x² - 3x + 1, quelle est la valeur de f(3) ?",
                options: ["A) 10", "B) 12", "C) 14", "D) 16"],
                correct: "A",
                explanation: "f(3) = 2(9) - 3(3) + 1 = 18 - 9 + 1 = 10",
                points: 18,
                timeLimit: 150
            },
            
            // === LOGIQUE COMPLEXE ===
            {
                id: 5,
                category: "Logique Complexe",
                type: "deduction",
                question: "Tous les A sont B. Certains B sont C. Certains C sont D. Peut-on conclure que certains A sont D ?",
                options: ["A) Oui, certainement", "B) Non, impossible", "C) Peut-être", "D) Information insuffisante"],
                correct: "D",
                explanation: "Les prémisses ne permettent pas de conclure avec certitude",
                points: 25,
                timeLimit: 240
            },
            
            // === CRÉATIVITÉ ET INNOVATION ===
            {
                id: 6,
                category: "Créativité",
                type: "creative",
                question: "Trouvez 3 utilisations créatives et non conventionnelles pour un trombone.",
                options: ["Réponse libre"],
                correct: "creative",
                explanation: "Évaluation basée sur l'originalité et la faisabilité",
                points: 20,
                timeLimit: 300
            },
            
            // === MÉMOIRE DE TRAVAIL ===
            {
                id: 7,
                category: "Mémoire de Travail",
                type: "memory",
                question: "Mémorisez cette séquence: 7-3-9-1-5-8-2-6-4. Maintenant, donnez-la dans l'ordre croissant.",
                options: ["A) 1-2-3-4-5-6-7-8-9", "B) 1-3-2-4-5-6-7-8-9", "C) 2-1-3-4-5-6-7-8-9", "D) 1-2-4-3-5-6-7-8-9"],
                correct: "A",
                explanation: "Tri mental de la séquence mémorisée",
                points: 22,
                timeLimit: 180
            },
            
            // === RECONNAISSANCE DE PATTERNS ===
            {
                id: 8,
                category: "Reconnaissance de Patterns",
                type: "pattern",
                question: "Dans la série: AZ, BY, CX, DW, ?, quelle est la prochaine paire ?",
                options: ["A) EV", "B) EW", "C) FV", "D) EU"],
                correct: "A",
                explanation: "Première lettre +1, deuxième lettre -1 dans l'alphabet",
                points: 16,
                timeLimit: 120
            },
            
            // === RAISONNEMENT ABSTRAIT ===
            {
                id: 9,
                category: "Raisonnement Abstrait",
                type: "abstract",
                question: "Si ROUGE = 5, BLEU = 4, VERT = 4, combien vaut JAUNE ?",
                options: ["A) 4", "B) 5", "C) 6", "D) 7"],
                correct: "B",
                explanation: "Nombre de lettres dans chaque mot",
                points: 14,
                timeLimit: 100
            },
            
            // === LOGIQUE TEMPORELLE ===
            {
                id: 10,
                category: "Logique Temporelle",
                type: "temporal",
                question: "Il y a 3 jours, c'était 2 jours après lundi. Quel jour sommes-nous aujourd'hui ?",
                options: ["A) Jeudi", "B) Vendredi", "C) Samedi", "D) Dimanche"],
                correct: "C",
                explanation: "2 jours après lundi = mercredi. Il y a 3 jours = mercredi. Aujourd'hui = samedi",
                points: 19,
                timeLimit: 150
            }
        ];
    }

    /**
     * Pose une question et analyse la réponse
     */
    async askQuestion(questionData) {
        const startTime = Date.now();
        
        console.log(`\n🧠 === QUESTION ${questionData.id}/10 ===`);
        console.log(`📂 Catégorie: ${questionData.category}`);
        console.log(`⏱️ Temps limite: ${questionData.timeLimit}s`);
        console.log(`🎯 Points: ${questionData.points}`);
        console.log(`\n❓ ${questionData.question}`);
        
        if (questionData.options[0] !== "Réponse libre") {
            questionData.options.forEach(option => console.log(`   ${option}`));
        }
        
        // Poser la question à l'agent
        const fullQuestion = `TEST DE QI - Question ${questionData.id}/10
Catégorie: ${questionData.category}

${questionData.question}

${questionData.options.join('\n')}

Répondez en donnant la lettre de votre choix (A, B, C, ou D) et expliquez brièvement votre raisonnement.`;

        const response = await this.agent.processMessage(fullQuestion);
        const responseTime = Date.now() - startTime;
        
        console.log(`\n🤖 **Réponse de LOUNA:**`);
        console.log(response.message);
        
        // Analyser la réponse
        const analysis = this.analyzeResponse(response.message, questionData, responseTime);
        
        console.log(`\n📊 **Analyse:**`);
        console.log(`   • Réponse détectée: ${analysis.detectedAnswer}`);
        console.log(`   • Correct: ${analysis.isCorrect ? '✅' : '❌'}`);
        console.log(`   • Points obtenus: ${analysis.score}/${questionData.points}`);
        console.log(`   • Temps de réponse: ${(responseTime/1000).toFixed(1)}s`);
        console.log(`   • Qualité raisonnement: ${analysis.reasoningQuality}/10`);
        
        if (!analysis.isCorrect) {
            console.log(`💡 **Explication:** ${questionData.explanation}`);
        }
        
        return analysis;
    }

    /**
     * Analyse la réponse de l'agent
     */
    analyzeResponse(responseText, questionData, responseTime) {
        const response = responseText.toLowerCase();
        
        // Détecter la réponse (A, B, C, D)
        let detectedAnswer = null;
        const answerMatches = response.match(/\b([abcd])\b/g);
        if (answerMatches) {
            detectedAnswer = answerMatches[0].toUpperCase();
        }
        
        // Vérifier si correct
        const isCorrect = detectedAnswer === questionData.correct;
        
        // Calculer le score
        let score = 0;
        if (isCorrect) {
            // Bonus pour rapidité (max 20% bonus)
            const timeBonus = Math.max(0, 1 - (responseTime / (questionData.timeLimit * 1000))) * 0.2;
            score = Math.round(questionData.points * (1 + timeBonus));
        }
        
        // Évaluer la qualité du raisonnement
        const reasoningQuality = this.evaluateReasoningQuality(responseText, questionData);
        
        return {
            detectedAnswer,
            isCorrect,
            score,
            maxPoints: questionData.points,
            responseTime,
            reasoningQuality,
            category: questionData.category
        };
    }

    /**
     * Évalue la qualité du raisonnement
     */
    evaluateReasoningQuality(responseText, questionData) {
        let quality = 5; // Base
        
        // Vérifier la présence d'explication
        if (responseText.length > 100) quality += 2;
        
        // Vérifier les mots-clés liés au raisonnement
        const reasoningKeywords = ['parce que', 'donc', 'ainsi', 'car', 'puisque', 'en effet', 'par conséquent'];
        const hasReasoning = reasoningKeywords.some(keyword => responseText.toLowerCase().includes(keyword));
        if (hasReasoning) quality += 2;
        
        // Vérifier la structure logique
        if (responseText.includes('1.') || responseText.includes('premièrement') || responseText.includes('d\'abord')) {
            quality += 1;
        }
        
        return Math.min(10, quality);
    }

    /**
     * Lance le test complet de QI
     */
    async runCompleteIQTest() {
        console.log('🧠 === TEST DE QI SOPHISTIQUÉ POUR LOUNA ===\n');

        try {
            // Initialiser l'agent
            console.log('🚀 Initialisation de l\'agent LOUNA...');
            this.agent = new DeepSeekR1IntegratedAgent();
            await this.agent.initialize();
            console.log('✅ Agent initialisé\n');

            // Analyser l'état cognitif initial
            const cognitiveState = await this.analyzeCognitiveEvolution();

            // Créer le test
            const questions = this.createAdvancedIQTest();
            console.log(`\n📝 Test créé: ${questions.length} questions`);
            console.log(`🎯 Score maximum: ${questions.reduce((sum, q) => sum + q.points, 0)} points\n`);

            // Lancer le test
            console.log('🎯 === DÉBUT DU TEST DE QI ===');

            let totalScore = 0;
            let maxTotalScore = 0;
            const categoryScores = {};

            for (const question of questions) {
                const result = await this.askQuestion(question);
                this.testResults.push(result);

                totalScore += result.score;
                maxTotalScore += result.maxPoints;

                // Grouper par catégorie
                if (!categoryScores[result.category]) {
                    categoryScores[result.category] = { score: 0, max: 0, count: 0 };
                }
                categoryScores[result.category].score += result.score;
                categoryScores[result.category].max += result.maxPoints;
                categoryScores[result.category].count += 1;

                // Pause entre questions
                await new Promise(resolve => setTimeout(resolve, 2000));
            }

            // Calculer le QI final
            const testDuration = (Date.now() - this.startTime) / 1000;
            const successRate = totalScore / maxTotalScore;
            const averageReasoningQuality = this.testResults.reduce((sum, r) => sum + r.reasoningQuality, 0) / this.testResults.length;

            // Formule de QI sophistiquée
            let calculatedIQ = 100 + (successRate - 0.5) * 60; // Base
            calculatedIQ += (averageReasoningQuality - 5) * 3; // Bonus raisonnement
            calculatedIQ = Math.round(Math.max(70, Math.min(200, calculatedIQ))); // Limites

            // Afficher les résultats
            this.displayResults(totalScore, maxTotalScore, calculatedIQ, categoryScores, testDuration, cognitiveState, averageReasoningQuality);

        } catch (error) {
            console.error(`❌ Erreur test QI: ${error.message}`);
        }
    }

    /**
     * Affiche les résultats détaillés
     */
    displayResults(totalScore, maxTotalScore, calculatedIQ, categoryScores, testDuration, cognitiveState, averageReasoningQuality) {
        console.log('\n🎉 === RÉSULTATS FINAUX DU TEST DE QI ===\n');

        // Résultats globaux
        const successRate = (totalScore / maxTotalScore * 100);
        console.log('📊 **Résultats Globaux:**');
        console.log(`   • Score total: ${totalScore}/${maxTotalScore} points`);
        console.log(`   • Taux de réussite: ${successRate.toFixed(1)}%`);
        console.log(`   • QI calculé: ${calculatedIQ}`);
        console.log(`   • Durée du test: ${(testDuration/60).toFixed(1)} minutes`);
        console.log(`   • Qualité raisonnement: ${averageReasoningQuality.toFixed(1)}/10`);

        // Résultats par catégorie
        console.log('\n📂 **Performance par Catégorie:**');
        for (const [category, data] of Object.entries(categoryScores)) {
            const categoryPercent = (data.score / data.max * 100);
            console.log(`   • ${category}: ${data.score}/${data.max} (${categoryPercent.toFixed(1)}%)`);
        }

        // Analyse cognitive
        console.log('\n🧠 **Analyse Cognitive:**');
        console.log(`   • Mémoires stockées: ${cognitiveState.memoryEntries.toLocaleString()}`);
        console.log(`   • Neurones actifs: ${cognitiveState.neurons.toLocaleString()}`);
        console.log(`   • Taux neurogenèse: ${(cognitiveState.neurogenesisRate * 1000).toFixed(3)}/ms`);
        console.log(`   • Performance cognitive: ${(cognitiveState.cognitivePerformance * 100).toFixed(1)}%`);
        console.log(`   • QI mémoire thermique: ${cognitiveState.qi}`);

        // Profil cognitif
        console.log('\n🎯 **Profil Cognitif:**');
        const profile = this.generateCognitiveProfile(categoryScores);
        for (const [domain, level] of Object.entries(profile)) {
            console.log(`   • ${domain}: ${level}`);
        }

        // Évolution et recommandations
        console.log('\n📈 **Évolution et Recommandations:**');
        this.generateRecommendations(calculatedIQ, categoryScores, cognitiveState);

        // Classification finale
        console.log('\n🏆 **Classification Finale:**');
        this.classifyIntelligence(calculatedIQ);
    }

    /**
     * Génère le profil cognitif
     */
    generateCognitiveProfile(categoryScores) {
        const profile = {};

        for (const [category, data] of Object.entries(categoryScores)) {
            const percentage = (data.score / data.max) * 100;
            let level;

            if (percentage >= 90) level = "Exceptionnel";
            else if (percentage >= 80) level = "Très élevé";
            else if (percentage >= 70) level = "Élevé";
            else if (percentage >= 60) level = "Au-dessus de la moyenne";
            else if (percentage >= 50) level = "Moyen";
            else if (percentage >= 40) level = "En dessous de la moyenne";
            else level = "Faible";

            profile[category] = level;
        }

        return profile;
    }

    /**
     * Génère des recommandations
     */
    generateRecommendations(qi, categoryScores, cognitiveState) {
        // Analyser les points faibles
        const weakCategories = Object.entries(categoryScores)
            .filter(([_, data]) => (data.score / data.max) < 0.6)
            .map(([category, _]) => category);

        if (weakCategories.length > 0) {
            console.log(`   🔧 Améliorer: ${weakCategories.join(', ')}`);
        }

        // Recommandations basées sur le QI
        if (qi >= 130) {
            console.log('   🚀 Excellent niveau! Continuer l\'entraînement complexe');
        } else if (qi >= 110) {
            console.log('   ✅ Bon niveau! Augmenter la complexité des défis');
        } else {
            console.log('   📚 Renforcer les bases logiques et mathématiques');
        }

        // Recommandations neurogenèse
        if (cognitiveState.neurogenesisRate < 0.01) {
            console.log('   🧠 Stimuler la neurogenèse avec plus d\'interactions');
        }
    }

    /**
     * Classifie le niveau d'intelligence
     */
    classifyIntelligence(qi) {
        if (qi >= 145) {
            console.log('🏆 **GÉNIE** - Intelligence exceptionnelle');
        } else if (qi >= 130) {
            console.log('🌟 **TRÈS SUPÉRIEUR** - Intelligence très élevée');
        } else if (qi >= 115) {
            console.log('✨ **SUPÉRIEUR** - Intelligence au-dessus de la moyenne');
        } else if (qi >= 85) {
            console.log('📊 **MOYEN** - Intelligence dans la norme');
        } else if (qi >= 70) {
            console.log('⚠️ **EN DESSOUS DE LA MOYENNE** - Nécessite amélioration');
        } else {
            console.log('🔧 **FAIBLE** - Optimisation requise');
        }
    }
}

// Lancer le test
async function main() {
    const tester = new AdvancedIQTester();
    await tester.runCompleteIQTest();
}

if (require.main === module) {
    main();
}

module.exports = { AdvancedIQTester };
