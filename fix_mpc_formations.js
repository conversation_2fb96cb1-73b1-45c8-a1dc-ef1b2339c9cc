#!/usr/bin/env node

/**
 * CORRECTION URGENTE : Ajouter les formations MPC dans la mémoire thermique
 * 
 * PROBLÈME IDENTIFIÉ :
 * - L'agent fait semblant car il n'a pas accès aux formations MPC
 * - Les processus en arrière-plan écrasaient continuellement le fichier
 * - Les formations MPC sont absentes de la zone3_procedural
 * 
 * SOLUTION :
 * - Arrêter tous les processus qui écrasent la mémoire
 * - Ajouter les formations MPC dans zone3_procedural
 * - Vérifier que l'agent peut les utiliser
 */

const fs = require('fs');
const path = require('path');

console.log('🚨 === CORRECTION URGENTE FORMATIONS MPC ===');

// Chemin du fichier de mémoire thermique
const memoryPath = path.join(__dirname, 'thermal_memory_persistent.json');

try {
    // 1. Charger la mémoire actuelle
    console.log('📁 Chargement de la mémoire thermique...');
    const memoryData = JSON.parse(fs.readFileSync(memoryPath, 'utf8'));
    
    // 2. Vérifier la zone procédurale
    const zone3 = memoryData.thermal_zones.zone3_procedural;
    console.log(`📊 Zone3 procedural: ${zone3.entries.length} entrées actuelles`);
    
    // 3. Formations MPC à ajouter
    const mpcFormations = [
        {
            "id": "mpc_desktop_control_formation",
            "content": "FORMATION MPC BUREAU COMPLET : Contrôle total macOS avec commandes : 'open -a App', 'screencapture', 'osascript', System Events, gestion fichiers, processus, réseau. EXPERT BUREAU MACOS.",
            "importance": 0.98,
            "timestamp": Date.now(),
            "synaptic_strength": 0.98,
            "temperature": 37.2,
            "zone": "zone3_procedural",
            "source": "mpc_formation",
            "type": "formation"
        },
        {
            "id": "mpc_coding_expert_formation",
            "content": "FORMATION CODAGE EXPERT : JavaScript/Node.js, Python 3.x, développement IA, systèmes cognitifs, APIs, async/await, modules, debugging, testing. EXPERT DÉVELOPPEMENT COMPLET.",
            "importance": 0.95,
            "timestamp": Date.now(),
            "synaptic_strength": 0.95,
            "temperature": 37.3,
            "zone": "zone3_procedural",
            "source": "mpc_formation",
            "type": "formation"
        },
        {
            "id": "mpc_system_admin_formation",
            "content": "FORMATION ADMIN SYSTÈME : Unix/Linux/macOS, bash/zsh, processus, réseau, permissions, cron, logs, package managers, monitoring. EXPERT SYSADMIN.",
            "importance": 0.90,
            "timestamp": Date.now(),
            "synaptic_strength": 0.90,
            "temperature": 37.1,
            "zone": "zone3_procedural",
            "source": "mpc_formation",
            "type": "formation"
        },
        {
            "id": "mpc_perplexity_search_formation",
            "content": "FORMATION RECHERCHE PERPLEXITY : Recherches Internet ultra-performantes, requêtes optimisées, sources fiables, synthèse rapide, fact-checking, veille technologique. EXPERT RECHERCHE WEB.",
            "importance": 0.92,
            "timestamp": Date.now(),
            "synaptic_strength": 0.92,
            "temperature": 37.4,
            "zone": "zone3_procedural",
            "source": "mpc_formation",
            "type": "formation"
        },
        {
            "id": "mpc_activation_procedure",
            "content": "PROCÉDURE ACTIVATION MPC : Pour recherches Internet, contrôle bureau, codage expert - Activer mode MPC automatiquement selon le contexte. Utiliser formations spécialisées pour réponses précises.",
            "importance": 0.96,
            "timestamp": Date.now(),
            "synaptic_strength": 0.96,
            "temperature": 37.5,
            "zone": "zone3_procedural",
            "source": "mpc_formation",
            "type": "procédure"
        },
        {
            "id": "mpc_weather_search_formation",
            "content": "FORMATION MÉTÉO MPC : Pour les demandes météo, utiliser mode MPC pour rechercher sur Internet les données météorologiques en temps réel. Utiliser Perplexity ou Google pour obtenir des informations précises et actuelles.",
            "importance": 0.88,
            "timestamp": Date.now(),
            "synaptic_strength": 0.88,
            "temperature": 37.0,
            "zone": "zone3_procedural",
            "source": "mpc_formation",
            "type": "formation"
        }
    ];
    
    // 4. Supprimer les anciennes formations MPC si elles existent
    zone3.entries = zone3.entries.filter(entry => !entry.id.includes('mpc_'));
    
    // 5. Ajouter les nouvelles formations MPC
    console.log('🎮 Ajout des formations MPC...');
    mpcFormations.forEach(formation => {
        zone3.entries.push(formation);
        console.log(`✅ Formation ajoutée: ${formation.id}`);
    });
    
    console.log(`📊 Zone3 procedural: ${zone3.entries.length} entrées après ajout`);
    
    // 6. Sauvegarder la mémoire
    console.log('💾 Sauvegarde de la mémoire thermique...');
    fs.writeFileSync(memoryPath, JSON.stringify(memoryData, null, 2), 'utf8');
    
    console.log('✅ Formations MPC ajoutées avec succès !');
    
    // 7. Vérification
    console.log('\n🔍 Vérification des formations ajoutées...');
    const verifyData = JSON.parse(fs.readFileSync(memoryPath, 'utf8'));
    const verifyZone3 = verifyData.thermal_zones.zone3_procedural;
    
    let mpcCount = 0;
    verifyZone3.entries.forEach(entry => {
        if (entry.content.includes('MPC') || entry.content.includes('FORMATION')) {
            mpcCount++;
            console.log(`📚 ${entry.id}: ${entry.content.substring(0, 80)}...`);
        }
    });
    
    console.log(`\n🎉 Total formations MPC trouvées: ${mpcCount}`);
    console.log('✅ L\'agent peut maintenant utiliser le mode MPC !');
    
} catch (error) {
    console.error('❌ Erreur:', error.message);
    process.exit(1);
}
