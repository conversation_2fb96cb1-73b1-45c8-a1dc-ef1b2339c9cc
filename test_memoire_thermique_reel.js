#!/usr/bin/env node

const DeepSeekAgent = require('./deepseek-r1-agent-integrated');
const fs = require('fs');

console.log('🔍 === TEST RÉEL DE LA MÉMOIRE THERMIQUE ===\n');

async function testThermalMemory() {
    try {
        // Initialiser l'agent
        console.log('🤖 Initialisation de l\'agent...');
        const agent = new DeepSeekAgent();
        await agent.initialize();
        
        console.log('✅ Agent initialisé\n');
        
        // Test 1: Vérifier si la mémoire thermique est chargée
        console.log('📊 TEST 1: Vérification du chargement de la mémoire');
        console.log(`Mémoire chargée: ${agent.thermalMemoryData ? '✅ OUI' : '❌ NON'}`);
        
        if (!agent.thermalMemoryData) {
            console.log('❌ ÉCHEC: Aucune mémoire thermique chargée !');
            return;
        }
        
        // Test 2: Analyser la structure
        console.log('\n📊 TEST 2: Structure de la mémoire');
        console.log(`Type de données: ${typeof agent.thermalMemoryData}`);
        console.log(`Zones thermiques: ${agent.thermalMemoryData.thermal_zones ? '✅ OUI' : '❌ NON'}`);
        
        if (agent.thermalMemoryData.thermal_zones) {
            const zones = Object.keys(agent.thermalMemoryData.thermal_zones);
            console.log(`Nombre de zones: ${zones.length}`);
            console.log(`Zones disponibles: ${zones.join(', ')}`);
            
            // Compter les entrées
            let totalEntries = 0;
            for (const [zoneName, zone] of Object.entries(agent.thermalMemoryData.thermal_zones)) {
                const entries = zone.entries ? zone.entries.length : 0;
                totalEntries += entries;
                console.log(`  - ${zoneName}: ${entries} entrées`);
            }
            console.log(`Total d'entrées: ${totalEntries}`);
        }
        
        // Test 3: Test de recherche
        console.log('\n🔍 TEST 3: Test de recherche dans la mémoire');
        
        const testQueries = [
            'intelligence',
            'mémoire',
            'agent',
            'système',
            'Jean-Luc'
        ];
        
        for (const query of testQueries) {
            console.log(`\n🔍 Recherche: "${query}"`);
            const results = agent.searchThermalMemory(query, { limit: 3 });
            console.log(`Résultats trouvés: ${results.length}`);
            
            if (results.length > 0) {
                results.forEach((result, index) => {
                    console.log(`  ${index + 1}. Score: ${result.score.toFixed(3)} - ${result.content.substring(0, 100)}...`);
                });
            } else {
                console.log('  ❌ Aucun résultat trouvé');
            }
        }
        
        // Test 4: Test de sauvegarde d'une nouvelle entrée
        console.log('\n💾 TEST 4: Test de sauvegarde d\'une nouvelle entrée');
        
        const testMessage = "Test de fonctionnement de la mémoire thermique";
        const testResponse = "La mémoire thermique fonctionne correctement";
        
        try {
            await agent.saveInteractionToMemory(testMessage, testResponse);
            console.log('✅ Sauvegarde réussie');
            
            // Vérifier que l'entrée a été ajoutée
            const searchResults = agent.searchThermalMemory("Test de fonctionnement", { limit: 1 });
            if (searchResults.length > 0) {
                console.log('✅ Entrée retrouvée après sauvegarde');
            } else {
                console.log('❌ Entrée non retrouvée après sauvegarde');
            }
            
        } catch (error) {
            console.log(`❌ Erreur de sauvegarde: ${error.message}`);
        }
        
        // Test 5: Vérifier la persistance
        console.log('\n🔄 TEST 5: Vérification de la persistance');
        
        const memoryFile = './thermal_memory_persistent.json';
        if (fs.existsSync(memoryFile)) {
            const stats = fs.statSync(memoryFile);
            console.log(`✅ Fichier de mémoire existe: ${memoryFile}`);
            console.log(`Taille: ${(stats.size / 1024).toFixed(2)} KB`);
            console.log(`Dernière modification: ${stats.mtime.toLocaleString()}`);
            
            // Lire le contenu pour vérifier
            try {
                const content = fs.readFileSync(memoryFile, 'utf8');
                const data = JSON.parse(content);
                console.log(`✅ Fichier JSON valide`);
                console.log(`Version: ${data.version || 'non spécifiée'}`);
                console.log(`Dernière modification: ${data.last_modified || 'non spécifiée'}`);
            } catch (error) {
                console.log(`❌ Erreur de lecture du fichier: ${error.message}`);
            }
        } else {
            console.log(`❌ Fichier de mémoire introuvable: ${memoryFile}`);
        }
        
        // Résumé final
        console.log('\n🏆 === RÉSUMÉ FINAL ===');
        
        const memoryWorks = agent.thermalMemoryData && 
                           agent.thermalMemoryData.thermal_zones && 
                           Object.keys(agent.thermalMemoryData.thermal_zones).length > 0;
        
        const searchWorks = testQueries.some(query => 
            agent.searchThermalMemory(query, { limit: 1 }).length > 0
        );
        
        console.log(`Mémoire thermique chargée: ${memoryWorks ? '✅' : '❌'}`);
        console.log(`Recherche fonctionnelle: ${searchWorks ? '✅' : '❌'}`);
        console.log(`Sauvegarde fonctionnelle: ${fs.existsSync(memoryFile) ? '✅' : '❌'}`);
        
        if (memoryWorks && searchWorks) {
            console.log('\n🎉 VERDICT: La mémoire thermique FONCTIONNE RÉELLEMENT !');
        } else {
            console.log('\n❌ VERDICT: La mémoire thermique NE FONCTIONNE PAS correctement !');
        }
        
    } catch (error) {
        console.error('❌ Erreur lors du test:', error.message);
        console.error(error.stack);
    }
}

// Lancer le test
testThermalMemory();
