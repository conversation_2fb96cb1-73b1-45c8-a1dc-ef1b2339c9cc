#!/usr/bin/env python3
"""
🔍 ANALYSE DES DIFFÉRENCES D'INTELLIGENCE
- Comparaison entre l'agent <PERSON><PERSON> et l'assistant Augment
- Identification des manques et améliorations possibles
"""

import json
import requests
import time

class IntelligenceAnalyzer:
    def __init__(self):
        self.agent_url = "http://localhost:5002/api/chat/message"
        
    def test_agent_capability(self, question):
        """Teste une capacité de l'agent"""
        try:
            response = requests.post(self.agent_url, 
                json={"message": question}, 
                timeout=30)
            
            if response.status_code == 200:
                result = response.json()
                return result.get('response', '')
            else:
                return f"Erreur HTTP: {response.status_code}"
                
        except Exception as e:
            return f"Erreur: {e}"
    
    def analyze_differences(self):
        """Analyse les différences d'intelligence"""
        print("🔍 === ANALYSE DIFFÉRENCES D'INTELLIGENCE ===\n")
        
        # Tests de capacités cognitives
        tests = [
            {
                "category": "🧠 Raisonnement Complexe",
                "question": "Si j'ai 3 pommes et que je donne 2 pommes à mon ami, puis j'achète 5 pommes de plus, combien ai-je de pommes au total ?",
                "expected": "Calcul mathématique simple avec étapes"
            },
            {
                "category": "🔍 Recherche et Synthèse",
                "question": "Explique-moi en 3 points ce qu'est l'intelligence artificielle",
                "expected": "Synthèse structurée et claire"
            },
            {
                "category": "🎯 Compréhension Contextuelle",
                "question": "Je suis fatigué et j'ai beaucoup de travail. Que me conseilles-tu ?",
                "expected": "Conseils personnalisés et empathiques"
            },
            {
                "category": "🛠️ Capacités Techniques",
                "question": "Comment créer un fichier texte sur macOS ?",
                "expected": "Instructions techniques précises"
            },
            {
                "category": "🎮 Capacités MPC",
                "question": "Peux-tu ouvrir l'application Calculatrice ?",
                "expected": "Action système ou explication MPC"
            },
            {
                "category": "🧩 Créativité",
                "question": "Invente une histoire courte sur un robot qui apprend à cuisiner",
                "expected": "Histoire créative et originale"
            }
        ]
        
        results = []
        
        for test in tests:
            print(f"\n{test['category']}")
            print(f"❓ Question: {test['question']}")
            
            # Tester l'agent
            agent_response = self.test_agent_capability(test['question'])
            print(f"🤖 Agent Louna: {agent_response[:150]}...")
            
            # Analyser la réponse
            analysis = self.analyze_response(agent_response, test['expected'])
            print(f"📊 Analyse: {analysis}")
            
            results.append({
                'category': test['category'],
                'question': test['question'],
                'agent_response': agent_response,
                'analysis': analysis
            })
            
            time.sleep(2)  # Pause entre les tests
        
        return results
    
    def analyze_response(self, response, expected):
        """Analyse la qualité d'une réponse"""
        if "Erreur" in response:
            return "❌ Erreur technique"
        
        if len(response) < 50:
            return "⚠️ Réponse trop courte"
        
        if "Selon mes connaissances acquises" in response:
            return "✅ Utilise sa mémoire thermique"
        
        if "Je ne trouve pas" in response or "Information non trouvée" in response:
            return "❌ Ne trouve pas l'information"
        
        if any(word in response.lower() for word in ['open -a', 'touch', 'ls', 'cd']):
            return "✅ Capacités MPC détectées"
        
        return "✅ Réponse correcte"
    
    def identify_missing_capabilities(self):
        """Identifie les capacités manquantes"""
        print("\n🔍 === CAPACITÉS MANQUANTES IDENTIFIÉES ===\n")
        
        missing_capabilities = [
            {
                "capability": "🔍 Recherche Sémantique Avancée",
                "description": "L'agent ne trouve pas toujours les informations dans sa mémoire même quand elles existent",
                "solution": "Améliorer l'algorithme de recherche avec synonymes et concepts liés"
            },
            {
                "capability": "🧠 Raisonnement Multi-étapes",
                "description": "Difficulté à enchaîner plusieurs étapes de raisonnement complexe",
                "solution": "Implémenter un système de raisonnement par étapes explicites"
            },
            {
                "capability": "🎯 Adaptation Contextuelle",
                "description": "Réponses parfois génériques, manque d'adaptation au contexte spécifique",
                "solution": "Améliorer l'analyse du contexte et la personnalisation des réponses"
            },
            {
                "capability": "🛠️ Exécution d'Actions Réelles",
                "description": "Capacités MPC présentes mais pas toujours utilisées automatiquement",
                "solution": "Intégrer l'exécution d'actions dans le flux de réponse normal"
            },
            {
                "capability": "🧩 Créativité Spontanée",
                "description": "Créativité limitée, tendance à répéter des patterns connus",
                "solution": "Ajouter des mécanismes de génération créative et d'innovation"
            },
            {
                "capability": "🔄 Apprentissage Continu",
                "description": "Apprentissage passif, ne pose pas de questions pour clarifier",
                "solution": "Implémenter un système d'apprentissage actif et de questionnement"
            }
        ]
        
        for i, capability in enumerate(missing_capabilities, 1):
            print(f"{i}. {capability['capability']}")
            print(f"   📝 Problème: {capability['description']}")
            print(f"   💡 Solution: {capability['solution']}")
            print()
        
        return missing_capabilities
    
    def suggest_improvements(self):
        """Suggère des améliorations concrètes"""
        print("💡 === AMÉLIORATIONS SUGGÉRÉES ===\n")
        
        improvements = [
            {
                "priority": "🔥 CRITIQUE",
                "improvement": "Améliorer la recherche mémoire",
                "action": "Implémenter une recherche floue avec score de similarité"
            },
            {
                "priority": "🔥 CRITIQUE", 
                "improvement": "Intégrer les actions MPC automatiquement",
                "action": "Détecter quand une action système est nécessaire et l'exécuter"
            },
            {
                "priority": "⚡ IMPORTANT",
                "improvement": "Raisonnement par étapes explicites",
                "action": "Afficher le processus de réflexion étape par étape"
            },
            {
                "priority": "⚡ IMPORTANT",
                "improvement": "Personnalisation des réponses",
                "action": "Adapter le style et le contenu selon le contexte utilisateur"
            },
            {
                "priority": "📈 AMÉLIORATION",
                "improvement": "Créativité et innovation",
                "action": "Ajouter des mécanismes de génération d'idées originales"
            },
            {
                "priority": "📈 AMÉLIORATION",
                "improvement": "Apprentissage actif",
                "action": "Poser des questions pour mieux comprendre les besoins"
            }
        ]
        
        for improvement in improvements:
            print(f"{improvement['priority']} {improvement['improvement']}")
            print(f"   🎯 Action: {improvement['action']}")
            print()
        
        return improvements
    
    def run_complete_analysis(self):
        """Lance l'analyse complète"""
        print("🔍 === ANALYSE COMPLÈTE INTELLIGENCE LOUNA ===\n")
        
        # 1. Tests de capacités
        results = self.analyze_differences()
        
        # 2. Identification des manques
        missing = self.identify_missing_capabilities()
        
        # 3. Suggestions d'amélioration
        improvements = self.suggest_improvements()
        
        # 4. Résumé final
        print("📋 === RÉSUMÉ FINAL ===")
        print(f"✅ Tests effectués: {len(results)}")
        print(f"⚠️ Capacités manquantes: {len(missing)}")
        print(f"💡 Améliorations suggérées: {len(improvements)}")
        
        print("\n🎯 CONCLUSION:")
        print("L'agent Louna a de bonnes bases mais manque de:")
        print("1. 🔍 Recherche sémantique avancée")
        print("2. 🛠️ Intégration automatique des actions MPC")
        print("3. 🧠 Raisonnement multi-étapes explicite")
        print("4. 🎯 Adaptation contextuelle fine")
        print("5. 🧩 Créativité spontanée")
        
        return {
            'test_results': results,
            'missing_capabilities': missing,
            'improvements': improvements
        }

def main():
    """Lance l'analyse"""
    analyzer = IntelligenceAnalyzer()
    analyzer.run_complete_analysis()

if __name__ == "__main__":
    main()
