#!/usr/bin/env node

/**
 * Test direct de l'agent pour vérifier qu'il fonctionne
 */

const DeepSeekR1IntegratedAgent = require('./deepseek-r1-agent-integrated.js');

async function testAgentDirect() {
    console.log('🧪 === TEST DIRECT DE L\'AGENT ===\n');
    
    try {
        // Créer l'agent
        console.log('1. Création de l\'agent...');
        const agent = new DeepSeekR1IntegratedAgent();
        
        // Initialiser l'agent
        console.log('2. Initialisation de l\'agent...');
        const initialized = await agent.initialize();
        
        if (!initialized) {
            console.error('❌ Échec initialisation agent');
            return;
        }
        
        console.log('✅ Agent initialisé avec succès\n');
        
        // Test 1: Question simple
        console.log('3. Test question simple...');
        console.log('Question: "Quelle est la capitale de la France ?"');
        
        const response1 = await agent.processMessage("Quelle est la capitale de la France ?");
        
        console.log('Réponse:');
        console.log('- Message:', response1.message);
        console.log('- Reflection:', response1.reflection);
        console.log('- Memory used:', response1.memory_used?.length || 0);
        
        console.log('\n' + '='.repeat(50) + '\n');
        
        // Test 2: Salutation
        console.log('4. Test salutation...');
        console.log('Question: "Bonjour"');
        
        const response2 = await agent.processMessage("Bonjour");
        
        console.log('Réponse:');
        console.log('- Message:', response2.message);
        console.log('- Reflection:', response2.reflection);
        
        console.log('\n' + '='.repeat(50) + '\n');
        
        // Test 3: Question sur l'agent
        console.log('5. Test question sur l\'agent...');
        console.log('Question: "Qui es-tu ?"');
        
        const response3 = await agent.processMessage("Qui es-tu ?");
        
        console.log('Réponse:');
        console.log('- Message:', response3.message);
        console.log('- Reflection:', response3.reflection);
        
        console.log('\n🎉 Tests terminés avec succès !');
        
    } catch (error) {
        console.error(`❌ Erreur lors du test: ${error.message}`);
        console.error(error.stack);
    }
}

// Lancer le test
if (require.main === module) {
    testAgentDirect();
}

module.exports = { testAgentDirect };
