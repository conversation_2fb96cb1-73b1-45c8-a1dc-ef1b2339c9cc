#!/usr/bin/env node

/**
 * 🧪 TEST RÉPONSE AGENT
 * 
 * Test de la méthode processMessage pour vérifier
 * que l'agent répond correctement aux questions
 */

const DeepSeekR1IntegratedAgent = require('./deepseek-r1-agent-integrated');

async function testAgentResponse() {
    console.log('🧪 === TEST RÉPONSE AGENT ===\n');
    
    try {
        // Initialiser l'agent
        console.log('🤖 Initialisation de l\'agent...');
        const agent = new DeepSeekR1IntegratedAgent();
        
        const success = await agent.initialize();
        if (!success) {
            throw new Error('Échec initialisation agent');
        }
        
        console.log('✅ Agent initialisé avec succès\n');
        
        // Test de plusieurs messages
        const testMessages = [
            "Bonjour ! Comment ça va ?",
            "Peux-tu m'expliquer ta mémoire thermique ?",
            "Qu'est-ce que DeepSeek R1 8B ?",
            "Comment fonctionne ton système neurologique ?",
            "Merci pour tes réponses !"
        ];
        
        console.log('💬 === TEST DES RÉPONSES ===\n');
        
        for (let i = 0; i < testMessages.length; i++) {
            const message = testMessages[i];
            console.log(`📝 Message ${i + 1}: "${message}"`);
            
            try {
                // Traiter le message
                const startTime = Date.now();
                const response = await agent.processMessage(message);
                const endTime = Date.now();
                
                console.log(`⏱️ Temps de traitement: ${endTime - startTime}ms`);
                
                // Vérifier la structure de la réponse
                if (response.error) {
                    console.log(`❌ Erreur: ${response.message}`);
                } else {
                    console.log(`✅ Réponse générée avec succès`);
                    console.log(`📄 Message: ${response.message.substring(0, 100)}...`);
                    
                    if (response.reflection) {
                        console.log(`💭 Réflexion: ${response.reflection}`);
                    }
                    
                    if (response.memory_used && response.memory_used.length > 0) {
                        console.log(`🧠 Mémoires utilisées: ${response.memory_used.length}`);
                        response.memory_used.forEach((mem, idx) => {
                            console.log(`   ${idx + 1}. ${mem.content.substring(0, 50)}...`);
                        });
                    }
                    
                    if (response.brain_state) {
                        console.log(`🧠 État cérébral: ${response.brain_state.consciousness_level || 'N/A'}`);
                        console.log(`🌡️ Température: ${response.brain_state.global_temperature?.toFixed(2) || 'N/A'}°C`);
                    }
                }
                
                console.log('─'.repeat(60));
                
                // Pause entre les messages
                await new Promise(resolve => setTimeout(resolve, 1000));
                
            } catch (error) {
                console.log(`❌ Erreur traitement message: ${error.message}`);
                console.log('─'.repeat(60));
            }
        }
        
        // Vérifier la mémoire après les interactions
        console.log('\n📊 === VÉRIFICATION MÉMOIRE ===');
        
        // Rechercher les conversations sauvegardées
        const conversations = agent.searchThermalMemory("conversation", { limit: 10 });
        console.log(`💾 Conversations sauvegardées: ${conversations.length}`);
        
        if (conversations.length > 0) {
            console.log('Dernières conversations:');
            conversations.slice(0, 3).forEach((conv, idx) => {
                console.log(`  ${idx + 1}. ${conv.content.substring(0, 80)}...`);
            });
        }
        
        // Statistiques de l'agent
        console.log('\n📈 === STATISTIQUES AGENT ===');
        console.log(`🧠 Mémoire thermique: ${Object.keys(agent.thermalMemoryData.thermal_zones).length} zones`);
        console.log(`💾 Total entrées: ${agent.countTotalMemoryEntries()}`);
        
        if (agent.modules.advancedBrain) {
            const brainStats = agent.modules.advancedBrain.getNeurogenesisStats();
            console.log(`🌱 Neurones totaux: ${brainStats.total_neurons.toLocaleString()}`);
            console.log(`💾 Neurones stockés: ${brainStats.stored_neurons}`);
        }
        
        console.log('\n🎉 === RÉSULTAT FINAL ===');
        console.log('✅ Agent répond correctement aux messages');
        console.log('✅ Mémoire thermique fonctionnelle');
        console.log('✅ Sauvegarde des conversations');
        console.log('✅ Système neurologique intégré');
        console.log('✅ Format de réponse correct pour l\'interface');
        
        console.log('\n🚀 L\'agent est prêt pour l\'interface de chat !');
        
    } catch (error) {
        console.error(`❌ Erreur test agent: ${error.message}`);
        console.error(error.stack);
        process.exit(1);
    }
}

// Lancer le test
if (require.main === module) {
    testAgentResponse();
}

module.exports = { testAgentResponse };
