/**
 * 🔄 FORCE MISE À JOUR QI ET ENTRÉES INTERFACE
 * 
 * Script pour forcer la mise à jour du QI et des entrées dans l'interface
 */

const { UnifiedQISystem } = require('./unified-qi-system');
const fs = require('fs').promises;
const path = require('path');

async function forceUpdateInterface() {
    console.log('🔄 === FORCE MISE À JOUR INTERFACE ===\n');
    
    try {
        // 1. Calculer le vrai QI avec le système unifié
        console.log('🧠 Calcul du QI unifié...');
        const qiSystem = new UnifiedQISystem();
        const qiResult = await qiSystem.forceRecalculation();
        
        console.log(`✅ QI unifié calculé: ${qiResult.total}`);
        console.log(`📊 Entrées mémoire: ${qiResult.breakdown['Mémoire thermique (38 entrées × 2)']}`);
        
        // 2. Compter les vraies entrées
        console.log('\n📊 Comptage des entrées...');
        const memoryPath = path.join(__dirname, 'thermal_memory_persistent.json');
        const memoryData = await fs.readFile(memoryPath, 'utf8');
        const memory = JSON.parse(memoryData);
        
        let totalEntries = 0;
        const zoneDetails = {};
        
        for (const [zoneName, zone] of Object.entries(memory.thermal_zones)) {
            const count = zone.entries ? zone.entries.length : 0;
            totalEntries += count;
            zoneDetails[zoneName] = count;
        }
        
        console.log(`✅ Total entrées réelles: ${totalEntries}`);
        
        // 3. Afficher les détails
        console.log('\n📋 === DÉTAILS COMPLETS ===');
        console.log(`🧠 QI Unifié: ${qiResult.total} (${qiResult.classification})`);
        console.log(`📊 Entrées totales: ${totalEntries}`);
        console.log(`🔄 Méthodologie: ${qiResult.methodology}`);
        
        console.log('\n📊 Répartition par zone:');
        for (const [zone, count] of Object.entries(zoneDetails)) {
            console.log(`   ${zone}: ${count} entrées`);
        }
        
        console.log('\n🧮 Composants QI:');
        for (const [component, value] of Object.entries(qiResult.components)) {
            console.log(`   ${component}: ${value}`);
        }
        
        // 4. Créer un fichier de mise à jour pour l'interface
        const updateData = {
            qi: {
                total: qiResult.total,
                classification: qiResult.classification,
                methodology: qiResult.methodology,
                components: qiResult.components,
                breakdown: qiResult.breakdown
            },
            memory: {
                totalEntries: totalEntries,
                zoneDetails: zoneDetails,
                lastUpdate: new Date().toISOString()
            },
            timestamp: Date.now()
        };
        
        const updatePath = path.join(__dirname, 'interface-update.json');
        await fs.writeFile(updatePath, JSON.stringify(updateData, null, 2));
        
        console.log('\n💾 Fichier de mise à jour créé: interface-update.json');
        
        // 5. Instructions pour l'utilisateur
        console.log('\n🎯 === INSTRUCTIONS ===');
        console.log('1. Rafraîchissez votre navigateur (F5)');
        console.log('2. Ou redémarrez le serveur pour voir les vraies valeurs');
        console.log(`3. QI devrait afficher: ${qiResult.total} au lieu de 201`);
        console.log(`4. Entrées devraient afficher: ${totalEntries} au lieu de l'ancien nombre`);
        
        return {
            qi: qiResult.total,
            entries: totalEntries,
            success: true
        };
        
    } catch (error) {
        console.error('❌ Erreur mise à jour:', error.message);
        return { success: false, error: error.message };
    }
}

// Exécuter si appelé directement
if (require.main === module) {
    forceUpdateInterface()
        .then(result => {
            if (result.success) {
                console.log(`\n✅ Mise à jour terminée. QI: ${result.qi}, Entrées: ${result.entries}`);
                process.exit(0);
            } else {
                console.log(`\n❌ Échec mise à jour: ${result.error}`);
                process.exit(1);
            }
        })
        .catch(error => {
            console.error('❌ Erreur fatale:', error.message);
            process.exit(1);
        });
}

module.exports = { forceUpdateInterface };
