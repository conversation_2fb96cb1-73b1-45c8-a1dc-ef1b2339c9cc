#!/usr/bin/env node

/**
 * 🔗 TEST CONNEXION DIRECTE SANS OLLAMA
 * 
 * Vérifie que l'agent utilise bien une connexion directe
 * sans passer par Ollama ou d'autres serveurs externes
 */

const axios = require('axios');

class TestConnexionDirecte {
    constructor() {
        this.baseUrl = 'http://localhost:3000';
        this.testResults = [];
        this.startTime = Date.now();
    }

    async runTests() {
        console.log('🔗 === TEST CONNEXION DIRECTE SANS OLLAMA ===\n');
        console.log('🎯 Objectif: Vérifier que l\'agent répond rapidement sans Ollama\n');

        try {
            // Test 1: Vérifier que le serveur répond
            await this.testServerResponse();
            
            // Test 2: Test de vitesse de réponse
            await this.testResponseSpeed();
            
            // Test 3: Test de conversation continue
            await this.testContinuousConversation();
            
            // Test 4: Vérifier qu'aucun processus Ollama n'est requis
            await this.testNoOllamaRequired();
            
            // Rapport final
            this.generateReport();
            
        } catch (error) {
            console.error(`❌ Erreur fatale: ${error.message}`);
        }
    }

    async testServerResponse() {
        console.log('🔍 Test 1: Réponse du serveur');
        
        try {
            const startTime = Date.now();
            const response = await axios.get(`${this.baseUrl}/api/stats`, {
                timeout: 5000
            });
            const responseTime = Date.now() - startTime;
            
            if (response.status === 200) {
                console.log(`  ✅ Serveur répond en ${responseTime}ms`);
                console.log(`  📊 Agent actif: ${response.data.agent_active ? 'Oui' : 'Non'}`);
                this.recordTest('Réponse serveur', true, `${responseTime}ms`);
            } else {
                throw new Error(`Status ${response.status}`);
            }
            
        } catch (error) {
            console.log(`  ❌ Échec: ${error.message}`);
            this.recordTest('Réponse serveur', false, error.message);
        }
        
        console.log('');
    }

    async testResponseSpeed() {
        console.log('🔍 Test 2: Vitesse de réponse (sans Ollama)');
        
        const testMessages = [
            'Bonjour',
            'Quel est ton nom ?',
            'Comment ça va ?'
        ];
        
        let totalTime = 0;
        let successCount = 0;
        
        for (const message of testMessages) {
            try {
                console.log(`  💬 Test: "${message}"`);
                const startTime = Date.now();
                
                const response = await axios.post(`${this.baseUrl}/api/chat/message`, {
                    message: message
                }, {
                    timeout: 5000, // Timeout court pour forcer la connexion directe
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                const responseTime = Date.now() - startTime;
                totalTime += responseTime;
                
                if (response.data && response.data.success && response.data.response) {
                    console.log(`    ✅ Réponse en ${responseTime}ms`);
                    console.log(`    📝 "${response.data.response.substring(0, 80)}..."`);
                    successCount++;
                } else {
                    console.log(`    ❌ Réponse invalide`);
                }
                
                // Pause courte entre les messages
                await new Promise(resolve => setTimeout(resolve, 500));
                
            } catch (error) {
                console.log(`    ❌ Erreur: ${error.message}`);
            }
        }
        
        const averageTime = totalTime / successCount;
        
        if (successCount === testMessages.length && averageTime < 2000) {
            console.log(`  ✅ Vitesse excellente: ${averageTime.toFixed(0)}ms en moyenne`);
            this.recordTest('Vitesse réponse', true, `${averageTime.toFixed(0)}ms moyenne`);
        } else if (successCount > 0) {
            console.log(`  ⚠️  Vitesse acceptable: ${averageTime.toFixed(0)}ms en moyenne`);
            this.recordTest('Vitesse réponse', averageTime < 3000, `${averageTime.toFixed(0)}ms moyenne`);
        } else {
            console.log(`  ❌ Aucune réponse reçue`);
            this.recordTest('Vitesse réponse', false, 'Aucune réponse');
        }
        
        console.log('');
    }

    async testContinuousConversation() {
        console.log('🔍 Test 3: Conversation continue');
        
        const conversation = [
            'Bonjour LOUNA',
            'Peux-tu me parler de ta mémoire thermique ?',
            'Quel est ton QI ?',
            'Merci pour ces informations'
        ];
        
        let conversationSuccess = true;
        let totalTime = 0;
        
        for (let i = 0; i < conversation.length; i++) {
            try {
                const message = conversation[i];
                console.log(`  💬 Message ${i + 1}: "${message}"`);
                
                const startTime = Date.now();
                const response = await axios.post(`${this.baseUrl}/api/chat/message`, {
                    message: message
                }, {
                    timeout: 4000,
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                const responseTime = Date.now() - startTime;
                totalTime += responseTime;
                
                if (response.data && response.data.success) {
                    console.log(`    ✅ Réponse en ${responseTime}ms`);
                } else {
                    conversationSuccess = false;
                    console.log(`    ❌ Échec de réponse`);
                }
                
                // Pause réaliste entre les messages
                await new Promise(resolve => setTimeout(resolve, 1000));
                
            } catch (error) {
                conversationSuccess = false;
                console.log(`    ❌ Erreur: ${error.message}`);
            }
        }
        
        const averageTime = totalTime / conversation.length;
        
        if (conversationSuccess && averageTime < 2000) {
            console.log(`  ✅ Conversation fluide: ${averageTime.toFixed(0)}ms par message`);
            this.recordTest('Conversation continue', true, `${averageTime.toFixed(0)}ms par message`);
        } else {
            console.log(`  ⚠️  Conversation partiellement réussie`);
            this.recordTest('Conversation continue', false, 'Problèmes détectés');
        }
        
        console.log('');
    }

    async testNoOllamaRequired() {
        console.log('🔍 Test 4: Vérification absence Ollama');
        
        try {
            // Tenter de se connecter à Ollama pour voir s'il est requis
            let ollamaRunning = false;
            
            try {
                await axios.get('http://localhost:11434/api/version', { timeout: 1000 });
                ollamaRunning = true;
                console.log('  ℹ️  Ollama détecté en cours d\'exécution');
            } catch (error) {
                console.log('  ✅ Ollama non requis (non détecté)');
            }
            
            // Tester l'agent même si Ollama n'est pas disponible
            const testMessage = 'Test sans Ollama';
            console.log(`  🧪 Test message: "${testMessage}"`);
            
            const startTime = Date.now();
            const response = await axios.post(`${this.baseUrl}/api/chat/message`, {
                message: testMessage
            }, {
                timeout: 3000,
                headers: {
                    'Content-Type': 'application/json'
                }
            });
            
            const responseTime = Date.now() - startTime;
            
            if (response.data && response.data.success) {
                console.log(`  ✅ Agent fonctionne sans Ollama (${responseTime}ms)`);
                console.log(`  📝 Réponse: "${response.data.response.substring(0, 60)}..."`);
                this.recordTest('Indépendance Ollama', true, `Fonctionne sans Ollama (${responseTime}ms)`);
            } else {
                throw new Error('Agent ne répond pas sans Ollama');
            }
            
        } catch (error) {
            console.log(`  ❌ Échec: ${error.message}`);
            this.recordTest('Indépendance Ollama', false, error.message);
        }
        
        console.log('');
    }

    recordTest(name, passed, details) {
        this.testResults.push({ 
            name, 
            passed, 
            details,
            timestamp: Date.now() 
        });
    }

    generateReport() {
        const duration = Date.now() - this.startTime;
        const passed = this.testResults.filter(test => test.passed).length;
        const total = this.testResults.length;
        const successRate = (passed / total * 100).toFixed(1);
        
        console.log('📊 === RAPPORT CONNEXION DIRECTE ===\n');
        console.log(`🎯 Tests réussis: ${passed}/${total} (${successRate}%)`);
        console.log(`⏱️  Durée totale: ${duration}ms`);
        
        console.log('\n🔍 Détail des tests:');
        this.testResults.forEach(test => {
            const status = test.passed ? '✅' : '❌';
            console.log(`  ${status} ${test.name}: ${test.details}`);
        });
        
        if (successRate >= 75) {
            console.log('\n🎉 VERDICT: CONNEXION DIRECTE FONCTIONNELLE ✅');
            console.log('🚀 L\'agent répond rapidement sans dépendance Ollama !');
            
            if (successRate === 100) {
                console.log('🏆 PARFAIT: Tous les tests réussis !');
            }
        } else {
            console.log('\n⚠️  VERDICT: PROBLÈMES DE CONNEXION DÉTECTÉS');
            console.log('🔧 Vérifications nécessaires');
        }
        
        console.log('\n💡 La connexion directe permet des réponses ultra-rapides !');
    }
}

// Lancement du test
if (require.main === module) {
    const tester = new TestConnexionDirecte();
    tester.runTests();
}

module.exports = TestConnexionDirecte;
