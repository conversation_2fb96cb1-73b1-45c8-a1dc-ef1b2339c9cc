#!/usr/bin/env python3
"""
🖥️ CRÉATEUR DE BOUTON BUREAU POUR LOUNA
- Bouton de démarrage sur le bureau
- Authentification automatique Jeanpaul97180
- Lancement direct de l'agent
"""

import os
import sys
from pathlib import Path

def create_desktop_launcher():
    """Crée un bouton de démarrage sur le bureau"""
    print("🖥️ === CRÉATION BOUTON BUREAU LOUNA ===\n")
    
    # Chemin vers le bureau
    desktop_path = Path.home() / "Desktop"
    if not desktop_path.exists():
        desktop_path = Path.home() / "Bureau"  # Français
    
    # Chemin actuel du projet
    project_path = os.getcwd()
    
    # Script de démarrage
    launcher_script = f"""#!/bin/bash
# 🚀 LANCEUR LOUNA AGENT
# Authentification automatique pour Jeanpaul97180

echo "🚀 === DÉMARRAGE LOUNA AGENT ==="
echo "👑 Utilisateur authentifié: Jeanpaul97180"
echo "📍 Répertoire: {project_path}"

cd "{project_path}"

# Vérifier que Python est disponible
if ! command -v python3 &> /dev/null; then
    echo "❌ Python3 non trouvé"
    exit 1
fi

# Démarrer l'agent
echo "🚀 Démarrage de l'agent LOUNA..."
python3 louna_agent_unifie.py

# Garder la fenêtre ouverte
echo "Appuyez sur Entrée pour fermer..."
read
"""
    
    # Créer le script de démarrage
    launcher_path = desktop_path / "🚀 LOUNA Agent.command"
    
    try:
        with open(launcher_path, 'w') as f:
            f.write(launcher_script)
        
        # Rendre exécutable
        os.chmod(launcher_path, 0o755)
        
        print(f"✅ Bouton créé: {launcher_path}")
        
        # Créer aussi un script d'authentification directe
        auth_script = f"""#!/bin/bash
# 🔑 AUTHENTIFICATION DIRECTE JEANPAUL97180

cd "{project_path}"

# Envoyer le code d'authentification
curl -s http://localhost:5002/api/chat/message \\
  -H "Content-Type: application/json" \\
  -d '{{"message": "Jeanpaul97180"}}' | \\
  python3 -m json.tool

echo "✅ Code d'authentification envoyé"
"""
        
        auth_path = desktop_path / "🔑 Auth Jeanpaul97180.command"
        
        with open(auth_path, 'w') as f:
            f.write(auth_script)
        
        os.chmod(auth_path, 0o755)
        
        print(f"✅ Bouton auth créé: {auth_path}")
        
        # Créer un bouton pour ouvrir l'interface web
        web_script = f"""#!/bin/bash
# 🌐 OUVRIR INTERFACE WEB LOUNA

echo "🌐 Ouverture de l'interface LOUNA..."
open http://localhost:5002

# Attendre un peu puis envoyer l'auth
sleep 2
curl -s http://localhost:5002/api/chat/message \\
  -H "Content-Type: application/json" \\
  -d '{{"message": "Jeanpaul97180"}}'

echo "✅ Interface ouverte et authentifiée"
"""
        
        web_path = desktop_path / "🌐 Interface LOUNA.command"
        
        with open(web_path, 'w') as f:
            f.write(web_script)
        
        os.chmod(web_path, 0o755)
        
        print(f"✅ Bouton web créé: {web_path}")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur création boutons: {e}")
        return False

def create_mpc_test_button():
    """Crée un bouton pour tester le mode MPC"""
    print("\n🎮 === CRÉATION BOUTON TEST MPC ===")
    
    desktop_path = Path.home() / "Desktop"
    if not desktop_path.exists():
        desktop_path = Path.home() / "Bureau"
    
    project_path = os.getcwd()
    
    mpc_script = f"""#!/bin/bash
# 🎮 TEST MODE MPC LOUNA
# Actions sur le bureau et contrôle système

cd "{project_path}"

echo "🎮 === TEST MODE MPC LOUNA ==="
echo "👑 Utilisateur: Jeanpaul97180"
echo "🎯 Test des capacités MPC..."

# Authentification
curl -s http://localhost:5002/api/chat/message \\
  -H "Content-Type: application/json" \\
  -d '{{"message": "Jeanpaul97180"}}'

echo "🔑 Authentification envoyée"

# Test MPC - Actions bureau
echo "🎮 Test 1: Capacités MPC"
curl -s http://localhost:5002/api/chat/message \\
  -H "Content-Type: application/json" \\
  -d '{{"message": "Quelles sont tes capacités MPC pour contrôler le bureau ?"}}' | \\
  python3 -c "import sys, json; print(json.load(sys.stdin)['response'])"

echo "\\n🎮 Test 2: Actions possibles"
curl -s http://localhost:5002/api/chat/message \\
  -H "Content-Type: application/json" \\
  -d '{{"message": "Peux-tu ouvrir une application ou créer un fichier ?"}}' | \\
  python3 -c "import sys, json; print(json.load(sys.stdin)['response'])"

echo "\\n✅ Tests MPC terminés"
echo "Appuyez sur Entrée pour fermer..."
read
"""
    
    mpc_path = desktop_path / "🎮 Test MPC LOUNA.command"
    
    try:
        with open(mpc_path, 'w') as f:
            f.write(mpc_script)
        
        os.chmod(mpc_path, 0o755)
        
        print(f"✅ Bouton MPC créé: {mpc_path}")
        return True
        
    except Exception as e:
        print(f"❌ Erreur création bouton MPC: {e}")
        return False

def main():
    """Crée tous les boutons bureau"""
    print("🖥️ === CRÉATION BOUTONS BUREAU LOUNA ===\n")
    
    success1 = create_desktop_launcher()
    success2 = create_mpc_test_button()
    
    if success1 and success2:
        print("\n🎉 === BOUTONS CRÉÉS AVEC SUCCÈS ===")
        print("✅ 🚀 LOUNA Agent.command - Démarrer l'agent")
        print("✅ 🔑 Auth Jeanpaul97180.command - Authentification directe")
        print("✅ 🌐 Interface LOUNA.command - Ouvrir interface web")
        print("✅ 🎮 Test MPC LOUNA.command - Tester capacités MPC")
        print("\n📋 Instructions:")
        print("1. Double-cliquez sur '🚀 LOUNA Agent' pour démarrer")
        print("2. Double-cliquez sur '🔑 Auth Jeanpaul97180' pour vous authentifier")
        print("3. Double-cliquez sur '🌐 Interface LOUNA' pour ouvrir l'interface")
        print("4. Double-cliquez sur '🎮 Test MPC' pour tester les capacités")
    else:
        print("\n❌ Erreur lors de la création des boutons")

if __name__ == "__main__":
    main()
