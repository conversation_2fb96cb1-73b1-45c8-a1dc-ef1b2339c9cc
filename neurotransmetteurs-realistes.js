#!/usr/bin/env node

/**
 * 🧪 MODULE NEUROTRANSMETTEURS RÉALISTES
 * 
 * Système de neurotransmetteurs synchronisé avec la température thermique :
 * - Dopamine, Sérotonine, Acétylcholine, GABA, Noradrénaline
 * - Production basée sur température optimale
 * - Effets sur les performances cognitives
 */

const fs = require('fs');

class NeurotransmetteurRealistes {
    constructor(thermalMemoryPath = 'thermal_memory_persistent.json') {
        this.thermalMemoryPath = thermalMemoryPath;
        this.thermalMemory = null;
        
        // Configuration des neurotransmetteurs
        this.neurotransmitterConfig = {
            dopamine: {
                optimalTemp: 37.2,
                tempRange: 2.0,
                baseProduction: 0.8,
                effects: ['motivation', 'reward', 'focus']
            },
            serotonin: {
                optimalTemp: 36.8,
                tempRange: 1.5,
                baseProduction: 0.7,
                effects: ['mood', 'sleep', 'appetite']
            },
            acetylcholine: {
                optimalTemp: 37.0,
                tempRange: 1.8,
                baseProduction: 0.75,
                effects: ['memory', 'attention', 'learning']
            },
            gaba: {
                optimalTemp: 36.5,
                tempRange: 2.5,
                baseProduction: 0.6,
                effects: ['relaxation', 'anxiety_reduction', 'sleep']
            },
            noradrenaline: {
                optimalTemp: 37.5,
                tempRange: 2.2,
                baseProduction: 0.65,
                effects: ['alertness', 'stress_response', 'attention']
            }
        };
        
        // État des neurotransmetteurs
        this.neurotransmitterState = {
            lastUpdate: Date.now(),
            averageTemperature: 37.0,
            globalBalance: 'optimal'
        };
        
        console.log('🧪 Module neurotransmetteurs réalistes initialisé');
        this.loadThermalMemory();
    }
    
    /**
     * Charge la mémoire thermique
     */
    loadThermalMemory() {
        try {
            this.thermalMemory = JSON.parse(fs.readFileSync(this.thermalMemoryPath, 'utf8'));
            console.log('✅ Mémoire thermique chargée pour neurotransmetteurs');
        } catch (error) {
            console.error('❌ Erreur chargement mémoire:', error.message);
        }
    }
    
    /**
     * Calcule la température moyenne
     */
    calculateAverageTemperature() {
        if (!this.thermalMemory?.thermal_zones) return 37.0;
        
        let totalTemp = 0;
        let zoneCount = 0;
        
        for (const zone of Object.values(this.thermalMemory.thermal_zones)) {
            if (zone.temperature) {
                totalTemp += zone.temperature;
                zoneCount++;
            }
        }
        
        return zoneCount > 0 ? totalTemp / zoneCount : 37.0;
    }
    
    /**
     * Calcule l'efficacité de production basée sur la température
     */
    calculateProductionEfficiency(neurotransmitter, avgTemp) {
        const config = this.neurotransmitterConfig[neurotransmitter];
        if (!config) return 0.5;
        
        const tempDistance = Math.abs(avgTemp - config.optimalTemp);
        
        if (tempDistance <= config.tempRange) {
            return 1.0 - (tempDistance / config.tempRange) * 0.5;
        }
        
        return 0.1; // Production minimale si trop loin de l'optimal
    }
    
    /**
     * Met à jour tous les neurotransmetteurs
     */
    updateNeurotransmitters() {
        const now = Date.now();
        const deltaTime = (now - this.neurotransmitterState.lastUpdate) / 1000;
        const avgTemp = this.calculateAverageTemperature();
        
        if (!this.thermalMemory?.neural_system?.neurotransmitters) {
            console.log('⚠️ Système neurotransmetteurs non trouvé');
            return;
        }
        
        const neurotransmitters = this.thermalMemory.neural_system.neurotransmitters;
        const updates = {};
        
        // Mettre à jour chaque neurotransmetteur
        for (const [name, nt] of Object.entries(neurotransmitters)) {
            if (this.neurotransmitterConfig[name]) {
                const efficiency = this.calculateProductionEfficiency(name, avgTemp);
                const config = this.neurotransmitterConfig[name];
                
                // Décroissance naturelle
                const decayRate = 0.995;
                nt.level *= decayRate;
                
                // Production basée sur température
                const production = config.baseProduction * efficiency * deltaTime * 0.01;
                nt.level += production;
                
                // Limiter entre 0 et 1
                nt.level = Math.max(0.1, Math.min(1.0, nt.level));
                
                // Mettre à jour les récepteurs (simulation)
                nt.receptors = Math.floor(nt.receptors * (0.99 + nt.level * 0.01));
                
                // Mettre à jour le taux de production
                nt.production_rate = efficiency;
                
                updates[name] = {
                    level: nt.level,
                    efficiency: efficiency,
                    production: production
                };
            }
        }
        
        // Mettre à jour l'état global
        this.neurotransmitterState.lastUpdate = now;
        this.neurotransmitterState.averageTemperature = avgTemp;
        this.neurotransmitterState.globalBalance = this.calculateGlobalBalance(neurotransmitters);
        
        // Sauvegarder
        this.saveThermalMemory();
        
        return {
            updates: updates,
            averageTemperature: avgTemp,
            globalBalance: this.neurotransmitterState.globalBalance
        };
    }
    
    /**
     * Calcule l'équilibre global des neurotransmetteurs
     */
    calculateGlobalBalance(neurotransmitters) {
        let totalLevel = 0;
        let count = 0;
        
        for (const [name, nt] of Object.entries(neurotransmitters)) {
            if (this.neurotransmitterConfig[name]) {
                totalLevel += nt.level;
                count++;
            }
        }
        
        const averageLevel = count > 0 ? totalLevel / count : 0.5;
        
        if (averageLevel > 0.8) return 'excellent';
        if (averageLevel > 0.6) return 'optimal';
        if (averageLevel > 0.4) return 'acceptable';
        if (averageLevel > 0.2) return 'faible';
        return 'critique';
    }
    
    /**
     * Calcule les effets cognitifs des neurotransmetteurs
     */
    calculateCognitiveEffects() {
        if (!this.thermalMemory?.neural_system?.neurotransmitters) return {};
        
        const neurotransmitters = this.thermalMemory.neural_system.neurotransmitters;
        const effects = {
            motivation: 0,
            focus: 0,
            memory: 0,
            mood: 0,
            alertness: 0,
            relaxation: 0,
            learning: 0
        };
        
        // Calculer les effets basés sur les niveaux
        for (const [name, nt] of Object.entries(neurotransmitters)) {
            const config = this.neurotransmitterConfig[name];
            if (config) {
                for (const effect of config.effects) {
                    if (effects.hasOwnProperty(effect)) {
                        effects[effect] += nt.level * 0.2; // Contribution pondérée
                    }
                }
            }
        }
        
        // Normaliser les effets
        for (const effect in effects) {
            effects[effect] = Math.max(0, Math.min(1, effects[effect]));
        }
        
        return effects;
    }
    
    /**
     * Sauvegarde la mémoire thermique
     */
    saveThermalMemory() {
        try {
            fs.writeFileSync(this.thermalMemoryPath, JSON.stringify(this.thermalMemory, null, 2));
        } catch (error) {
            console.error('❌ Erreur sauvegarde mémoire:', error.message);
        }
    }
    
    /**
     * Démarre la régulation automatique
     */
    startAutomaticRegulation(intervalMs = 30000) { // Toutes les 30 secondes
        console.log(`🧪 Démarrage régulation neurotransmetteurs (${intervalMs}ms)`);
        
        setInterval(() => {
            const result = this.updateNeurotransmitters();
            
            if (result) {
                console.log(`🧪 Neurotransmetteurs mis à jour:`);
                console.log(`   🌡️ Température: ${result.averageTemperature.toFixed(2)}°C`);
                console.log(`   ⚖️ Équilibre: ${result.globalBalance}`);
                
                // Afficher les niveaux principaux
                for (const [name, update] of Object.entries(result.updates)) {
                    console.log(`   ${name}: ${update.level.toFixed(2)} (eff: ${update.efficiency.toFixed(2)})`);
                }
            }
        }, intervalMs);
    }
    
    /**
     * Retourne les statistiques des neurotransmetteurs
     */
    getStats() {
        const neurotransmitters = this.thermalMemory?.neural_system?.neurotransmitters || {};
        const cognitiveEffects = this.calculateCognitiveEffects();
        
        return {
            neurotransmitters: neurotransmitters,
            cognitiveEffects: cognitiveEffects,
            globalBalance: this.neurotransmitterState.globalBalance,
            averageTemperature: this.neurotransmitterState.averageTemperature,
            lastUpdate: this.neurotransmitterState.lastUpdate
        };
    }
    
    /**
     * Simule un boost de neurotransmetteur spécifique
     */
    boostNeurotransmitter(name, amount = 0.2) {
        if (!this.thermalMemory?.neural_system?.neurotransmitters?.[name]) {
            console.log(`⚠️ Neurotransmetteur ${name} non trouvé`);
            return false;
        }
        
        const nt = this.thermalMemory.neural_system.neurotransmitters[name];
        nt.level = Math.min(1.0, nt.level + amount);
        
        console.log(`⚡ Boost ${name}: niveau ${nt.level.toFixed(2)}`);
        this.saveThermalMemory();
        
        return true;
    }
}

// Test si exécuté directement
if (require.main === module) {
    const neurotransmitters = new NeurotransmetteurRealistes();
    
    // Test de mise à jour
    console.log('🧪 Test neurotransmetteurs...');
    const result = neurotransmitters.updateNeurotransmitters();
    console.log('📊 Résultat:', result);
    
    // Afficher les effets cognitifs
    const effects = neurotransmitters.calculateCognitiveEffects();
    console.log('🧠 Effets cognitifs:', effects);
    
    // Démarrer régulation automatique
    neurotransmitters.startAutomaticRegulation(20000); // Toutes les 20 secondes pour test
}

module.exports = NeurotransmetteurRealistes;
