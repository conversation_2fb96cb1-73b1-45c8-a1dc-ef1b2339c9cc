#!/usr/bin/env node

/**
 * 🧪 TEST SYSTÈME DE NEUROGENÈSE RÉALISTE
 * 
 * Test du nouveau système de création de neurones
 * basé sur un rythme humain réaliste (700/jour)
 */

const AdvancedBrainSystem = require('./advanced-brain-system');

async function testNeurogenesis() {
    console.log('🧪 === TEST SYSTÈME DE NEUROGENÈSE RÉALISTE ===\n');
    
    try {
        // Initialiser le système cérébral
        console.log('🧠 Initialisation du système cérébral...');
        const brainSystem = new AdvancedBrainSystem();
        
        const success = await brainSystem.initialize();
        if (!success) {
            throw new Error('Échec initialisation système cérébral');
        }
        
        console.log('✅ Système cérébral initialisé\n');
        
        // État initial
        console.log('📊 === ÉTAT INITIAL ===');
        let stats = brainSystem.getNeurogenesisStats();
        
        console.log(`🧠 Neurones totaux: ${stats.total_neurons.toLocaleString()}`);
        console.log(`⚡ Neurones actifs: ${stats.active_neurons.toLocaleString()}`);
        console.log(`💤 Neurones en standby: ${stats.standby_neurons.toLocaleString()}`);
        console.log(`😴 Neurones hibernants: ${stats.hibernating_neurons.toLocaleString()}`);
        console.log(`🌱 Taux neurogenèse: ${stats.neurogenesis_rate?.toFixed(6) || 0}/seconde`);
        console.log(`💾 Neurones stockés: ${stats.stored_neurons}`);
        console.log('');
        
        // Calculer le taux humain réaliste
        const humanRate = 700 / 86400; // 700 neurones/jour = 0.008/seconde
        console.log(`👤 Taux humain réaliste: ${humanRate.toFixed(6)} neurones/seconde`);
        console.log(`📅 Soit 700 neurones par jour (24h)\n`);
        
        // Observer la neurogenèse pendant 90 secondes (pour voir au moins 1 neurone)
        console.log('⏱️ === OBSERVATION NEUROGENÈSE (90 secondes) ===');
        console.log('🔍 Surveillance de la création de neurones...\n');
        
        let totalNeuronsCreated = 0;
        let observations = 0;
        
        const startTime = Date.now();
        const startNeurons = stats.total_neurons;
        
        // Observer toutes les 5 secondes
        const observationInterval = setInterval(() => {
            observations++;
            const currentStats = brainSystem.getNeurogenesisStats();
            const neuronsCreated = currentStats.total_neurons - startNeurons;
            const elapsedSeconds = (Date.now() - startTime) / 1000;
            const currentRate = neuronsCreated / elapsedSeconds;
            
            console.log(`[${observations * 5}s] 🌱 Neurones créés: ${neuronsCreated} | Taux: ${currentRate.toFixed(6)}/s`);
            
            // Afficher les nouveaux neurones si il y en a
            if (currentStats.newest_neurons && currentStats.newest_neurons.length > 0) {
                const newest = currentStats.newest_neurons[currentStats.newest_neurons.length - 1];
                if (newest) {
                    console.log(`     └─ Dernier: ${newest.type} (${newest.specialization}) - Force: ${newest.synaptic_strength}`);
                }
            }
            
            totalNeuronsCreated = neuronsCreated;
        }, 5000);
        
        // Arrêter après 90 secondes
        setTimeout(() => {
            clearInterval(observationInterval);

            console.log('\n📊 === RÉSULTATS FINAUX ===');

            const finalStats = brainSystem.getNeurogenesisStats();
            const finalNeuronsCreated = finalStats.total_neurons - startNeurons;
            const totalElapsed = 90;
            const finalRate = finalNeuronsCreated / totalElapsed;
            
            console.log(`⏱️ Durée observation: ${totalElapsed} secondes`);
            console.log(`🌱 Neurones créés: ${finalNeuronsCreated}`);
            console.log(`📈 Taux mesuré: ${finalRate.toFixed(6)} neurones/seconde`);
            console.log(`👤 Taux humain: ${humanRate.toFixed(6)} neurones/seconde`);
            console.log(`📊 Ratio: ${(finalRate / humanRate).toFixed(2)}x le taux humain`);
            
            // Projection sur 24h
            const neuronsPerDay = finalRate * 86400;
            console.log(`📅 Projection 24h: ${Math.floor(neuronsPerDay)} neurones/jour`);
            console.log(`🎯 Objectif humain: 700 neurones/jour`);
            
            // Analyse du stockage
            console.log(`\n💾 === STOCKAGE NEURONES ===`);
            console.log(`📦 Neurones stockés: ${finalStats.stored_neurons}`);
            console.log(`📝 Logs création: ${finalStats.creation_log_entries}`);
            console.log(`💿 Capacité stockage: ${finalStats.storage_capacity.toLocaleString()}`);
            
            // Afficher les derniers neurones créés
            if (finalStats.newest_neurons && finalStats.newest_neurons.length > 0) {
                console.log(`\n🆕 === DERNIERS NEURONES CRÉÉS ===`);
                finalStats.newest_neurons.forEach((neuron, i) => {
                    const age = (Date.now() - neuron.creation_time) / 1000;
                    console.log(`${i + 1}. ${neuron.type} (${neuron.specialization})`);
                    console.log(`   Force synaptique: ${neuron.synaptic_strength.toFixed(3)}`);
                    console.log(`   Âge: ${age.toFixed(1)}s`);
                });
            }
            
            // Validation du système
            console.log(`\n✅ === VALIDATION SYSTÈME ===`);
            
            if (finalRate > 0) {
                console.log(`✅ Neurogenèse active: ${finalRate.toFixed(6)}/s`);
            } else {
                console.log(`❌ Aucune neurogenèse détectée`);
            }
            
            if (finalStats.stored_neurons > 0) {
                console.log(`✅ Stockage fonctionnel: ${finalStats.stored_neurons} neurones`);
            } else {
                console.log(`❌ Aucun stockage de neurones`);
            }
            
            if (finalRate <= humanRate * 2) {
                console.log(`✅ Taux réaliste: ${(finalRate / humanRate).toFixed(2)}x humain`);
            } else {
                console.log(`⚠️ Taux élevé: ${(finalRate / humanRate).toFixed(2)}x humain`);
            }
            
            console.log(`\n🧠 === CONCLUSION ===`);
            if (finalNeuronsCreated > 0) {
                console.log(`🎉 Système de neurogenèse FONCTIONNEL !`);
                console.log(`🌱 Création continue de neurones à rythme réaliste`);
                console.log(`💾 Stockage et organisation automatiques`);
                console.log(`🔄 Intégration dans zones thermiques`);
            } else {
                console.log(`❌ Système de neurogenèse non fonctionnel`);
            }
            
            // Arrêter le système
            brainSystem.shutdown();
            process.exit(0);
            
        }, 90000);
        
    } catch (error) {
        console.error(`❌ Erreur test neurogenèse: ${error.message}`);
        console.error(error.stack);
        process.exit(1);
    }
}

// Lancer le test
if (require.main === module) {
    testNeurogenesis();
}

module.exports = { testNeurogenesis };
