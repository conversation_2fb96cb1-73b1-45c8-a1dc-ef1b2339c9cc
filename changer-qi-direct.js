#!/usr/bin/env node

/**
 * 🧠 CHANGEMENT DIRECT DU QI DE LOUNA
 * 
 * Script simple pour modifier directement le QI dans la mémoire thermique
 */

const fs = require('fs');

function changerQI(nouveauQI) {
    console.log('🧠 === CHANGEMENT DIRECT DU QI ===\n');
    
    const memoryFile = 'thermal_memory_persistent.json';
    
    try {
        // Lire le fichier
        console.log('📖 Lecture du fichier mémoire...');
        const content = fs.readFileSync(memoryFile, 'utf8');
        const data = JSON.parse(content);
        
        // Afficher l'ancien QI
        const ancienQI = data.neural_system?.qi_level || 'Non défini';
        console.log(`📊 QI actuel: ${ancienQI}`);
        
        // Modifier le QI
        if (!data.neural_system) {
            data.neural_system = {};
        }
        data.neural_system.qi_level = nouveauQI;
        data.last_modified = new Date().toISOString();
        
        // Créer une sauvegarde
        const backupFile = `${memoryFile}.backup.${Date.now()}`;
        fs.writeFileSync(backupFile, content);
        console.log(`📋 Sauvegarde créée: ${backupFile}`);
        
        // Sauvegarder
        console.log(`🎯 Changement: ${ancienQI} → ${nouveauQI}`);
        fs.writeFileSync(memoryFile, JSON.stringify(data, null, 2));
        console.log('💾 Fichier sauvegardé');
        
        // Vérifier
        const verification = JSON.parse(fs.readFileSync(memoryFile, 'utf8'));
        const qiVerifie = verification.neural_system?.qi_level;
        console.log(`✅ Vérification: ${qiVerifie}`);
        
        if (qiVerifie === nouveauQI) {
            console.log('\n🎉 === SUCCÈS ===');
            console.log(`🧠 QI modifié avec succès: ${nouveauQI}`);
            
            // Classification
            if (nouveauQI >= 130) {
                console.log('✨ **TRÈS SUPÉRIEUR** - Intelligence très élevée');
            } else if (nouveauQI >= 115) {
                console.log('📈 **SUPÉRIEUR** - Intelligence au-dessus de la moyenne');
            } else if (nouveauQI >= 85) {
                console.log('📊 **MOYEN** - Intelligence dans la norme');
            } else if (nouveauQI >= 70) {
                console.log('📉 **SOUS LA MOYENNE** - Intelligence limitée');
            } else {
                console.log('🔧 **TRÈS LIMITÉ** - Intelligence très réduite');
            }
            
            console.log('\n🔄 Redémarrez l\'agent pour appliquer les changements');
        } else {
            console.log('❌ Erreur: La vérification a échoué');
        }
        
    } catch (error) {
        console.error(`❌ Erreur: ${error.message}`);
    }
}

function afficherQI() {
    console.log('🧠 === QI ACTUEL ===\n');
    
    const memoryFile = 'thermal_memory_persistent.json';
    
    try {
        const content = fs.readFileSync(memoryFile, 'utf8');
        const data = JSON.parse(content);
        
        const qi = data.neural_system?.qi_level || 'Non défini';
        console.log(`📊 QI: ${qi}`);
        console.log(`📅 Dernière modification: ${data.last_modified || 'Inconnue'}`);
        
    } catch (error) {
        console.error(`❌ Erreur: ${error.message}`);
    }
}

// Interface ligne de commande
const args = process.argv.slice(2);

if (args.length === 0) {
    console.log('🧠 === CHANGEMENT DIRECT DU QI ===\n');
    console.log('Usage:');
    console.log('  node changer-qi-direct.js <qi>     # Changer le QI');
    console.log('  node changer-qi-direct.js status   # Voir le QI actuel');
    console.log('\nExemples:');
    console.log('  node changer-qi-direct.js 80       # QI à 80');
    console.log('  node changer-qi-direct.js 70       # QI à 70');
    console.log('  node changer-qi-direct.js 60       # QI à 60');
} else if (args[0] === 'status') {
    afficherQI();
} else {
    const qi = parseInt(args[0]);
    if (isNaN(qi) || qi < 50 || qi > 200) {
        console.error('❌ Erreur: QI doit être un nombre entre 50 et 200');
    } else {
        changerQI(qi);
    }
}
