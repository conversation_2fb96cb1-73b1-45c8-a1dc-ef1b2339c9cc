/**
 * 🚀 INTÉGRATION CHAT LOUNA DANS ELECTRON
 * Fichier pour intégrer l'interface de chat dans votre application Electron
 * Créé pour Jean-<PERSON> 97180
 */

/**
 * Configuration de l'interface de chat
 */
const CHAT_CONFIG = {
    serverUrl: 'http://localhost:3000',
    windowOptions: {
        width: 1200,
        height: 800,
        webPreferences: {
            nodeIntegration: false,
            contextIsolation: true,
            enableRemoteModule: false
        },
        icon: null, // Ajoutez le chemin vers votre icône
        title: 'LOUNA - Chat DeepSeek R1 8B',
        show: false, // Ne pas afficher immédiatement
        autoHideMenuBar: true,
        resizable: true,
        minimizable: true,
        maximizable: true,
        closable: true
    }
};

/**
 * Classe pour gérer l'intégration du chat dans Electron
 */
class ElectronChatIntegration {
    constructor(mainWindow) {
        this.mainWindow = mainWindow;
        this.chatWindow = null;
        this.serverStatus = false;
        
        // Initialiser
        this.init();
    }

    /**
     * Initialisation de l'intégration
     */
    init() {
        console.log('🚀 Initialisation de l\'intégration chat LOUNA...');
        
        // Vérifier le statut du serveur
        this.checkServerStatus();
        
        // Vérification périodique
        setInterval(() => {
            this.checkServerStatus();
        }, 30000);
        
        console.log('✅ Intégration chat initialisée');
    }

    /**
     * Vérifier le statut du serveur de chat
     */
    async checkServerStatus() {
        try {
            const { net } = require('electron');
            const request = net.request(CHAT_CONFIG.serverUrl + '/health');
            
            request.on('response', (response) => {
                if (response.statusCode === 200) {
                    this.serverStatus = true;
                    console.log('✅ Serveur de chat disponible');
                } else {
                    this.serverStatus = false;
                    console.log('⚠️ Serveur de chat non disponible');
                }
            });
            
            request.on('error', (error) => {
                this.serverStatus = false;
                console.log('❌ Erreur connexion serveur chat:', error.message);
            });
            
            request.end();
            
        } catch (error) {
            this.serverStatus = false;
            console.log('❌ Erreur vérification serveur:', error.message);
        }
    }

    /**
     * Ouvrir l'interface de chat
     */
    openChatInterface() {
        console.log('💬 Ouverture de l\'interface de chat LOUNA...');

        // Vérifier si une fenêtre de chat existe déjà
        if (this.chatWindow && !this.chatWindow.isDestroyed()) {
            // Ramener la fenêtre au premier plan
            this.chatWindow.show();
            this.chatWindow.focus();
            return;
        }

        // Créer une nouvelle fenêtre de chat
        const { BrowserWindow } = require('electron');
        
        this.chatWindow = new BrowserWindow(CHAT_CONFIG.windowOptions);

        // Charger l'interface de chat
        this.chatWindow.loadURL(CHAT_CONFIG.serverUrl);

        // Afficher la fenêtre une fois chargée
        this.chatWindow.once('ready-to-show', () => {
            this.chatWindow.show();
            console.log('✅ Interface de chat ouverte');
        });

        // Gérer la fermeture
        this.chatWindow.on('closed', () => {
            this.chatWindow = null;
            console.log('💬 Fenêtre de chat fermée');
        });

        // Gérer les erreurs de chargement
        this.chatWindow.webContents.on('did-fail-load', (event, errorCode, errorDescription) => {
            console.error('❌ Erreur chargement chat:', errorDescription);
            
            // Afficher une page d'erreur
            this.showErrorPage();
        });

        return this.chatWindow;
    }

    /**
     * Afficher une page d'erreur si le serveur n'est pas disponible
     */
    showErrorPage() {
        const errorHtml = `
        <!DOCTYPE html>
        <html>
        <head>
            <title>Erreur - Chat LOUNA</title>
            <style>
                body {
                    font-family: Arial, sans-serif;
                    background: linear-gradient(135deg, #1a1a2e, #16213e);
                    color: white;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    height: 100vh;
                    margin: 0;
                }
                .error-container {
                    text-align: center;
                    padding: 40px;
                    background: rgba(255, 255, 255, 0.1);
                    border-radius: 15px;
                    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
                }
                .error-icon {
                    font-size: 64px;
                    margin-bottom: 20px;
                }
                h1 {
                    color: #ff6b35;
                    margin-bottom: 20px;
                }
                .retry-btn {
                    background: linear-gradient(135deg, #00d4ff, #0099cc);
                    color: white;
                    border: none;
                    padding: 12px 24px;
                    border-radius: 25px;
                    cursor: pointer;
                    font-size: 16px;
                    margin-top: 20px;
                }
                .retry-btn:hover {
                    background: linear-gradient(135deg, #0099cc, #00d4ff);
                }
            </style>
        </head>
        <body>
            <div class="error-container">
                <div class="error-icon">⚠️</div>
                <h1>Serveur de Chat Non Disponible</h1>
                <p>L'interface de chat LOUNA n'est pas accessible.</p>
                <p>Veuillez vérifier que le serveur est démarré sur le port 3000.</p>
                <button class="retry-btn" onclick="location.reload()">Réessayer</button>
            </div>
        </body>
        </html>
        `;

        this.chatWindow.loadURL('data:text/html;charset=utf-8,' + encodeURIComponent(errorHtml));
    }

    /**
     * Fermer l'interface de chat
     */
    closeChatInterface() {
        if (this.chatWindow && !this.chatWindow.isDestroyed()) {
            this.chatWindow.close();
        }
    }

    /**
     * Obtenir le statut du serveur
     */
    getServerStatus() {
        return this.serverStatus;
    }

    /**
     * Créer le menu pour l'interface de chat
     */
    createChatMenu() {
        return {
            label: 'Chat LOUNA',
            submenu: [
                {
                    label: 'Ouvrir Interface de Chat',
                    accelerator: 'CmdOrCtrl+Shift+C',
                    click: () => {
                        this.openChatInterface();
                    }
                },
                {
                    label: 'Fermer Chat',
                    accelerator: 'CmdOrCtrl+Shift+X',
                    click: () => {
                        this.closeChatInterface();
                    }
                },
                { type: 'separator' },
                {
                    label: 'Statut Serveur',
                    enabled: false,
                    label: this.serverStatus ? '✅ Serveur Disponible' : '❌ Serveur Indisponible'
                }
            ]
        };
    }

    /**
     * Injecter le bouton dans une page web
     */
    injectChatButton(webContents) {
        const buttonScript = `
        // Créer le bouton de chat
        const chatBtn = document.createElement('button');
        chatBtn.innerHTML = '💬 Chat LOUNA';
        chatBtn.style.cssText = \`
            position: fixed;
            bottom: 30px;
            right: 30px;
            background: linear-gradient(135deg, #00d4ff, #0099cc);
            color: white;
            border: none;
            padding: 15px 25px;
            border-radius: 50px;
            cursor: pointer;
            font-weight: bold;
            font-size: 16px;
            z-index: 10000;
            box-shadow: 0 6px 20px rgba(0, 212, 255, 0.4);
            transition: all 0.3s ease;
        \`;
        
        chatBtn.addEventListener('click', () => {
            // Envoyer un message à Electron pour ouvrir le chat
            window.electronAPI?.openChat();
        });
        
        chatBtn.addEventListener('mouseenter', () => {
            chatBtn.style.transform = 'translateY(-3px)';
            chatBtn.style.boxShadow = '0 8px 25px rgba(0, 212, 255, 0.6)';
        });
        
        chatBtn.addEventListener('mouseleave', () => {
            chatBtn.style.transform = 'translateY(0)';
            chatBtn.style.boxShadow = '0 6px 20px rgba(0, 212, 255, 0.4)';
        });
        
        document.body.appendChild(chatBtn);
        `;

        webContents.executeJavaScript(buttonScript);
    }
}

/**
 * Fonction d'initialisation pour Electron
 */
function initializeChatIntegration(mainWindow) {
    const chatIntegration = new ElectronChatIntegration(mainWindow);
    
    // Exposer les fonctions à l'API Electron
    const { ipcMain } = require('electron');
    
    ipcMain.handle('open-chat', () => {
        return chatIntegration.openChatInterface();
    });
    
    ipcMain.handle('close-chat', () => {
        return chatIntegration.closeChatInterface();
    });
    
    ipcMain.handle('chat-server-status', () => {
        return chatIntegration.getServerStatus();
    });
    
    return chatIntegration;
}

module.exports = {
    ElectronChatIntegration,
    initializeChatIntegration,
    CHAT_CONFIG
};
