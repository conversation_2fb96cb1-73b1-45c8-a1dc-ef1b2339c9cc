<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bouton Chat LOUNA</title>
    <style>
        /* Styles pour le bouton d'accès au chat */
        .chat-access-button {
            position: fixed;
            bottom: 30px;
            right: 30px;
            background: linear-gradient(135deg, #00d4ff, #0099cc);
            color: white;
            border: none;
            padding: 15px 25px;
            border-radius: 50px;
            cursor: pointer;
            font-weight: bold;
            font-size: 16px;
            display: flex;
            align-items: center;
            gap: 10px;
            transition: all 0.3s ease;
            box-shadow: 0 6px 20px rgba(0, 212, 255, 0.4);
            z-index: 10000;
            text-decoration: none;
            min-width: 200px;
            justify-content: center;
        }

        .chat-access-button:hover {
            background: linear-gradient(135deg, #0099cc, #00d4ff);
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 212, 255, 0.6);
        }

        .chat-access-button:active {
            transform: translateY(-1px);
            box-shadow: 0 4px 15px rgba(0, 212, 255, 0.4);
        }

        .chat-access-button .icon {
            font-size: 20px;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }

        /* Notification de statut */
        .status-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 10px 20px;
            border-radius: 10px;
            font-size: 14px;
            z-index: 10001;
            display: none;
            animation: slideIn 0.3s ease-out;
        }

        @keyframes slideIn {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }

        /* Styles pour intégration dans l'interface existante */
        .electron-header-button {
            background: linear-gradient(135deg, #00d4ff, #0099cc);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 25px;
            cursor: pointer;
            font-weight: bold;
            font-size: 14px;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 212, 255, 0.3);
            text-decoration: none;
            margin-left: 15px;
        }

        .electron-header-button:hover {
            background: linear-gradient(135deg, #0099cc, #00d4ff);
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 212, 255, 0.4);
        }

        .electron-header-button .icon {
            font-size: 16px;
        }
    </style>
</head>
<body>

<!-- BOUTON FLOTTANT POUR ACCÈS AU CHAT -->
<button id="chatAccessBtn" class="chat-access-button" onclick="openChatInterface()">
    <span class="icon">💬</span>
    <span>Chat LOUNA DeepSeek</span>
</button>

<!-- NOTIFICATION DE STATUT -->
<div id="statusNotification" class="status-notification"></div>

<script>
/**
 * 💬 FONCTION POUR OUVRIR L'INTERFACE DE CHAT LOUNA
 * Cette fonction ouvre l'interface de chat moderne avec mémoire thermique
 */
function openChatInterface() {
    console.log('💬 Ouverture de l\'interface de chat LOUNA...');
    
    // URL de l'interface de chat (ajustez selon votre configuration)
    const chatInterfaceUrl = 'http://localhost:3000';
    
    // Afficher une notification
    showStatusNotification('💬 Ouverture de l\'interface de chat...', 'info');
    
    // Ouvrir dans une nouvelle fenêtre
    const chatWindow = window.open(chatInterfaceUrl, 'LOUNAChat', 
        'width=1200,height=800,scrollbars=yes,resizable=yes,status=yes,toolbar=no,menubar=no');
    
    if (!chatWindow) {
        // Si le popup est bloqué
        showStatusNotification('❌ Popup bloqué. Cliquez pour ouvrir manuellement.', 'error');
        
        // Proposer une alternative
        setTimeout(() => {
            if (confirm('Le popup a été bloqué. Voulez-vous ouvrir l\'interface de chat dans un nouvel onglet ?')) {
                window.open(chatInterfaceUrl, '_blank');
            }
        }, 1000);
    } else {
        // Succès
        showStatusNotification('✅ Interface de chat ouverte !', 'success');
        
        // Optionnel : Focus sur la nouvelle fenêtre
        chatWindow.focus();
    }
}

/**
 * Affiche une notification de statut
 */
function showStatusNotification(message, type = 'info') {
    const notification = document.getElementById('statusNotification');
    
    // Couleurs selon le type
    const colors = {
        info: 'rgba(0, 212, 255, 0.9)',
        success: 'rgba(40, 167, 69, 0.9)',
        error: 'rgba(220, 53, 69, 0.9)',
        warning: 'rgba(255, 193, 7, 0.9)'
    };
    
    notification.style.background = colors[type] || colors.info;
    notification.textContent = message;
    notification.style.display = 'block';
    
    // Masquer après 3 secondes
    setTimeout(() => {
        notification.style.display = 'none';
    }, 3000);
}

/**
 * Vérification de la disponibilité du serveur de chat
 */
async function checkChatServerStatus() {
    try {
        const response = await fetch('http://localhost:3000/health', { 
            method: 'GET',
            timeout: 2000 
        });
        
        if (response.ok) {
            console.log('✅ Serveur de chat disponible');
            return true;
        } else {
            console.log('⚠️ Serveur de chat non disponible');
            return false;
        }
    } catch (error) {
        console.log('❌ Erreur connexion serveur chat:', error.message);
        return false;
    }
}

/**
 * Initialisation au chargement
 */
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 Bouton d\'accès au chat LOUNA initialisé');
    
    // Vérifier le statut du serveur de chat
    checkChatServerStatus().then(available => {
        const button = document.getElementById('chatAccessBtn');
        if (available) {
            button.style.background = 'linear-gradient(135deg, #00d4ff, #0099cc)';
            button.title = 'Interface de chat LOUNA disponible - Cliquez pour ouvrir';
        } else {
            button.style.background = 'linear-gradient(135deg, #ffa500, #ff6b35)';
            button.title = 'Serveur de chat non disponible - Vérifiez que le serveur est démarré';
        }
    });
});

// Vérification périodique du statut (toutes les 30 secondes)
setInterval(() => {
    checkChatServerStatus();
}, 30000);
</script>

<!-- INSTRUCTIONS D'INTÉGRATION DANS ELECTRON -->
<!--
POUR INTÉGRER CE BOUTON DANS VOTRE APPLICATION ELECTRON :

1. MÉTHODE 1 - Bouton flottant (recommandée) :
   - Copiez le CSS et le JavaScript dans votre page principale
   - Le bouton apparaîtra en bas à droite de toute page

2. MÉTHODE 2 - Bouton dans le header :
   - Utilisez la classe "electron-header-button" 
   - Ajoutez dans votre barre de navigation :
   
   <button class="electron-header-button" onclick="openChatInterface()">
       <span class="icon">💬</span>
       <span>Chat LOUNA</span>
   </button>

3. MÉTHODE 3 - Menu Electron :
   - Ajoutez dans votre menu principal :
   
   {
       label: 'Chat LOUNA',
       click: () => {
           // Code pour ouvrir l'interface de chat
           require('electron').shell.openExternal('http://localhost:3000');
       }
   }

4. CONFIGURATION :
   - Ajustez l'URL dans openChatInterface() selon votre configuration
   - Modifiez les styles selon votre thème
   - Personnalisez les notifications selon vos besoins

5. DÉPENDANCES :
   - Assurez-vous que le serveur de chat fonctionne sur le port 3000
   - Vérifiez que l'agent DeepSeek R1 8B est opérationnel
   - Testez la mémoire thermique avant l'intégration
-->

</body>
</html>
