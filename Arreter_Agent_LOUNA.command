#!/bin/bash

# Script d'arrêt de l'Agent LOUNA DeepSeek R1 8B
# <PERSON><PERSON><PERSON> pour <PERSON> 97180

echo "🛑 === ARRÊT AGENT LOUNA DEEPSEEK R1 8B ==="
echo ""

# Arrêter le serveur Node.js sur le port 3000
echo "🔍 Recherche du serveur Agent LOUNA..."
if lsof -i :3000 > /dev/null 2>&1; then
    echo "🛑 Arrêt du serveur Agent LOUNA..."
    lsof -ti :3000 | xargs kill -9
    sleep 2
    
    if ! lsof -i :3000 > /dev/null 2>&1; then
        echo "✅ Serveur Agent LOUNA arrêté avec succès"
    else
        echo "⚠️  Le serveur résiste, force brute..."
        pkill -f "chat-interface-server.js"
        sleep 1
        echo "✅ Serveur forcé à l'arrêt"
    fi
else
    echo "ℹ️  Aucun serveur Agent LOUNA en cours d'exécution"
fi

# Optionnel : arrêter Ollama si souhaité
read -p "🤔 Voulez-vous aussi arrêter <PERSON> ? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "🛑 Arrêt d'Ollama..."
    pkill -f "ollama"
    echo "✅ Ollama arrêté"
else
    echo "ℹ️  Ollama reste en cours d'exécution"
fi

echo ""
echo "🎯 === AGENT LOUNA ARRÊTÉ ==="
echo "💡 Pour redémarrer, double-cliquez sur 'Lancer_Agent_LOUNA.command'"
echo ""

# Garder la fenêtre ouverte quelques secondes
sleep 3
