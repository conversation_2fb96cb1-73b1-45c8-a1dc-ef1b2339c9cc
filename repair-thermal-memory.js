/**
 * 🔧 RÉPARATION MÉMOIRE THERMIQUE
 * 
 * Script pour réparer le fichier JSON corrompu de la mémoire thermique
 */

const fs = require('fs').promises;
const path = require('path');

async function repairThermalMemory() {
    console.log('🔧 === RÉPARATION MÉMOIRE THERMIQUE ===\n');
    
    const memoryPath = path.join(__dirname, 'thermal_memory_persistent.json');
    const backupPath = path.join(__dirname, 'thermal_memory_backup.json');
    
    try {
        // 1. Créer une sauvegarde
        console.log('📦 Création sauvegarde...');
        try {
            const originalData = await fs.readFile(memoryPath, 'utf8');
            await fs.writeFile(backupPath, originalData);
            console.log('✅ Sauvegarde créée: thermal_memory_backup.json');
        } catch (error) {
            console.log('⚠️ Impossible de créer la sauvegarde:', error.message);
        }
        
        // 2. Lire et analyser le fichier corrompu
        console.log('\n🔍 Analyse du fichier corrompu...');
        let rawData;
        try {
            rawData = await fs.readFile(memoryPath, 'utf8');
            console.log(`📊 Taille du fichier: ${rawData.length} caractères`);
        } catch (error) {
            console.error('❌ Impossible de lire le fichier:', error.message);
            return false;
        }
        
        // 3. Tenter de parser le JSON
        let memoryData;
        try {
            memoryData = JSON.parse(rawData);
            console.log('✅ JSON valide - Aucune réparation nécessaire');
            return true;
        } catch (parseError) {
            console.log('❌ JSON corrompu détecté:', parseError.message);
            
            // 4. Tenter de réparer le JSON
            console.log('\n🔧 Tentative de réparation...');
            
            // Méthode 1: Trouver la dernière accolade fermante valide
            let repairedData = rawData;
            
            // Supprimer les caractères après la dernière accolade fermante
            const lastBraceIndex = rawData.lastIndexOf('}');
            if (lastBraceIndex !== -1) {
                repairedData = rawData.substring(0, lastBraceIndex + 1);
                console.log(`🔧 Troncature à la position ${lastBraceIndex + 1}`);
            }
            
            // Tenter de parser la version réparée
            try {
                memoryData = JSON.parse(repairedData);
                console.log('✅ Réparation réussie par troncature');
            } catch (repairError) {
                console.log('❌ Réparation par troncature échouée');
                
                // Méthode 2: Créer une nouvelle structure de base
                console.log('🔧 Création d\'une nouvelle structure...');
                memoryData = createDefaultMemoryStructure();
                console.log('✅ Nouvelle structure créée');
            }
        }
        
        // 5. Valider et nettoyer la structure
        console.log('\n🧹 Validation et nettoyage...');
        memoryData = validateAndCleanMemoryStructure(memoryData);
        
        // 6. Sauvegarder la version réparée
        console.log('\n💾 Sauvegarde de la version réparée...');
        await fs.writeFile(memoryPath, JSON.stringify(memoryData, null, 2));
        console.log('✅ Mémoire thermique réparée et sauvegardée');
        
        // 7. Statistiques finales
        console.log('\n📊 === STATISTIQUES FINALES ===');
        console.log(`   Zones de mémoire: ${Object.keys(memoryData.thermal_zones || {}).length}`);
        
        let totalEntries = 0;
        if (memoryData.thermal_zones) {
            for (const zone of Object.values(memoryData.thermal_zones)) {
                if (zone.entries) {
                    totalEntries += zone.entries.length;
                }
            }
        }
        console.log(`   Entrées totales: ${totalEntries}`);
        console.log(`   QI système neural: ${memoryData.neural_system?.qi_level || 'Non défini'}`);
        console.log(`   Dernière mise à jour: ${new Date(memoryData.neural_system?.qi_last_update || Date.now()).toLocaleString()}`);
        
        console.log('\n🎉 RÉPARATION TERMINÉE AVEC SUCCÈS !');
        return true;
        
    } catch (error) {
        console.error('❌ Erreur fatale lors de la réparation:', error.message);
        console.error(error.stack);
        return false;
    }
}

function createDefaultMemoryStructure() {
    return {
        thermal_zones: {
            cognitive_zone: {
                temperature: 37.2,
                entries: []
            },
            emotional_zone: {
                temperature: 36.8,
                entries: []
            },
            procedural_zone: {
                temperature: 37.0,
                entries: []
            },
            episodic_zone: {
                temperature: 37.1,
                entries: []
            },
            semantic_zone: {
                temperature: 36.9,
                entries: []
            },
            meta: {
                temperature: 37.0,
                entries: []
            }
        },
        neural_system: {
            qi_level: 201,
            qi_components: {
                baseAgent: 120,
                thermalMemory: 36,
                cognitiveBoost: 35,
                experience: 0,
                neurogenesis: 0,
                formations: 0,
                kyberBoost: 10
            },
            qi_classification: "GÉNIE EXCEPTIONNEL",
            qi_methodology: "UNIFIED_QI_SYSTEM_V1",
            qi_last_update: Date.now()
        },
        metadata: {
            created: Date.now(),
            last_repair: Date.now(),
            version: "1.0.0",
            total_entries: 0
        }
    };
}

function validateAndCleanMemoryStructure(data) {
    // Assurer la structure de base
    if (!data.thermal_zones) data.thermal_zones = {};
    if (!data.neural_system) data.neural_system = {};
    if (!data.metadata) data.metadata = {};
    
    // Valider les zones thermiques
    const requiredZones = ['cognitive_zone', 'emotional_zone', 'procedural_zone', 'episodic_zone', 'semantic_zone', 'meta'];
    
    for (const zoneName of requiredZones) {
        if (!data.thermal_zones[zoneName]) {
            data.thermal_zones[zoneName] = {
                temperature: 37.0,
                entries: []
            };
        }
        
        // Assurer que chaque zone a des entrées valides
        if (!Array.isArray(data.thermal_zones[zoneName].entries)) {
            data.thermal_zones[zoneName].entries = [];
        }
        
        // Nettoyer les entrées corrompues
        data.thermal_zones[zoneName].entries = data.thermal_zones[zoneName].entries.filter(entry => {
            return entry && typeof entry === 'object' && entry.content;
        });
    }
    
    // Valider le système neural
    if (!data.neural_system.qi_level) {
        data.neural_system.qi_level = 201;
    }
    
    if (!data.neural_system.qi_methodology) {
        data.neural_system.qi_methodology = "UNIFIED_QI_SYSTEM_V1";
    }
    
    data.neural_system.qi_last_update = Date.now();
    
    // Mettre à jour les métadonnées
    data.metadata.last_repair = Date.now();
    if (!data.metadata.created) {
        data.metadata.created = Date.now();
    }
    
    return data;
}

// Exécuter la réparation si appelé directement
if (require.main === module) {
    repairThermalMemory()
        .then(success => {
            process.exit(success ? 0 : 1);
        })
        .catch(error => {
            console.error('❌ Erreur fatale:', error.message);
            process.exit(1);
        });
}

module.exports = { repairThermalMemory };
