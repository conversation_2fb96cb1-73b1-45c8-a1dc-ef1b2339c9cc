#!/usr/bin/env node

/**
 * 🌱 MODULE NEUROGENÈSE RÉALISTE
 * 
 * Système de création de neurones basé sur la biologie humaine :
 * - 700 neurones/jour comme un humain adulte
 * - Synchronisé avec la température thermique
 * - Création réelle de neurones dans la mémoire
 */

const fs = require('fs');

class NeurogenesesRealiste {
    constructor(thermalMemoryPath = 'thermal_memory_persistent.json') {
        this.thermalMemoryPath = thermalMemoryPath;
        this.thermalMemory = null;
        
        // Paramètres neurogenèse RÉALISTES
        this.neurogenesisConfig = {
            humanRatePerDay: 700,           // Neurones/jour chez l'humain adulte
            humanRatePerSecond: 700 / 86400, // 0.008 neurones/seconde
            minTempBonus: 0.5,              // Bonus minimum (température froide)
            maxTempBonus: 2.0,              // Bonus maximum (température optimale)
            optimalTemp: 37.0,              // Température optimale pour neurogenèse
            tempRange: 3.0                  // Plage de température efficace
        };
        
        // État neurogenèse
        this.neurogenesisState = {
            lastUpdate: Date.now(),
            totalNeuronsCreated: 0,
            currentRate: 0,
            temperatureBonus: 1.0,
            neuronsToCreate: 0
        };
        
        console.log('🌱 Module neurogenèse réaliste initialisé');
        this.loadThermalMemory();
    }
    
    /**
     * Charge la mémoire thermique
     */
    loadThermalMemory() {
        try {
            this.thermalMemory = JSON.parse(fs.readFileSync(this.thermalMemoryPath, 'utf8'));
            console.log('✅ Mémoire thermique chargée pour neurogenèse');
        } catch (error) {
            console.error('❌ Erreur chargement mémoire:', error.message);
        }
    }
    
    /**
     * Calcule la température moyenne des zones thermiques
     */
    calculateAverageTemperature() {
        if (!this.thermalMemory?.thermal_zones) return 37.0;
        
        let totalTemp = 0;
        let zoneCount = 0;
        
        for (const zone of Object.values(this.thermalMemory.thermal_zones)) {
            if (zone.temperature) {
                totalTemp += zone.temperature;
                zoneCount++;
            }
        }
        
        return zoneCount > 0 ? totalTemp / zoneCount : 37.0;
    }
    
    /**
     * Calcule le bonus de neurogenèse basé sur la température
     */
    calculateTemperatureBonus(avgTemp) {
        const { optimalTemp, tempRange, minTempBonus, maxTempBonus } = this.neurogenesisConfig;
        
        // Distance de la température optimale
        const tempDistance = Math.abs(avgTemp - optimalTemp);
        
        // Bonus inversement proportionnel à la distance
        if (tempDistance <= tempRange) {
            const efficiency = 1.0 - (tempDistance / tempRange);
            return minTempBonus + (efficiency * (maxTempBonus - minTempBonus));
        }
        
        return minTempBonus;
    }
    
    /**
     * Met à jour la neurogenèse basée sur la température
     */
    updateNeurogenesis() {
        const now = Date.now();
        const deltaTime = (now - this.neurogenesisState.lastUpdate) / 1000; // en secondes
        
        // Calculer la température moyenne
        const avgTemp = this.calculateAverageTemperature();
        
        // Calculer le bonus de température
        const tempBonus = this.calculateTemperatureBonus(avgTemp);
        
        // Taux de neurogenèse ajusté
        const adjustedRate = this.neurogenesisConfig.humanRatePerSecond * tempBonus;
        
        // Neurones à créer basés sur le temps écoulé
        const neuronsToCreate = Math.floor(adjustedRate * deltaTime);
        
        // Mettre à jour l'état
        this.neurogenesisState.lastUpdate = now;
        this.neurogenesisState.currentRate = adjustedRate;
        this.neurogenesisState.temperatureBonus = tempBonus;
        this.neurogenesisState.neuronsToCreate = neuronsToCreate;
        
        // Créer les neurones si nécessaire
        if (neuronsToCreate > 0) {
            this.createNewNeurons(neuronsToCreate, avgTemp);
        }
        
        return {
            neuronsCreated: neuronsToCreate,
            currentRate: adjustedRate,
            temperatureBonus: tempBonus,
            averageTemperature: avgTemp
        };
    }
    
    /**
     * Crée de nouveaux neurones dans la mémoire thermique
     */
    createNewNeurons(count, temperature) {
        if (!this.thermalMemory?.neural_system?.neuron_storage) {
            console.log('⚠️ Stockage neurones non trouvé');
            return;
        }
        
        const storage = this.thermalMemory.neural_system.neuron_storage;
        const now = Date.now();
        
        // Créer les nouveaux neurones
        for (let i = 0; i < count; i++) {
            const neuronId = `neurogenesis_${now}_${i}`;
            
            const newNeuron = {
                id: neuronId,
                type: 'neurogenesis_created',
                created_at: now,
                birth_temperature: temperature,
                state: 'standby',
                connections: [],
                activation_level: 0.1,
                efficiency: 0.7 + Math.random() * 0.3,
                age: 0,
                location: this.selectNeuronLocation(),
                specialization: this.selectNeuronSpecialization()
            };
            
            // Ajouter au stockage
            storage.neurons.push(newNeuron);
        }
        
        // Mettre à jour les compteurs
        const neural = this.thermalMemory.neural_system;
        neural.total_neurons = (neural.total_neurons || 0) + count;
        neural.standby_neurons = (neural.standby_neurons || 0) + Math.floor(count * 0.85);
        neural.active_neurons = (neural.active_neurons || 0) + Math.floor(count * 0.1);
        neural.hibernating_neurons = (neural.hibernating_neurons || 0) + Math.floor(count * 0.05);
        
        // Mettre à jour les statistiques
        neural.last_neurogenesis = now;
        neural.neurogenesis_rate = this.neurogenesisState.currentRate;
        
        this.neurogenesisState.totalNeuronsCreated += count;
        
        console.log(`🌱 ${count} nouveaux neurones créés (total: ${neural.total_neurons})`);
        
        // Sauvegarder la mémoire mise à jour
        this.saveThermalMemory();
    }
    
    /**
     * Sélectionne l'emplacement du neurone
     */
    selectNeuronLocation() {
        const locations = [
            'hippocampus',      // Mémoire
            'prefrontal_cortex', // Raisonnement
            'temporal_lobe',    // Langage
            'parietal_lobe',    // Intégration
            'occipital_lobe',   // Traitement visuel
            'cerebellum',       // Coordination
            'brainstem'         // Fonctions vitales
        ];
        
        return locations[Math.floor(Math.random() * locations.length)];
    }
    
    /**
     * Sélectionne la spécialisation du neurone
     */
    selectNeuronSpecialization() {
        const specializations = [
            'memory_encoding',
            'pattern_recognition',
            'logical_reasoning',
            'creative_thinking',
            'emotional_processing',
            'sensory_integration',
            'motor_control',
            'language_processing'
        ];
        
        return specializations[Math.floor(Math.random() * specializations.length)];
    }
    
    /**
     * Sauvegarde la mémoire thermique
     */
    saveThermalMemory() {
        try {
            fs.writeFileSync(this.thermalMemoryPath, JSON.stringify(this.thermalMemory, null, 2));
        } catch (error) {
            console.error('❌ Erreur sauvegarde mémoire:', error.message);
        }
    }
    
    /**
     * Démarre la neurogenèse automatique
     */
    startAutomaticNeurogenesis(intervalMs = 60000) { // Toutes les minutes
        console.log(`🌱 Démarrage neurogenèse automatique (${intervalMs}ms)`);
        
        setInterval(() => {
            const result = this.updateNeurogenesis();
            
            if (result.neuronsCreated > 0) {
                console.log(`🌱 Neurogenèse: ${result.neuronsCreated} neurones créés`);
                console.log(`   📊 Taux: ${result.currentRate.toFixed(6)}/s`);
                console.log(`   🌡️ Température: ${result.averageTemperature.toFixed(2)}°C`);
                console.log(`   ⚡ Bonus: ${result.temperatureBonus.toFixed(2)}x`);
            }
        }, intervalMs);
    }
    
    /**
     * Retourne les statistiques de neurogenèse
     */
    getStats() {
        return {
            totalNeuronsCreated: this.neurogenesisState.totalNeuronsCreated,
            currentRate: this.neurogenesisState.currentRate,
            temperatureBonus: this.neurogenesisState.temperatureBonus,
            averageTemperature: this.calculateAverageTemperature(),
            totalNeurons: this.thermalMemory?.neural_system?.total_neurons || 0,
            activeNeurons: this.thermalMemory?.neural_system?.active_neurons || 0,
            standbyNeurons: this.thermalMemory?.neural_system?.standby_neurons || 0,
            hibernatingNeurons: this.thermalMemory?.neural_system?.hibernating_neurons || 0
        };
    }
}

// Test si exécuté directement
if (require.main === module) {
    const neurogenesis = new NeurogenesesRealiste();
    
    // Test de neurogenèse
    console.log('🧪 Test neurogenèse...');
    const result = neurogenesis.updateNeurogenesis();
    console.log('📊 Résultat:', result);
    
    // Démarrer neurogenèse automatique
    neurogenesis.startAutomaticNeurogenesis(30000); // Toutes les 30 secondes pour test
}

module.exports = NeurogenesesRealiste;
