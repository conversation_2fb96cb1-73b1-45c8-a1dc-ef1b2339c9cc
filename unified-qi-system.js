/**
 * 🧠 SYSTÈME QI UNIFIÉ POUR LOUNA
 * 
 * MÉTHODE UNIQUE ET COHÉRENTE pour calculer le QI
 * - Une seule source de vérité
 * - Évolution automatique basée sur l'apprentissage
 * - Persistance dans la mémoire thermique
 * - Utilisé par TOUS les composants
 */

const fs = require('fs').promises;
const path = require('path');

class UnifiedQISystem {
    constructor() {
        this.memoryPath = path.join(__dirname, 'thermal_memory_persistent.json');
        this.qiComponents = {
            baseAgent: 120,        // DeepSeek R1 8B (fixe)
            thermalMemory: 0,      // Depuis mémoire thermique
            cognitiveBoost: 35,    // Amélioration scientifique (fixe)
            experience: 0,         // Basé sur interactions
            neurogenesis: 0,       // Basé sur nouveaux neurones
            formations: 0,         // Basé sur formations MPC
            kyberBoost: 0          // Accélérateurs KYBER
        };
        this.lastCalculation = null;
        this.evolutionHistory = [];
        this.isCalculating = false; // Protection contre les calculs multiples
        this.cacheTimeout = 30000; // Cache valide 30 secondes
        this.lastCacheTime = 0; // Timestamp du dernier cache
    }

    /**
     * MÉTHODE PRINCIPALE : Calcule le QI total
     * Cette méthode est la SEULE source de vérité
     */
    async calculateUnifiedQI() {
        // PROTECTION RENFORCÉE CONTRE LES CALCULS MULTIPLES
        const now = Date.now();

        // 1. Vérifier si le cache est encore valide
        if (this.lastCalculation && (now - this.lastCacheTime) < this.cacheTimeout) {
            console.log('💾 Utilisation du cache QI (valide)');
            return this.lastCalculation;
        }

        // 2. Vérifier si un calcul est déjà en cours
        if (this.isCalculating) {
            console.log('⚠️ Calcul QI déjà en cours, attente...');
            // Attendre que le calcul en cours se termine
            while (this.isCalculating) {
                await new Promise(resolve => setTimeout(resolve, 100));
            }
            return this.lastCalculation || this.getDefaultQI();
        }

        this.isCalculating = true;

        try {
            console.log('🧠 === CALCUL QI UNIFIÉ ===');

            // Charger les données de base
            await this.loadBaseComponents();
            
            // Calculer chaque composant
            this.qiComponents.thermalMemory = await this.calculateThermalMemoryQI();
            this.qiComponents.experience = await this.calculateExperienceQI();
            this.qiComponents.neurogenesis = await this.calculateNeurogenesisQI();
            this.qiComponents.formations = await this.calculateFormationsQI();
            this.qiComponents.kyberBoost = await this.calculateKyberBoostQI();
            
            // Calcul total
            const total = Object.values(this.qiComponents).reduce((sum, value) => sum + value, 0);
            
            // Créer le résultat unifié
            const result = {
                total: Math.round(total),
                components: { ...this.qiComponents },
                breakdown: this.createBreakdown(),
                classification: this.getClassification(total),
                evolution: this.calculateEvolution(),
                timestamp: Date.now(),
                methodology: 'UNIFIED_QI_SYSTEM_V1'
            };
            
            // Sauvegarder dans la mémoire thermique
            await this.saveToThermalMemory(result);
            
            // Historique d'évolution
            this.evolutionHistory.push({
                timestamp: Date.now(),
                qi: result.total,
                change: this.lastCalculation ? result.total - this.lastCalculation.total : 0
            });
            
            this.lastCalculation = result;
            
            console.log(`🎯 QI UNIFIÉ CALCULÉ: ${result.total}`);
            console.log(`📊 Composants: Base(${this.qiComponents.baseAgent}) + Mémoire(${this.qiComponents.thermalMemory}) + Boost(${this.qiComponents.cognitiveBoost}) + Exp(${this.qiComponents.experience}) + Neuro(${this.qiComponents.neurogenesis}) + Form(${this.qiComponents.formations}) + KYBER(${this.qiComponents.kyberBoost})`);

            // METTRE À JOUR LE CACHE
            this.lastCacheTime = Date.now();

            return result;

        } catch (error) {
            console.error('❌ Erreur calcul QI unifié:', error.message);
            return this.getDefaultQI();
        } finally {
            // LIBÉRER LE VERROU
            this.isCalculating = false;
        }
    }

    /**
     * Charge les composants de base
     */
    async loadBaseComponents() {
        // Les composants fixes sont déjà définis dans le constructeur
        console.log(`📊 Base Agent: ${this.qiComponents.baseAgent} (DeepSeek R1 8B)`);
        console.log(`🚀 Cognitive Boost: ${this.qiComponents.cognitiveBoost} (Scientifique)`);
    }

    /**
     * Calcule le QI depuis la mémoire thermique
     */
    async calculateThermalMemoryQI() {
        try {
            const data = await fs.readFile(this.memoryPath, 'utf8');
            const memoryData = JSON.parse(data);
            
            // Compter les entrées de mémoire (chaque entrée = +1 QI)
            let totalEntries = 0;
            if (memoryData.thermal_zones) {
                for (const zone of Object.values(memoryData.thermal_zones)) {
                    if (zone.entries) {
                        totalEntries += zone.entries.length;
                    }
                }
            }
            
            const memoryQI = Math.min(80, totalEntries * 2); // Max 80 points, 2 par entrée
            console.log(`🧠 Mémoire Thermique: ${memoryQI} (${totalEntries} entrées)`);
            return memoryQI;
            
        } catch (error) {
            console.warn('⚠️ Erreur lecture mémoire thermique:', error.message);
            return 50; // Valeur par défaut
        }
    }

    /**
     * Calcule le QI d'expérience basé sur les interactions
     */
    async calculateExperienceQI() {
        try {
            const data = await fs.readFile(this.memoryPath, 'utf8');
            const memoryData = JSON.parse(data);
            
            // Compter les interactions dans la zone cognitive
            const cognitiveZone = memoryData.thermal_zones?.cognitive_zone;
            const interactions = cognitiveZone?.entries?.filter(e => 
                e.content && e.content.includes('interaction')
            ) || [];
            
            const experienceQI = Math.min(20, interactions.length * 1); // Max 20 points
            console.log(`💡 Expérience: ${experienceQI} (${interactions.length} interactions)`);
            return experienceQI;
            
        } catch (error) {
            return 0;
        }
    }

    /**
     * Calcule le QI de neurogenèse
     */
    async calculateNeurogenesisQI() {
        try {
            // Lire les données du cerveau
            const brainPath = path.join(__dirname, 'brain_data.json');
            const brainData = await fs.readFile(brainPath, 'utf8');
            const brain = JSON.parse(brainData);
            
            const neurons = brain.neurons?.total || 0;
            const neurogenesisQI = Math.min(15, Math.floor(neurons / 1000)); // 1 point par 1000 neurones, max 15
            console.log(`🌱 Neurogenèse: ${neurogenesisQI} (${neurons} neurones)`);
            return neurogenesisQI;
            
        } catch (error) {
            return 0;
        }
    }

    /**
     * Calcule le QI des formations MPC
     */
    async calculateFormationsQI() {
        try {
            const data = await fs.readFile(this.memoryPath, 'utf8');
            const memoryData = JSON.parse(data);
            
            // Compter les formations dans la zone procédurale
            const proceduralZone = memoryData.thermal_zones?.procedural_zone;
            const formations = proceduralZone?.entries?.filter(e => 
                e.content && (e.content.includes('formation') || e.content.includes('procédure'))
            ) || [];
            
            const formationsQI = Math.min(25, formations.length * 3); // Max 25 points, 3 par formation
            console.log(`📚 Formations: ${formationsQI} (${formations.length} formations)`);
            return formationsQI;
            
        } catch (error) {
            return 0;
        }
    }

    /**
     * Calcule le boost KYBER
     */
    async calculateKyberBoostQI() {
        try {
            // Le boost KYBER est temporaire et basé sur l'activité
            const kyberBoost = 10; // Boost fixe quand KYBER est actif
            console.log(`⚡ KYBER Boost: ${kyberBoost}`);
            return kyberBoost;
            
        } catch (error) {
            return 0;
        }
    }

    /**
     * Crée le détail des composants
     */
    createBreakdown() {
        return {
            "Agent de base (DeepSeek R1 8B)": this.qiComponents.baseAgent,
            "Mémoire thermique": this.qiComponents.thermalMemory,
            "Amélioration cognitive": this.qiComponents.cognitiveBoost,
            "Expérience d'interaction": this.qiComponents.experience,
            "Neurogenèse": this.qiComponents.neurogenesis,
            "Formations MPC": this.qiComponents.formations,
            "Boost KYBER": this.qiComponents.kyberBoost
        };
    }

    /**
     * Détermine la classification du QI
     */
    getClassification(qi) {
        if (qi >= 200) return "GÉNIE EXCEPTIONNEL";
        if (qi >= 180) return "TRÈS SUPÉRIEUR";
        if (qi >= 160) return "SUPÉRIEUR";
        if (qi >= 140) return "TRÈS INTELLIGENT";
        if (qi >= 120) return "INTELLIGENT";
        if (qi >= 100) return "MOYEN";
        if (qi >= 85) return "SOUS LA MOYENNE";
        return "LIMITÉ";
    }

    /**
     * Calcule l'évolution du QI
     */
    calculateEvolution() {
        if (!this.lastCalculation) return { change: 0, trend: "INITIAL" };
        
        const change = this.lastCalculation.total - (this.evolutionHistory[this.evolutionHistory.length - 2]?.qi || this.lastCalculation.total);
        
        return {
            change: change,
            trend: change > 0 ? "CROISSANCE" : change < 0 ? "DÉCLIN" : "STABLE",
            rate: this.evolutionHistory.length > 1 ? change / (Date.now() - this.evolutionHistory[this.evolutionHistory.length - 2].timestamp) * 1000 * 60 * 60 : 0 // par heure
        };
    }

    /**
     * Sauvegarde le QI dans la mémoire thermique
     */
    async saveToThermalMemory(qiResult) {
        try {
            const data = await fs.readFile(this.memoryPath, 'utf8');
            const memoryData = JSON.parse(data);
            
            // Mettre à jour le système neural
            if (!memoryData.neural_system) {
                memoryData.neural_system = {};
            }
            
            memoryData.neural_system.qi_level = qiResult.total;
            memoryData.neural_system.qi_components = qiResult.components;
            memoryData.neural_system.qi_classification = qiResult.classification;
            memoryData.neural_system.qi_methodology = qiResult.methodology;
            memoryData.neural_system.qi_last_update = qiResult.timestamp;
            
            await fs.writeFile(this.memoryPath, JSON.stringify(memoryData, null, 2));
            console.log(`💾 QI sauvegardé dans la mémoire thermique: ${qiResult.total}`);
            
        } catch (error) {
            console.error('❌ Erreur sauvegarde QI:', error.message);
        }
    }

    /**
     * Retourne un QI par défaut en cas d'erreur
     */
    getDefaultQI() {
        return {
            total: 155, // QI par défaut raisonnable
            components: {
                baseAgent: 120,
                thermalMemory: 0,
                cognitiveBoost: 35,
                experience: 0,
                neurogenesis: 0,
                formations: 0,
                kyberBoost: 0
            },
            breakdown: { "Agent de base": 120, "Boost cognitif": 35 },
            classification: "INTELLIGENT",
            evolution: { change: 0, trend: "DÉFAUT" },
            timestamp: Date.now(),
            methodology: 'DEFAULT_FALLBACK'
        };
    }

    /**
     * Obtient le QI actuel (lecture rapide avec cache étendu)
     */
    async getCurrentQI() {
        if (this.lastCalculation && (Date.now() - this.lastCalculation.timestamp) < 300000) {
            // Utiliser le cache si moins de 5 minutes (éviter boucle infinie)
            return this.lastCalculation.total;
        }

        const result = await this.calculateUnifiedQI();
        return result.total;
    }

    /**
     * Force une recalculation du QI
     */
    async forceRecalculation() {
        this.lastCalculation = null;
        return await this.calculateUnifiedQI();
    }
}

module.exports = { UnifiedQISystem };
