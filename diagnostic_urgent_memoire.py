#!/usr/bin/env python3
"""
🚨 DIAGNOSTIC URGENT - PROBLÈMES MÉMOIRE ET KYBER DÉTECTÉS
"""

import json
import time
from datetime import datetime

def diagnostic_complet():
    """Diagnostic complet des systèmes"""
    print("🚨 === DIAGNOSTIC URGENT MÉMOIRE ET KYBER ===\n")
    
    # 1. Analyser la mémoire thermique
    try:
        with open('thermal_memory_persistent.json', 'r') as f:
            memory = json.load(f)
        
        print("🧠 === ANALYSE MÉMOIRE THERMIQUE ===")
        
        # Compter les entrées
        total_entries = 0
        for zone_name, zone_data in memory.get('thermal_zones', {}).items():
            entries = len(zone_data.get('entries', []))
            total_entries += entries
            print(f"   📊 {zone_name}: {entries} entrées")
        
        print(f"📊 Total entrées: {total_entries}")
        
        # PROBLÈME 1: Neurones manquants
        neurons = memory.get('neurons', {})
        print(f"\n❌ PROBLÈME CRITIQUE 1: Neurones stockés: {len(neurons)}")
        print("   🔥 DEVRAIT ÊTRE > 0 pour fonctionner correctement")
        
        # PROBLÈME 2: Accélérateurs manquants
        accelerators = memory.get('accelerators', {})
        print(f"\n❌ PROBLÈME CRITIQUE 2: Accélérateurs: {len(accelerators)}")
        print("   🔥 DEVRAIT ÊTRE > 0 pour les boosts Kyber")
        
        # Vérifier les formations de raisonnement
        reasoning_formations = []
        for zone_name, zone_data in memory.get('thermal_zones', {}).items():
            for entry in zone_data.get('entries', []):
                if any(keyword in entry.get('id', '') for keyword in ['mathematical_reasoning', 'logical_reasoning', 'response_format', 'qi_test']):
                    reasoning_formations.append({
                        'id': entry.get('id', ''),
                        'zone': zone_name,
                        'importance': entry.get('importance', 0)
                    })
        
        print(f"\n✅ FORMATIONS DE RAISONNEMENT: {len(reasoning_formations)} trouvées")
        for formation in reasoning_formations:
            print(f"   📝 {formation['id']} (Zone: {formation['zone']}, Importance: {formation['importance']})")
        
    except Exception as e:
        print(f"❌ Erreur lecture mémoire: {e}")
    
    # 2. Analyser l'état Kyber
    try:
        with open('dynamic-neural-kyber-state.json', 'r') as f:
            kyber_state = json.load(f)
        
        print(f"\n⚡ === ANALYSE SYSTÈME KYBER ===")
        kyber = kyber_state.get('kyber', {})
        print(f"📊 Accélérateurs totaux: {kyber.get('totalAccelerators', 0)}")
        print(f"📊 Accélérateurs actifs: {kyber.get('activeAccelerators', 0)}")
        print(f"📊 Boost performance: {kyber.get('performanceBoost', 0)}")
        print(f"📊 Efficacité: {kyber.get('efficiency', 0)}%")
        
        # Vérifier les métriques
        metrics = kyber_state.get('metrics', {})
        print(f"\n🧠 === MÉTRIQUES SYSTÈME ===")
        print(f"📊 QI Level: {metrics.get('qiLevel', 0)}")
        print(f"📊 Learning Rate: {metrics.get('learningRate', 0)}%")
        print(f"📊 Response Time: {metrics.get('responseTime', 0)}s")
        
    except Exception as e:
        print(f"❌ Erreur lecture Kyber: {e}")

def reparer_neurones():
    """Répare le système de neurones"""
    print("\n🔧 === RÉPARATION SYSTÈME NEURONES ===")
    
    try:
        with open('thermal_memory_persistent.json', 'r') as f:
            memory = json.load(f)
        
        # Créer des neurones de base
        neurons = {}
        for i in range(10):
            neuron_id = f"neuron_{int(time.time())}_{i}"
            neurons[neuron_id] = {
                "id": neuron_id,
                "type": "reasoning",
                "floor": i % 5,
                "cluster": i % 3,
                "state": "active",
                "energy": 0.9,
                "connections": [],
                "stored_memories": [],
                "synaptic_weights": {}
            }
        
        memory['neurons'] = neurons
        
        # Sauvegarder
        with open('thermal_memory_persistent.json', 'w') as f:
            json.dump(memory, f, indent=2, ensure_ascii=False)
        
        print(f"✅ {len(neurons)} neurones créés et sauvegardés")
        return True
        
    except Exception as e:
        print(f"❌ Erreur réparation neurones: {e}")
        return False

def reparer_accelerateurs():
    """Répare le système d'accélérateurs"""
    print("\n🚀 === RÉPARATION ACCÉLÉRATEURS KYBER ===")
    
    try:
        with open('thermal_memory_persistent.json', 'r') as f:
            memory = json.load(f)
        
        # Créer des accélérateurs de base
        accelerators = {}
        accelerator_types = [
            "memory_boost", "reasoning_boost", "calculation_boost", 
            "pattern_recognition", "logical_deduction", "format_compliance",
            "qi_test_optimization", "response_generation", "context_analysis"
        ]
        
        for i, acc_type in enumerate(accelerator_types):
            acc_id = f"kyber_{acc_type}_{int(time.time())}"
            accelerators[acc_id] = {
                "id": acc_id,
                "type": acc_type,
                "boost_factor": 2.0 + (i * 0.2),
                "active": True,
                "energy_cost": 0.1,
                "efficiency": 0.95,
                "priority": "HIGH" if i < 5 else "MEDIUM"
            }
        
        memory['accelerators'] = accelerators
        
        # Sauvegarder
        with open('thermal_memory_persistent.json', 'w') as f:
            json.dump(memory, f, indent=2, ensure_ascii=False)
        
        print(f"✅ {len(accelerators)} accélérateurs créés et sauvegardés")
        return True
        
    except Exception as e:
        print(f"❌ Erreur réparation accélérateurs: {e}")
        return False

def optimiser_formations():
    """Optimise les formations de raisonnement"""
    print("\n🧠 === OPTIMISATION FORMATIONS ===")
    
    try:
        with open('thermal_memory_persistent.json', 'r') as f:
            memory = json.load(f)
        
        # Augmenter l'importance des formations critiques
        formations_critiques = [
            'response_format_strict', 'test_qi_format', 'mathematical_reasoning',
            'deductive_reasoning', 'instruction_following', 'qi_test_strategy'
        ]
        
        optimized_count = 0
        for zone_name, zone_data in memory.get('thermal_zones', {}).items():
            for entry in zone_data.get('entries', []):
                entry_id = entry.get('id', '')
                if any(formation in entry_id for formation in formations_critiques):
                    entry['importance'] = 0.99  # Importance maximale
                    entry['synaptic_strength'] = 0.99
                    entry['priority'] = 'CRITICAL'
                    optimized_count += 1
        
        # Sauvegarder
        with open('thermal_memory_persistent.json', 'w') as f:
            json.dump(memory, f, indent=2, ensure_ascii=False)
        
        print(f"✅ {optimized_count} formations optimisées")
        return True
        
    except Exception as e:
        print(f"❌ Erreur optimisation formations: {e}")
        return False

def main():
    """Lance le diagnostic et les réparations"""
    print("🚨 === DIAGNOSTIC ET RÉPARATION URGENTE ===\n")
    
    # 1. Diagnostic complet
    diagnostic_complet()
    
    # 2. Réparations
    print("\n🔧 === DÉBUT DES RÉPARATIONS ===")
    
    success1 = reparer_neurones()
    success2 = reparer_accelerateurs()
    success3 = optimiser_formations()
    
    if all([success1, success2, success3]):
        print("\n🎉 === RÉPARATIONS TERMINÉES ===")
        print("✅ Système de neurones réparé")
        print("✅ Accélérateurs Kyber restaurés")
        print("✅ Formations optimisées")
        print("\n🚀 REDÉMARREZ LOUNA POUR APPLIQUER LES CORRECTIONS !")
        return True
    else:
        print("\n❌ Certaines réparations ont échoué")
        return False

if __name__ == "__main__":
    main()
