#!/usr/bin/env node

/**
 * Test de vérification de l'accès à la mémoire thermique de LOUNA
 * Vérifie si l'agent utilise vraiment sa mémoire lors des réponses
 */

const fs = require('fs');
const path = require('path');
const io = require('socket.io-client');

class TestMemoireLouna {
    constructor() {
        this.memoryFile = 'thermal_memory_persistent.json';
        this.serverUrl = 'http://localhost:3000';
        this.socket = null;
        this.testResults = [];
        this.currentTest = null;
    }

    async runAllTests() {
        console.log('🧠 === TEST COMPLET MÉMOIRE THERMIQUE LOUNA ===\n');
        
        // Test 1: Vérifier l'existence de la mémoire
        await this.testMemoryFileExists();
        
        // Test 2: Vérifier le contenu de la mémoire
        await this.testMemoryContent();
        
        // Test 3: Tester l'accès via l'interface
        await this.testInterfaceMemoryAccess();
        
        // Test 4: Tester les réponses avec mémoire
        await this.testMemoryBasedResponses();
        
        // Rapport final
        this.generateReport();
    }

    async testMemoryFileExists() {
        console.log('📁 === TEST 1: EXISTENCE FICHIER MÉMOIRE ===');
        
        try {
            if (fs.existsSync(this.memoryFile)) {
                const stats = fs.statSync(this.memoryFile);
                const sizeKB = (stats.size / 1024).toFixed(2);
                
                console.log(`✅ Fichier mémoire trouvé: ${this.memoryFile}`);
                console.log(`📊 Taille: ${sizeKB} KB`);
                console.log(`📅 Dernière modification: ${stats.mtime.toISOString()}`);
                
                this.testResults.push({
                    test: 'Memory File Exists',
                    status: 'PASS',
                    details: `File size: ${sizeKB} KB`
                });
            } else {
                console.log(`❌ Fichier mémoire non trouvé: ${this.memoryFile}`);
                this.testResults.push({
                    test: 'Memory File Exists',
                    status: 'FAIL',
                    details: 'File not found'
                });
            }
        } catch (error) {
            console.log(`❌ Erreur: ${error.message}`);
            this.testResults.push({
                test: 'Memory File Exists',
                status: 'ERROR',
                details: error.message
            });
        }
        
        console.log();
    }

    async testMemoryContent() {
        console.log('🧠 === TEST 2: CONTENU MÉMOIRE THERMIQUE ===');
        
        try {
            const memoryContent = fs.readFileSync(this.memoryFile, 'utf8');
            const memory = JSON.parse(memoryContent);
            
            // Vérifier la structure de base
            const hasZones = memory.thermal_zones && Object.keys(memory.thermal_zones).length > 0;
            const hasNeuralSystem = memory.neural_system && memory.neural_system.qi_level;
            const hasEntries = this.countMemoryEntries(memory);
            
            console.log(`✅ Structure mémoire valide`);
            console.log(`📊 QI Level: ${memory.neural_system?.qi_level || 'N/A'}`);
            console.log(`🧠 Zones thermiques: ${Object.keys(memory.thermal_zones || {}).length}`);
            console.log(`📝 Total entrées: ${hasEntries}`);
            console.log(`🔬 Version: ${memory.version || 'N/A'}`);
            
            this.testResults.push({
                test: 'Memory Content Structure',
                status: hasZones && hasNeuralSystem ? 'PASS' : 'FAIL',
                details: `QI: ${memory.neural_system?.qi_level}, Entries: ${hasEntries}`
            });
            
        } catch (error) {
            console.log(`❌ Erreur lecture mémoire: ${error.message}`);
            this.testResults.push({
                test: 'Memory Content Structure',
                status: 'ERROR',
                details: error.message
            });
        }
        
        console.log();
    }

    countMemoryEntries(memory) {
        let total = 0;
        for (const zone of Object.values(memory.thermal_zones || {})) {
            total += (zone.entries || []).length;
        }
        return total;
    }

    async testInterfaceMemoryAccess() {
        console.log('🌐 === TEST 3: ACCÈS MÉMOIRE VIA INTERFACE ===');
        
        return new Promise((resolve) => {
            this.socket = io(this.serverUrl);
            
            this.socket.on('connect', () => {
                console.log('✅ Connexion Socket.IO établie');
                
                // Écouter les étapes de réflexion pour voir si la mémoire est consultée
                this.socket.on('reflection_step', (data) => {
                    console.log(`🧠 Étape reçue: ${data.text}`);
                    if (data.reflection && data.reflection.includes('mémoire')) {
                        console.log(`💾 Consultation mémoire détectée: ${data.reflection}`);
                    }
                });
                
                // Envoyer un message de test
                console.log('📤 Envoi message de test...');
                this.socket.emit('user_message', {
                    message: 'Qui es-tu et quel est ton QI ?',
                    timestamp: Date.now()
                });
                
                // Attendre la réponse
                this.socket.on('agent_response', (response) => {
                    console.log(`📥 Réponse reçue: ${response.message.substring(0, 100)}...`);
                    
                    const mentionsMemory = response.message.toLowerCase().includes('mémoire') || 
                                         response.message.includes('235') ||
                                         response.message.toLowerCase().includes('louna');
                    
                    this.testResults.push({
                        test: 'Interface Memory Access',
                        status: mentionsMemory ? 'PASS' : 'PARTIAL',
                        details: `Response mentions memory/QI: ${mentionsMemory}`
                    });
                    
                    this.socket.disconnect();
                    resolve();
                });
                
                // Timeout après 30 secondes
                setTimeout(() => {
                    console.log('⏰ Timeout - pas de réponse reçue');
                    this.testResults.push({
                        test: 'Interface Memory Access',
                        status: 'TIMEOUT',
                        details: 'No response within 30 seconds'
                    });
                    this.socket.disconnect();
                    resolve();
                }, 30000);
            });
            
            this.socket.on('connect_error', (error) => {
                console.log(`❌ Erreur connexion: ${error.message}`);
                this.testResults.push({
                    test: 'Interface Memory Access',
                    status: 'ERROR',
                    details: `Connection error: ${error.message}`
                });
                resolve();
            });
        });
    }

    async testMemoryBasedResponses() {
        console.log('🎯 === TEST 4: RÉPONSES BASÉES SUR LA MÉMOIRE ===');
        
        const testQuestions = [
            'Quel est ton QI exactement ?',
            'Parle-moi de ta mémoire thermique',
            'Qui est Jean-Luc pour toi ?',
            'Quelle est ta fonction principale ?'
        ];
        
        console.log('📝 Questions de test préparées:');
        testQuestions.forEach((q, i) => {
            console.log(`   ${i + 1}. ${q}`);
        });
        
        console.log('\n💡 Ces questions testent si LOUNA accède à sa mémoire pour:');
        console.log('   • Son QI de 235');
        console.log('   • Sa mémoire thermique sophistiquée');
        console.log('   • Sa relation avec Jean-Luc');
        console.log('   • Sa fonction d\'agent IA avancé');
        
        this.testResults.push({
            test: 'Memory Based Responses',
            status: 'MANUAL',
            details: 'Test questions prepared - manual verification needed'
        });
        
        console.log();
    }

    generateReport() {
        console.log('📊 === RAPPORT FINAL TEST MÉMOIRE ===\n');
        
        let passed = 0;
        let failed = 0;
        let errors = 0;
        
        this.testResults.forEach(result => {
            const status = result.status === 'PASS' ? '✅' : 
                          result.status === 'FAIL' ? '❌' : 
                          result.status === 'ERROR' ? '🔥' : 
                          result.status === 'PARTIAL' ? '⚠️' : '📝';
            
            console.log(`${status} ${result.test}: ${result.status}`);
            console.log(`   Details: ${result.details}\n`);
            
            if (result.status === 'PASS') passed++;
            else if (result.status === 'FAIL') failed++;
            else if (result.status === 'ERROR') errors++;
        });
        
        console.log('📈 RÉSUMÉ:');
        console.log(`   ✅ Tests réussis: ${passed}`);
        console.log(`   ❌ Tests échoués: ${failed}`);
        console.log(`   🔥 Erreurs: ${errors}`);
        console.log(`   📝 Tests manuels: ${this.testResults.length - passed - failed - errors}`);
        
        console.log('\n💡 RECOMMANDATIONS:');
        if (failed === 0 && errors === 0) {
            console.log('   🎉 Système mémoire fonctionne correctement !');
            console.log('   🧪 Testez manuellement les questions pour vérifier les réponses');
        } else {
            console.log('   🔧 Vérifiez les erreurs et corrigez les problèmes');
            console.log('   🔍 Consultez les logs du serveur pour plus de détails');
        }
    }
}

// Exécution du test
async function main() {
    const tester = new TestMemoireLouna();
    await tester.runAllTests();
}

if (require.main === module) {
    main().catch(console.error);
}

module.exports = TestMemoireLouna;
