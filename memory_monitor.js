#!/usr/bin/env node

/**
 * 🧠 MONITEUR DE MÉMOIRE THERMIQUE EN TEMPS RÉEL
 * 
 * Surveille et affiche l'état des 6 zones de mémoire thermique :
 * - Procedural (formations, compétences)
 * - Episodic (expériences, conversations)
 * - Semantic (connaissances, faits)
 * - Working (mémoire de travail temporaire)
 * - Emotional (états émotionnels, sentiments)
 * - Meta (métacognition, réflexions)
 */

const fs = require('fs');
const EventEmitter = require('events');

class ThermalMemoryMonitor extends EventEmitter {
    constructor() {
        super();
        this.memoryFile = './thermal_memory_persistent.json';
        this.isMonitoring = false;
        this.lastSnapshot = null;
        this.memoryStats = {
            total_entries: 0,
            zones: {},
            recent_activity: [],
            temperature_avg: 37.0,
            qi_level: 0
        };
    }

    /**
     * Démarre la surveillance en temps réel
     */
    startMonitoring(intervalMs = 1000) {
        if (this.isMonitoring) return;
        
        console.log('🧠 Démarrage du moniteur de mémoire thermique...');
        this.isMonitoring = true;

        // Surveillance continue
        this.monitorInterval = setInterval(() => {
            this.scanMemory();
        }, intervalMs);

        // Première analyse
        this.scanMemory();
    }

    /**
     * Arrête la surveillance
     */
    stopMonitoring() {
        if (!this.isMonitoring) return;
        
        console.log('🧠 Arrêt du moniteur de mémoire thermique...');
        this.isMonitoring = false;
        
        if (this.monitorInterval) {
            clearInterval(this.monitorInterval);
        }
    }

    /**
     * Analyse la mémoire thermique
     */
    scanMemory() {
        try {
            if (!fs.existsSync(this.memoryFile)) {
                this.emit('memory_error', 'Fichier mémoire non trouvé');
                return;
            }

            const memoryData = JSON.parse(fs.readFileSync(this.memoryFile, 'utf8'));
            const currentSnapshot = this.createSnapshot(memoryData);

            // Détecter les changements
            if (this.lastSnapshot) {
                const changes = this.detectChanges(this.lastSnapshot, currentSnapshot);
                if (changes.length > 0) {
                    this.emit('memory_changed', changes);
                }
            }

            // Mettre à jour les statistiques
            this.updateStats(currentSnapshot);
            this.lastSnapshot = currentSnapshot;

            // Émettre l'état actuel
            this.emit('memory_update', this.memoryStats);

        } catch (error) {
            this.emit('memory_error', `Erreur analyse mémoire: ${error.message}`);
        }
    }

    /**
     * Crée un instantané de la mémoire
     */
    createSnapshot(memoryData) {
        const snapshot = {
            timestamp: Date.now(),
            zones: {},
            qi_level: memoryData.neural_system?.qi_level || 0,
            total_entries: 0
        };

        // Analyser chaque zone
        const zoneNames = ['procedural', 'episodic', 'semantic', 'working', 'emotional', 'meta'];
        
        for (const zoneName of zoneNames) {
            const zone = memoryData.thermal_zones?.[zoneName];
            if (zone) {
                snapshot.zones[zoneName] = {
                    temperature: zone.temperature || 37.0,
                    capacity: zone.capacity || 1000,
                    entries_count: zone.entries?.length || 0,
                    entries: zone.entries || [],
                    usage_percent: Math.round(((zone.entries?.length || 0) / (zone.capacity || 1000)) * 100)
                };
                snapshot.total_entries += zone.entries?.length || 0;
            } else {
                snapshot.zones[zoneName] = {
                    temperature: 37.0,
                    capacity: 1000,
                    entries_count: 0,
                    entries: [],
                    usage_percent: 0
                };
            }
        }

        return snapshot;
    }

    /**
     * Détecte les changements entre deux instantanés
     */
    detectChanges(oldSnapshot, newSnapshot) {
        const changes = [];

        // Changements globaux
        if (oldSnapshot.qi_level !== newSnapshot.qi_level) {
            changes.push({
                type: 'qi_change',
                old_value: oldSnapshot.qi_level,
                new_value: newSnapshot.qi_level,
                timestamp: newSnapshot.timestamp
            });
        }

        if (oldSnapshot.total_entries !== newSnapshot.total_entries) {
            changes.push({
                type: 'total_entries_change',
                old_value: oldSnapshot.total_entries,
                new_value: newSnapshot.total_entries,
                difference: newSnapshot.total_entries - oldSnapshot.total_entries,
                timestamp: newSnapshot.timestamp
            });
        }

        // Changements par zone
        for (const zoneName in newSnapshot.zones) {
            const oldZone = oldSnapshot.zones[zoneName];
            const newZone = newSnapshot.zones[zoneName];

            if (oldZone.entries_count !== newZone.entries_count) {
                changes.push({
                    type: 'zone_entries_change',
                    zone: zoneName,
                    old_count: oldZone.entries_count,
                    new_count: newZone.entries_count,
                    difference: newZone.entries_count - oldZone.entries_count,
                    timestamp: newSnapshot.timestamp
                });
            }

            if (Math.abs(oldZone.temperature - newZone.temperature) > 0.1) {
                changes.push({
                    type: 'temperature_change',
                    zone: zoneName,
                    old_temp: oldZone.temperature,
                    new_temp: newZone.temperature,
                    timestamp: newSnapshot.timestamp
                });
            }

            // Nouvelles entrées
            const newEntries = newZone.entries.filter(entry => 
                !oldZone.entries.some(oldEntry => oldEntry.id === entry.id)
            );

            for (const entry of newEntries) {
                changes.push({
                    type: 'new_entry',
                    zone: zoneName,
                    entry: {
                        id: entry.id,
                        type: entry.type || 'unknown',
                        category: entry.category || 'general',
                        importance: entry.importance || 0,
                        content_preview: (entry.content || '').substring(0, 100) + '...'
                    },
                    timestamp: newSnapshot.timestamp
                });
            }
        }

        return changes;
    }

    /**
     * Met à jour les statistiques
     */
    updateStats(snapshot) {
        this.memoryStats = {
            total_entries: snapshot.total_entries,
            zones: snapshot.zones,
            qi_level: snapshot.qi_level,
            temperature_avg: this.calculateAverageTemperature(snapshot.zones),
            last_update: snapshot.timestamp,
            monitoring_active: this.isMonitoring
        };

        // Garder les 50 dernières activités
        if (this.memoryStats.recent_activity.length > 50) {
            this.memoryStats.recent_activity = this.memoryStats.recent_activity.slice(-50);
        }
    }

    /**
     * Calcule la température moyenne
     */
    calculateAverageTemperature(zones) {
        const temps = Object.values(zones).map(zone => zone.temperature);
        return temps.reduce((sum, temp) => sum + temp, 0) / temps.length;
    }

    /**
     * Obtient les statistiques actuelles
     */
    getStats() {
        return this.memoryStats;
    }

    /**
     * Obtient les détails d'une zone spécifique
     */
    getZoneDetails(zoneName) {
        if (!this.lastSnapshot || !this.lastSnapshot.zones[zoneName]) {
            return null;
        }

        const zone = this.lastSnapshot.zones[zoneName];
        return {
            name: zoneName,
            temperature: zone.temperature,
            capacity: zone.capacity,
            entries_count: zone.entries_count,
            usage_percent: zone.usage_percent,
            recent_entries: zone.entries.slice(-10).map(entry => ({
                id: entry.id,
                type: entry.type || 'unknown',
                category: entry.category || 'general',
                importance: entry.importance || 0,
                timestamp: entry.timestamp,
                content_preview: (entry.content || '').substring(0, 150) + '...'
            }))
        };
    }

    /**
     * Recherche dans la mémoire
     */
    searchMemory(query, options = {}) {
        if (!this.lastSnapshot) return [];

        const results = [];
        const limit = options.limit || 10;
        const zone = options.zone || null;

        const zonesToSearch = zone ? [zone] : Object.keys(this.lastSnapshot.zones);

        for (const zoneName of zonesToSearch) {
            const zoneData = this.lastSnapshot.zones[zoneName];
            if (!zoneData.entries) continue;

            for (const entry of zoneData.entries) {
                if (entry.content && entry.content.toLowerCase().includes(query.toLowerCase())) {
                    results.push({
                        zone: zoneName,
                        entry: {
                            id: entry.id,
                            type: entry.type || 'unknown',
                            category: entry.category || 'general',
                            importance: entry.importance || 0,
                            timestamp: entry.timestamp,
                            content: entry.content
                        },
                        relevance: this.calculateRelevance(entry.content, query)
                    });
                }
            }
        }

        // Trier par pertinence
        results.sort((a, b) => b.relevance - a.relevance);
        return results.slice(0, limit);
    }

    /**
     * Calcule la pertinence d'un résultat
     */
    calculateRelevance(content, query) {
        const contentLower = content.toLowerCase();
        const queryLower = query.toLowerCase();
        
        let score = 0;
        
        // Correspondance exacte
        if (contentLower.includes(queryLower)) {
            score += 10;
        }
        
        // Mots individuels
        const queryWords = queryLower.split(' ');
        for (const word of queryWords) {
            if (contentLower.includes(word)) {
                score += 2;
            }
        }
        
        return score;
    }
}

// Export pour utilisation dans d'autres modules
module.exports = ThermalMemoryMonitor;

// Lancement direct si exécuté
if (require.main === module) {
    const monitor = new ThermalMemoryMonitor();
    
    monitor.on('memory_update', (stats) => {
        console.log('🧠 Mise à jour mémoire:', {
            total_entries: stats.total_entries,
            qi_level: stats.qi_level,
            temp_avg: stats.temperature_avg.toFixed(1)
        });
    });
    
    monitor.on('memory_changed', (changes) => {
        console.log('🔄 Changements détectés:', changes.length);
        for (const change of changes) {
            console.log(`  • ${change.type}: ${JSON.stringify(change)}`);
        }
    });
    
    monitor.on('memory_error', (error) => {
        console.error('❌ Erreur mémoire:', error);
    });
    
    monitor.startMonitoring(2000); // Surveillance toutes les 2 secondes
    
    // Arrêt propre
    process.on('SIGINT', () => {
        console.log('\n🛑 Arrêt du moniteur...');
        monitor.stopMonitoring();
        process.exit(0);
    });
}
