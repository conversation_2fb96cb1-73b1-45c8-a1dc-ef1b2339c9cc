#!/usr/bin/env node

/**
 * 🧪 TEST D'INTÉGRATION DEEPSEEK R1 8B + MÉMOIRE THERMIQUE PYTHON
 * 
 * Test de l'agent DeepSeek R1 8B avec la mémoire thermique
 * récupérée de l'agent Python
 */

const DeepSeekR1IntegratedAgent = require('./deepseek-r1-agent-integrated');

async function testIntegration() {
    console.log('🧪 === TEST D\'INTÉGRATION DEEPSEEK R1 8B + MÉMOIRE THERMIQUE ===\n');
    
    try {
        // 1. Créer l'agent
        console.log('1️⃣ Création de l\'agent DeepSeek R1 8B...');
        const agent = new DeepSeekR1IntegratedAgent({
            debug: true
        });
        
        // 2. Initialiser l'agent
        console.log('2️⃣ Initialisation de l\'agent...');
        const initResult = await agent.initialize();
        
        if (!initResult) {
            throw new Error('Échec de l\'initialisation');
        }
        
        console.log('✅ Agent initialisé avec succès\n');
        
        // 3. Tester le chargement de la mémoire thermique
        console.log('3️⃣ Test du chargement de la mémoire thermique...');
        
        if (!agent.state.memoryLoaded) {
            throw new Error('Mémoire thermique non chargée');
        }
        
        const stats = agent.analyzeMemoryStats();
        console.log(`📊 Statistiques mémoire:`);
        console.log(`   - Entrées totales: ${stats.totalEntries}`);
        console.log(`   - Zones: ${stats.zones}`);
        console.log(`   - QI: ${stats.qi}`);
        console.log(`   - Température: ${stats.temperature}°`);
        console.log(`   - Version: ${stats.version}`);
        console.log(`   - Format: ${stats.format}`);
        console.log('✅ Mémoire thermique chargée avec succès\n');
        
        // 4. Tester la recherche dans la mémoire
        console.log('4️⃣ Test de recherche dans la mémoire...');
        
        const searchResults = agent.searchThermalMemory('mémoire thermique', { limit: 3 });
        console.log(`🔍 Résultats de recherche (${searchResults.length} trouvés):`);
        
        for (const result of searchResults) {
            console.log(`   - ${result.id}: ${result.content.substring(0, 80)}...`);
            console.log(`     Importance: ${result.importance}, Pertinence: ${result.relevance.toFixed(2)}`);
        }
        console.log('✅ Recherche mémoire fonctionnelle\n');
        
        // 5. Tester l'intégration mémoire-réflexion
        console.log('5️⃣ Test de l\'intégration mémoire-réflexion...');
        
        const testInput = "Comment fonctionne la mémoire thermique ?";
        console.log(`💭 Question test: "${testInput}"`);
        
        // Test de réflexion avec mémoire
        const memories = agent.searchThermalMemory(testInput, { limit: 2 });
        const reflection = await agent.performReflection(testInput, memories, {});
        
        console.log(`🧠 Réflexion générée:`);
        console.log(`   - Type d'entrée: ${reflection.input_analysis.type}`);
        console.log(`   - Complexité: ${reflection.input_analysis.complexity.toFixed(2)}`);
        console.log(`   - Mots-clés: ${reflection.input_analysis.keywords.join(', ')}`);
        console.log(`   - Contexte mémoire: ${reflection.memory_context.length} souvenirs`);
        console.log(`   - Chaîne de raisonnement: ${reflection.reasoning_chain.length} étapes`);
        console.log('✅ Intégration mémoire-réflexion fonctionnelle\n');
        
        // 6. Test des accélérateurs
        console.log('6️⃣ Test des accélérateurs Kyber...');
        
        if (agent.thermalMemoryData.accelerators) {
            const accelerators = Object.values(agent.thermalMemoryData.accelerators);
            console.log(`⚡ Accélérateurs actifs: ${accelerators.length}`);
            
            for (const acc of accelerators) {
                console.log(`   - ${acc.type}: boost ${acc.boost_factor}x (${acc.priority})`);
            }
            console.log('✅ Accélérateurs Kyber opérationnels\n');
        }
        
        // 7. Test du système neuronal
        console.log('7️⃣ Test du système neuronal...');
        
        if (agent.thermalMemoryData.neural_system) {
            const neural = agent.thermalMemoryData.neural_system;
            console.log(`🧠 Système neuronal:`);
            console.log(`   - Neurones totaux: ${neural.total_neurons.toLocaleString()}`);
            console.log(`   - QI: ${neural.qi_level}`);
            console.log(`   - Neurones actifs: ${neural.active_neurons?.toLocaleString() || 'N/A'}`);
            console.log(`   - Intégration Python: ${neural.python_integration ? '✅' : '❌'}`);
            console.log(`   - Compatibilité DeepSeek: ${neural.deepseek_compatibility ? '✅' : '❌'}`);
            console.log('✅ Système neuronal opérationnel\n');
        }
        
        // 8. Test de protection
        console.log('8️⃣ Test du système de protection...');
        
        if (agent.thermalMemoryData.protection_system) {
            const protection = agent.thermalMemoryData.protection_system;
            console.log(`🛡️ Système de protection:`);
            console.log(`   - Anti-absorption: ${protection.anti_absorption ? '✅' : '❌'}`);
            console.log(`   - Persistance forcée: ${protection.force_persistence ? '✅' : '❌'}`);
            console.log(`   - Niveau: ${protection.protection_level}`);
            console.log(`   - Mémoire Python préservée: ${protection.python_memory_preserved ? '✅' : '❌'}`);
            console.log('✅ Système de protection actif\n');
        }
        
        // 9. Résumé final
        console.log('🎉 === RÉSUMÉ DU TEST D\'INTÉGRATION ===');
        console.log('✅ Agent DeepSeek R1 8B initialisé');
        console.log('✅ Mémoire thermique Python chargée');
        console.log('✅ Recherche mémoire fonctionnelle');
        console.log('✅ Intégration mémoire-réflexion opérationnelle');
        console.log('✅ Accélérateurs Kyber actifs');
        console.log('✅ Système neuronal opérationnel');
        console.log('✅ Protection système active');
        console.log('\n🚀 INTÉGRATION RÉUSSIE ! L\'agent DeepSeek R1 8B est maintenant');
        console.log('   équipé de la mémoire thermique sophistiquée de l\'agent Python');
        console.log('   avec système de réflexion intégré et connexion directe.\n');
        
        return true;
        
    } catch (error) {
        console.error(`❌ Erreur lors du test: ${error.message}`);
        console.error(error.stack);
        return false;
    }
}

// Exécuter le test
if (require.main === module) {
    testIntegration().then(success => {
        process.exit(success ? 0 : 1);
    });
}

module.exports = { testIntegration };
