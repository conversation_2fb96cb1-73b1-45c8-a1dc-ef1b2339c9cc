#!/usr/bin/env node

/**
 * 🔧 CORRECTION MÉMOIRE + TEST MPC COMPLET
 * 
 * 1. Corrige la mémoire thermique avec les formations MPC
 * 2. Teste le mode MPC en direct
 * 3. Vérifie l'affichage du QI
 */

const fs = require('fs');
const io = require('socket.io-client');

class MemoryMPCFixer {
    constructor() {
        this.memoryFile = './thermal_memory_persistent.json';
        this.socket = null;
    }

    /**
     * Lance la correction complète
     */
    async runCompleteFix() {
        console.log('🔧 === CORRECTION MÉMOIRE + TEST MPC ===\n');

        try {
            // 1. Corriger la mémoire
            await this.fixMemoryStructure();

            // 2. Ajouter les formations MPC
            await this.addMPCFormations();

            // 3. Sauvegarder
            await this.saveMemory();

            // 4. Tester la connexion à l'agent
            await this.testAgentConnection();

            // 5. Tester le mode MPC
            await this.testMPCMode();

            console.log('\n🎉 === CORRECTION ET TESTS TERMINÉS ===');

        } catch (error) {
            console.error(`❌ Erreur: ${error.message}`);
        }
    }

    /**
     * Corrige la structure de la mémoire
     */
    async fixMemoryStructure() {
        console.log('🔧 Correction de la structure mémoire...');

        // Charger la mémoire existante
        this.memoryData = JSON.parse(fs.readFileSync(this.memoryFile, 'utf8'));

        // Mettre à jour le QI
        this.memoryData.neural_system.qi_level = 235;
        this.memoryData.neural_system.qi_components = {
            base_agent_deepseek_r1: 120,
            thermal_memory_system: 80,
            cognitive_boost_scientific: 35,
            mpc_formations_bonus: 25,
            total_calculated: 260
        };

        console.log('✅ Structure mémoire corrigée');
        console.log(`🧠 QI mis à jour: ${this.memoryData.neural_system.qi_level}`);
    }

    /**
     * Ajoute les formations MPC dans la zone procédurale existante
     */
    async addMPCFormations() {
        console.log('🎮 Ajout des formations MPC...');

        const proceduralZone = this.memoryData.thermal_zones.zone3_procedural;
        
        // Formations MPC expertes
        const mpcFormations = [
            {
                id: `mpc_desktop_control_${Date.now()}`,
                content: "FORMATION MPC BUREAU COMPLET : Contrôle total macOS avec commandes : 'open -a App', 'screencapture', 'osascript', System Events, gestion fichiers, processus, réseau. EXPERT BUREAU MACOS.",
                importance: 0.98,
                timestamp: Date.now() / 1000,
                synaptic_strength: 0.98,
                temperature: 37.2,
                zone: "zone3_procedural",
                source: "mpc_formation",
                type: "mpc_desktop_control"
            },
            {
                id: `mpc_coding_expert_${Date.now() + 1}`,
                content: "FORMATION CODAGE EXPERT : JavaScript/Node.js, Python 3.x, développement IA, systèmes cognitifs, APIs, async/await, modules, debugging, testing. EXPERT DÉVELOPPEMENT COMPLET.",
                importance: 0.95,
                timestamp: Date.now() / 1000,
                synaptic_strength: 0.95,
                temperature: 37.3,
                zone: "zone3_procedural",
                source: "mpc_formation",
                type: "mpc_coding_expert"
            },
            {
                id: `mpc_system_admin_${Date.now() + 2}`,
                content: "FORMATION ADMIN SYSTÈME : Unix/Linux/macOS, bash/zsh, processus, réseau, permissions, cron, logs, package managers, monitoring. EXPERT SYSADMIN.",
                importance: 0.90,
                timestamp: Date.now() / 1000,
                synaptic_strength: 0.90,
                temperature: 37.1,
                zone: "zone3_procedural",
                source: "mpc_formation",
                type: "mpc_system_admin"
            },
            {
                id: `mpc_perplexity_search_${Date.now() + 3}`,
                content: "FORMATION RECHERCHE PERPLEXITY : Recherches Internet ultra-performantes, requêtes optimisées, sources fiables, synthèse rapide, fact-checking, veille technologique. EXPERT RECHERCHE WEB.",
                importance: 0.92,
                timestamp: Date.now() / 1000,
                synaptic_strength: 0.92,
                temperature: 37.4,
                zone: "zone3_procedural",
                source: "mpc_formation",
                type: "mpc_perplexity_search"
            }
        ];

        // Ajouter les formations
        for (const formation of mpcFormations) {
            proceduralZone.entries.push(formation);
            console.log(`✅ Formation ajoutée: ${formation.type}`);
        }

        // Mettre à jour la température de la zone
        proceduralZone.temperature = 37.3;

        console.log(`✅ ${mpcFormations.length} formations MPC ajoutées`);
    }

    /**
     * Sauvegarde la mémoire
     */
    async saveMemory() {
        console.log('💾 Sauvegarde de la mémoire...');

        this.memoryData.last_modified = new Date().toISOString();
        fs.writeFileSync(this.memoryFile, JSON.stringify(this.memoryData, null, 2));

        console.log('✅ Mémoire sauvegardée');
    }

    /**
     * Teste la connexion à l'agent
     */
    async testAgentConnection() {
        console.log('🔌 Test de connexion à l\'agent...');

        return new Promise((resolve) => {
            this.socket = io('http://localhost:3000');

            this.socket.on('connect', () => {
                console.log('✅ Connexion à l\'agent réussie');
                resolve();
            });

            this.socket.on('connect_error', (error) => {
                console.log('⚠️ Erreur connexion, agent peut-être non démarré');
                resolve();
            });

            setTimeout(() => {
                console.log('⏱️ Timeout connexion');
                resolve();
            }, 3000);
        });
    }

    /**
     * Teste le mode MPC
     */
    async testMPCMode() {
        console.log('🎮 === TEST MODE MPC ===');

        if (!this.socket || !this.socket.connected) {
            console.log('⚠️ Pas de connexion à l\'agent, test MPC manuel requis');
            this.printMPCTestInstructions();
            return;
        }

        console.log('🧪 Test des capacités MPC...');

        // Test 1: Demander les capacités MPC
        await this.sendTestMessage("Quelles sont tes capacités MPC pour contrôler le bureau macOS ?");

        // Test 2: Demander un test simple
        await this.sendTestMessage("Peux-tu me montrer comment tu peux lister les fichiers du bureau ?");

        // Test 3: Test de recherche Perplexity
        await this.sendTestMessage("Utilise tes capacités de recherche pour me trouver les dernières nouvelles sur l'IA");
    }

    /**
     * Envoie un message de test
     */
    async sendTestMessage(message) {
        return new Promise((resolve) => {
            console.log(`📤 Test: "${message}"`);

            this.socket.emit('user_message', {
                message: message,
                timestamp: Date.now()
            });

            this.socket.once('agent_response', (response) => {
                console.log(`📥 Réponse: ${response.message.substring(0, 100)}...`);
                resolve();
            });

            setTimeout(() => {
                console.log('⏱️ Timeout réponse');
                resolve();
            }, 10000);
        });
    }

    /**
     * Instructions pour test MPC manuel
     */
    printMPCTestInstructions() {
        console.log('\n📋 === INSTRUCTIONS TEST MPC MANUEL ===');
        console.log('1. Ouvrez l\'interface chat: http://localhost:3000');
        console.log('2. Testez ces commandes:');
        console.log('   • "Active le mode MPC et liste les fichiers du bureau"');
        console.log('   • "Utilise MPC pour ouvrir le Finder"');
        console.log('   • "Prends une capture d\'écran avec MPC"');
        console.log('   • "Montre-moi tes formations MPC"');
        console.log('3. Vérifiez que le QI s\'affiche correctement dans le moniteur');
        console.log('4. Vérifiez que les formations MPC apparaissent dans la mémoire');
    }
}

// Lancer la correction si exécuté directement
if (require.main === module) {
    const fixer = new MemoryMPCFixer();
    fixer.runCompleteFix();
}

module.exports = MemoryMPCFixer;
